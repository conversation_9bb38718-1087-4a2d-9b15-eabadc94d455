%========= Abstract in English =========%
%                                       %
% English abstract page                 %
%=======================================%

\chapter*{Abstract}
\addcontentsline{toc}{chapter}{Abstract}
\thispagestyle{empty}

\textbf{Context:} In a constantly evolving IT environment, optimal server resource management constitutes a major challenge for enterprises. \companyName, an HR solutions publisher, faces monitoring and capacity analysis challenges for its on-premise infrastructure comprising over 50 servers.

\textbf{Problem Statement:} The absence of a centralized capacity management solution leads to difficulties in resource planning, proactive overload detection, and capacity report generation for technical and managerial teams.

\textbf{Objective:} This graduation project aims to design and develop \projectTitle, a modern web platform enabling automation of server capacity management, analysis, and reporting, while integrating intelligent alerting features and temporal comparisons.

\textbf{Methodology:} The adopted approach combines thorough business needs analysis, architectural design based on modern technologies (Angular 19, FastAPI, Oracle), and native integration with existing metrics collection infrastructure (Zabbix).

\textbf{Results:} At mid-term, the \projectTitle\ solution presents an 85\% completion rate with full implementation of the Angular 19 frontend, modular FastAPI backend, and Zabbix integration. Operational features include an interactive dashboard, sophisticated usage comparison system, and configurable alert module.

\textbf{Impact:} The platform already demonstrates its ability to centralize capacity management, automate capacity reporting, and improve decision-making regarding infrastructure evolution, thus meeting the initial project objectives.

\textbf{Keywords:} Capacity Management, Server Capacity, Angular 19, FastAPI, Zabbix, Dashboard, Alerts, Reporting, Infrastructure, DevOps.

\clearpage
