# Documentation - Récupération des Données "Liste des Serveurs"

## Vue d'ensemble

La page "Liste des Serveurs" affiche tous les serveurs du système CloudCapacity avec leurs informations techniques et clients. Cette documentation explique comment les données sont récupérées depuis plusieurs sources et combinées pour l'affichage.

## Sources de Données

### Sources Principales
- **Zabbix API** - Métriques techniques des serveurs (CPU, RAM, Swap)
- **Base de données CloudCapacity** - Stockage persistant des informations serveurs
- **Table CLIENT_CONF** - Configurations et assignations clients

### Bibliothèques Utilisées

#### Frontend (Angular)
- **Angular HttpClient** - Requêtes HTTP vers l'API backend
- **RxJS** - Gestion des flux de données asynchrones
- **Angular Material** - Interface utilisateur (tableaux, filtres)

#### Backend (Python/FastAPI)
- **pyzabbix** - Connexion et requêtes vers l'API Zabbix
- **SQLAlchemy** - Accès aux données de la base Oracle
- **FastAPI** - Endpoints API REST
- **ThreadPoolExecutor** - Traitement parallèle des serveurs

## Flux de Récupération des Données

### 1. Chargement Initial
- L'utilisateur accède à la page "Liste des Serveurs"
- Le composant `server-list.component.ts` appelle `loadServers()`
- Requête HTTP GET vers `/capacity/get-servers`

### 2. Endpoint Backend Principal
**Endpoint** : `GET /capacity/get-servers`
- Récupère tous les serveurs depuis la table SERVER
- Joint avec CLIENT_CONF pour obtenir les informations plateforme
- Retourne les données combinées au frontend

### 3. Traitement des Données Backend
```
Étapes de traitement :
1. Requête SQL vers table SERVER
2. Jointure avec CLIENT_CONF sur client_config_label
3. Enrichissement avec informations plateforme
4. Formatage des données pour le frontend
5. Retour JSON avec liste complète des serveurs
```

### 4. Affichage Frontend
- Réception des données serveurs
- Mise à jour des filtres disponibles (clients, types, statuts)
- Application des filtres actifs
- Affichage dans le tableau avec pagination

## Synchronisation avec Zabbix

### Synchronisation Manuelle
**Bouton "Actualiser"** déclenche :
- Appel à `/capacity/return-and-sync-hostnames-and-metrics`
- Récupération des hostnames depuis Zabbix (filtrés "frsops*")
- Traitement parallèle des métriques serveurs
- Mise à jour de la base de données
- Rechargement automatique de la liste

### Métriques Récupérées depuis Zabbix
- **system.cpu.num** - Nombre de CPUs
- **vm.memory.size[total]** - Mémoire RAM totale
- **system.swap.size[,total]** - Espace swap total

### Traitement Parallèle
- Utilisation de ThreadPoolExecutor (20 workers max)
- Traitement simultané de plusieurs serveurs
- Optimisation des performances pour gros volumes

## Mapping Client-Serveur

### Synchronisation des Informations Clients
**Bouton "Sync Client Info"** déclenche :
- Appel à `/capacity/sync-client-information`
- Analyse des configurations CLIENT_CONF actives
- Extraction des hostnames depuis les champs serveurs
- Mapping automatique hostname → client

### Logique de Mapping
1. **Extraction des hostnames** depuis les champs :
   - webserver, webserverint, treatmentserver
   - dbserver1, dbserver2
   - qserver1 à qserver6

2. **Résolution des conflits** par priorité :
   - Type plateforme (MPS > ESP)
   - Type environnement (PROD > autres)
   - Nom client (ordre alphabétique)

3. **Assignation automatique** :
   - client_name, client_environment
   - client_config_label, server_role
   - platform (Linux/Windows)

## Structure des Données

### Modèle Serveur (Frontend)
- **Informations techniques** : hostname, total_cpus, total_ram_gb, total_swap_gb
- **Informations clients** : client_name, client_environment, platform
- **Métadonnées** : server_type, timestamp_update, status

### Modèle Base de Données
- **Table SERVER** - Données techniques persistantes
- **Table CLIENT_CONF** - Configurations clients actives
- **Jointures** - Enrichissement des données serveurs

## Fonctionnalités de Filtrage

### Filtres Disponibles
- **Client** - Filtrage par nom de client
- **Type de serveur** - Virtual Machine, Physical Server
- **Statut** - Stable, En Alerte, Corrigé
- **Catégorie** - Web, Applicatif, Base de données, Infrastructure
- **Recherche textuelle** - Sur hostname et autres champs

### Mise à Jour Dynamique
- Recalcul automatique des options de filtre
- Mise à jour des totaux (CPUs, mémoire)
- Pagination adaptative selon les résultats

## Gestion des Erreurs

### Frontend
- Indicateurs de chargement avec barre de progression
- Messages d'erreur traduits
- Retry automatique en cas d'échec réseau

### Backend
- Validation des connexions Zabbix
- Gestion des timeouts et erreurs réseau
- Logging détaillé pour le débogage
- Transactions sécurisées pour la base de données

## Performance et Optimisation

### Optimisations Backend
- Traitement parallèle des requêtes Zabbix
- Mise en cache des connexions
- Requêtes SQL optimisées avec jointures

### Optimisations Frontend
- Pagination des résultats (10 éléments par page)
- Filtrage côté client pour la réactivité
- Observables RxJS pour la gestion des états

## Endpoints Principaux

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| GET | `/capacity/get-servers` | Récupération liste complète des serveurs |
| POST | `/capacity/return-and-sync-hostnames-and-metrics` | Synchronisation avec Zabbix |
| POST | `/capacity/sync-client-information` | Mise à jour assignations clients |

Cette architecture garantit une récupération efficace et une présentation cohérente des données serveurs avec synchronisation temps réel depuis Zabbix et mapping automatique des informations clients.
