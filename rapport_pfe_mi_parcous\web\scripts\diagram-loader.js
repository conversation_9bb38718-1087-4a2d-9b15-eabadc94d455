// Script pour charger et afficher les diagrammes Mermaid
document.addEventListener('DOMContentLoaded', function () {
    // Configuration Mermaid
    mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        themeVariables: {
            primaryColor: '#0066cc',
            primaryTextColor: '#333',
            primaryBorderColor: '#0066cc',
            lineColor: '#666',
            secondaryColor: '#f9f9f9',
            tertiaryColor: '#fff'
        },
        flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
        },
        sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true,
            rightAngles: false,
            showSequenceNumbers: false
        },
        er: {
            diagramPadding: 20,
            layoutDirection: 'TB',
            minEntityWidth: 100,
            minEntityHeight: 75,
            entityPadding: 15,
            stroke: '#333',
            fill: '#f9f9f9',
            fontSize: 12
        }
    });

    // Définition des diagrammes intégrés
    const diagramsContent = {
        'architecture-generale': `flowchart TD
    subgraph "🌐 Couche Présentation"
        direction TB
        A["🖥️ Client Web<br/>Angular 19"]
        A1["📊 Dashboard<br/>Component"]
        A2["📋 Server List<br/>Component"]
        A3["🔍 Server Detail<br/>Component"]
        A4["📈 Usage Comparison<br/>Component"]
        A5["🚨 Threshold Alerts<br/>Component"]
    end

    subgraph "🔗 Couche API Gateway"
        direction TB
        B["⚡ API Gateway<br/>Nginx Reverse Proxy"]
        B1["🔐 Authentication<br/>& Authorization"]
        B2["🛡️ CORS & Security<br/>Headers"]
    end

    subgraph "⚙️ Couche Logique Métier"
        direction TB
        C["🐍 Backend FastAPI<br/>Python 3.11"]
        C1["📋 Server Info<br/>Router"]
        C2["📊 Server Usage<br/>Router"]
        C3["🔄 Comparison<br/>Router"]
        C4["🚨 Threshold<br/>Router"]
        C5["📄 Reporting<br/>Router"]
    end

    subgraph "🔌 Couche Intégration"
        direction TB
        E["📊 API Zabbix<br/>PyZabbix Client"]
        E1["🖥️ Zabbix Monitoring<br/>Server"]
        E2["📈 Collecte Métriques<br/>Temps Réel"]
        F["📄 Module Reporting<br/>PDF/Excel"]
        F1["📑 Génération PDF<br/>ReportLab"]
        F2["📊 Export Excel<br/>OpenPyXL"]
    end

    subgraph "🗄️ Couche Données"
        direction TB
        D["🏛️ Base Oracle<br/>Database 19c"]
        D1["🖥️ Table Serveurs<br/>Métadonnées"]
        D2["📊 Table Métriques<br/>Time Series"]
        D3["🚨 Table Alertes<br/>Notifications"]
        D4["⚙️ Table Config<br/>Paramètres"]
    end

    %% Flux vertical principal
    A ==> B
    B ==> C
    C ==> E
    C ==> F
    C ==> D

    %% Relations internes Frontend
    A -.-> A1
    A -.-> A2
    A -.-> A3
    A -.-> A4
    A -.-> A5

    %% Relations internes API Gateway
    B -.-> B1
    B -.-> B2

    %% Relations internes Backend
    C -.-> C1
    C -.-> C2
    C -.-> C3
    C -.-> C4
    C -.-> C5

    %% Relations internes Zabbix
    E -.-> E1
    E -.-> E2

    %% Relations internes Reporting
    F -.-> F1
    F -.-> F2

    %% Relations internes Base de données
    D -.-> D1
    D -.-> D2
    D -.-> D3
    D -.-> D4

    %% Styles avec couleurs modernes
    style A fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    style C fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style E fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    style F fill:#fce4ec,stroke:#c2185b,stroke-width:3px
    style D fill:#ffebee,stroke:#d32f2f,stroke-width:3px`,

        'cas-utilisation': `flowchart TD
    subgraph Acteurs["👥 Acteurs du Système"]
        direction TB
        Admin["🔧 Administrateur<br/>Système"]
        User["👤 Utilisateur<br/>Standard"]
        System["🖥️ Système<br/>Zabbix"]
    end

    subgraph UC_Gestion["📋 Gestion des Serveurs"]
        direction TB
        UC1["📋 Consulter liste<br/>des serveurs"]
        UC2["🔍 Voir détails<br/>d'un serveur"]
        UC3["🔎 Filtrer et<br/>rechercher"]
        UC4["🔄 Synchroniser<br/>données Zabbix"]
    end

    subgraph UC_Monitoring["📊 Monitoring & Analyse"]
        direction TB
        UC5["📊 Consulter<br/>dashboard"]
        UC6["📈 Visualiser<br/>métriques"]
        UC7["🔄 Comparer usage<br/>serveurs/périodes"]
        UC8["📉 Analyser<br/>tendances"]
    end

    subgraph UC_Alertes["🚨 Système d'Alertes"]
        direction TB
        UC9["⚙️ Configurer<br/>seuils d'alerte"]
        UC10["🚨 Consulter<br/>alertes actives"]
        UC11["📧 Gérer<br/>notifications"]
        UC12["📜 Historique<br/>des alertes"]
    end

    subgraph UC_Reporting["📄 Reporting & Export"]
        direction TB
        UC13["📊 Générer<br/>rapports"]
        UC14["📑 Exporter<br/>PDF/Excel"]
        UC15["⏰ Programmer<br/>rapports"]
        UC16["🎨 Personnaliser<br/>rapports"]
    end

    %% Relations Administrateur (accès complet)
    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC4
    Admin --> UC5
    Admin --> UC6
    Admin --> UC7
    Admin --> UC8
    Admin --> UC9
    Admin --> UC10
    Admin --> UC11
    Admin --> UC12
    Admin --> UC13
    Admin --> UC14
    Admin --> UC15
    Admin --> UC16

    %% Relations Utilisateur (accès limité)
    User --> UC1
    User --> UC2
    User --> UC3
    User --> UC5
    User --> UC6
    User --> UC7
    User --> UC8
    User --> UC10
    User --> UC13
    User --> UC14

    %% Relations Système (automatiques)
    System --> UC4
    System --> UC6
    System --> UC10

    %% Styles avec couleurs distinctes
    style Admin fill:#ffcdd2,stroke:#d32f2f,stroke-width:3px
    style User fill:#c8e6c9,stroke:#388e3c,stroke-width:3px
    style System fill:#bbdefb,stroke:#1976d2,stroke-width:3px

    %% Styles pour les groupes de cas d'utilisation
    style UC_Gestion fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style UC_Monitoring fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
    style UC_Alertes fill:#fff3e0,stroke:#ff9800,stroke-width:2px
    style UC_Reporting fill:#e3f2fd,stroke:#2196f3,stroke-width:2px`,

        'sequence-collecte': `sequenceDiagram
    participant F as 🌐 Frontend Angular
    participant B as ⚙️ Backend FastAPI
    participant Z as 📊 API Zabbix
    participant D as 🗄️ Base Oracle

    Note over F,D: 📋 Processus de collecte des métriques serveur

    %% Étape 1: Récupération liste serveurs
    F->>+B: GET /capacity/get-servers
    Note right of F: Demande liste serveurs

    B->>+D: SELECT * FROM servers
    D-->>-B: Liste des serveurs
    B-->>-F: Serveurs avec métadonnées

    %% Étape 2: Demande métriques spécifiques
    F->>+B: POST /capacity/server-usage-per-month
    Note right of F: hostname, year, month

    %% Étape 3: Connexion Zabbix
    B->>+Z: Authentification API
    Z-->>-B: Session token

    %% Étape 4: Récupération host_id
    B->>+Z: get_host_id(hostname)
    Z-->>-B: host_id

    %% Étape 5: Récupération item_ids
    B->>+Z: get_item_ids(host_id)
    Note right of Z: Items CPU, Memory, Swap
    Z-->>-B: item_ids array

    %% Étape 6: Collecte données historiques
    B->>+Z: trend.get(itemids, time_from, time_till)
    Note right of Z: Données horaires du mois
    Z-->>-B: Raw trend data

    %% Étape 7: Traitement des données
    B->>B: 🔄 process_trend_data()
    Note right of B: Transformation et calculs statistiques

    %% Étape 8: Retour données structurées
    B-->>-F: ZabbixData structurées
    Note left of B: cpu_usage[], memory_usage[], swap_utilization[]

    %% Étape 9: Mise à jour interface
    F->>F: 📈 updateChartData()
    Note right of F: Mise à jour Chart.js

    F->>F: 🖥️ Affichage dashboard
    Note right of F: Composants de visualisation`,

        'modele-donnees': `erDiagram
    Server {
        int id PK "Identifiant unique"
        string hostname UK "Nom du serveur"
        string server_type "VM, Physical, Container"
        int total_cpus "Nombre de CPUs"
        bigint total_ram_b "RAM totale en bytes"
        int total_ram_gb "RAM totale en GB"
        bigint total_swap_b "Swap total en bytes"
        int total_swap_gb "Swap total en GB"
        datetime timestamp_update "Dernière mise à jour"
        string status "online, offline, maintenance"
        string client_name "Nom du client"
        string client_environment "prod, test, dev"
        string server_role "web, db, app, cache"
        string platform "Linux, Windows"
        string envtype "Production, Test"
    }

    UsageDataPoint {
        int id PK "Identifiant unique"
        string hostname FK "Référence serveur"
        datetime timestamp "Horodatage mesure"
        float cpu_usage "Utilisation CPU en %"
        float memory_usage "Utilisation mémoire en %"
        float swap_usage "Utilisation swap en %"
        string metric_type "hourly, daily, monthly"
    }

    Alert {
        int id PK "Identifiant unique"
        string hostname FK "Référence serveur"
        string alert_type "cpu, memory, swap, prediction"
        float threshold_value "Seuil configuré"
        float current_value "Valeur actuelle"
        datetime timestamp "Moment de l'alerte"
        string status "active, resolved, acknowledged"
        string severity "low, medium, high, critical"
        text description "Description détaillée"
    }

    Threshold {
        int id PK "Identifiant unique"
        string threshold_type "cpu, memory, swap"
        float warning_value "Seuil d'avertissement"
        float critical_value "Seuil critique"
        boolean enabled "Actif ou non"
        datetime created_at "Date de création"
        datetime updated_at "Dernière modification"
    }

    ClientConf {
        int id PK "Identifiant unique"
        string client_name "Nom du client"
        string environment "Environnement"
        string config_label "Libellé configuration"
        json server_list "Liste des serveurs"
        datetime created_at "Date de création"
        datetime updated_at "Dernière modification"
    }

    MonthlyData {
        int id PK "Identifiant unique"
        string hostname FK "Référence serveur"
        int year "Année"
        int month "Mois 1-12"
        float avg_cpu_usage "CPU moyen"
        float max_cpu_usage "CPU maximum"
        float avg_memory_usage "Mémoire moyenne"
        float max_memory_usage "Mémoire maximum"
        float avg_swap_usage "Swap moyen"
        float max_swap_usage "Swap maximum"
        datetime calculated_at "Date de calcul"
    }

    %% Relations entre entités
    Server ||--o{ UsageDataPoint : "génère des métriques"
    Server ||--o{ Alert : "déclenche des alertes"
    Server ||--o{ MonthlyData : "agrège mensuellement"
    ClientConf ||--o{ Server : "contient des serveurs"
    Threshold ||--o{ Alert : "définit les seuils"`,

        'sequence-rapport': `sequenceDiagram
    participant U as 👤 Utilisateur
    participant UI as 🖥️ Interface Angular
    participant API as ⚙️ API Backend
    participant DB as 🗄️ Base Oracle
    participant R as 📄 Service Rapport
    participant E as 📧 Service Email

    Note over U,E: 📊 Processus de génération de rapport CloudCapacity

    %% Étape 1: Demande utilisateur
    U->>+UI: Demande génération rapport
    Note right of U: Sélection période et serveurs

    %% Étape 2: Appel API
    UI->>+API: POST /capacity/generate-capacity-report
    Note right of UI: servers[], period, format

    %% Étape 3: Récupération données
    API->>+DB: SELECT métriques historiques
    Note right of DB: Données sur période sélectionnée
    DB-->>-API: Données métriques structurées

    %% Étape 4: Génération rapport
    API->>+R: Génère rapport PDF/Excel
    Note right of R: Template + données + formatage
    R->>R: 📊 Création graphiques
    R->>R: 📋 Formatage tableaux
    R-->>-API: Fichier rapport généré

    %% Étape 5: Sauvegarde métadonnées
    API->>+DB: INSERT rapport metadata
    Note right of DB: Historique des rapports
    DB-->>-API: Confirmation sauvegarde

    %% Étape 6: Envoi par email (optionnel)
    alt Envoi par email demandé
        API->>+E: Envoie rapport par email
        Note right of E: Destinataires configurés
        E-->>-API: Confirmation envoi
    end

    %% Étape 7: Retour utilisateur
    API-->>-UI: status: success, report_id, download_url
    UI-->>-U: ✅ Rapport généré avec succès

    Note over U,E: 📁 Rapport disponible en téléchargement`,

        'sequence-prediction': `sequenceDiagram
    participant S as 🤖 Système Scheduler
    participant ML as 🧠 Module ML
    participant API as ⚙️ API Backend
    participant DB as 🗄️ Base Oracle
    participant A as 🚨 Service Alertes

    Note over S,A: 🔮 Processus d'analyse prédictive (Fonctionnalité planifiée)

    %% Étape 1: Déclenchement automatique
    S->>+API: Déclenche analyse prédictive
    Note right of S: Tâche programmée quotidienne

    %% Étape 2: Récupération données historiques
    API->>+DB: SELECT données historiques
    Note right of DB: 3 mois de métriques par serveur
    DB-->>-API: Données temporelles structurées

    %% Étape 3: Analyse par ML
    API->>+ML: Envoie données pour analyse
    Note right of ML: Algorithmes de prédiction

    ML->>ML: 📈 Calcul tendances CPU/Memory
    Note right of ML: Régression linéaire, moyennes mobiles

    ML->>ML: 🔮 Prédiction surcharges
    Note right of ML: Détection anomalies futures

    ML->>ML: 📊 Calcul scores de confiance
    Note right of ML: Probabilité de surcharge

    ML-->>-API: Résultats prédictions + confiance

    %% Étape 4: Sauvegarde prédictions
    API->>+DB: INSERT prédictions
    Note right of DB: Table predictions avec scores
    DB-->>-API: Confirmation sauvegarde

    %% Étape 5: Gestion des alertes
    alt 🚨 Risque de surcharge détecté
        API->>+A: Crée alerte prédictive
        Note right of A: Seuil de risque dépassé (>80%)

        A->>A: 👥 Détermine destinataires
        Note right of A: Admins + responsables serveur

        A->>A: 📧 Prépare notification
        Note right of A: Email + dashboard notification

        A-->>-API: Alerte créée et envoyée
    end

    %% Étape 6: Finalisation
    API-->>-S: ✅ Analyse terminée

    Note over S,A: 🎯 Prédictions disponibles dans dashboard<br/>⚠️ Fonctionnalité en développement`,

        'architecture-detaillee': `flowchart TD
    subgraph "🌐 Couche Présentation - Frontend"
        direction TB
        A1["🖥️ Angular 19 UI<br/>Composants Autonomes"]
        A2["🎨 Modern Dark Theme<br/>CSS Variables"]
        A3["📊 Chart.js Visualizations<br/>Graphiques Interactifs"]
        A4["📱 Responsive Design<br/>Mobile-First"]
    end

    subgraph "🔗 Couche API Gateway"
        direction TB
        B1["🌐 Nginx Reverse Proxy<br/>Load Balancer"]
        B2["🚪 API Gateway<br/>Routage Intelligent"]
        B3["🛡️ CORS & Security<br/>Headers & Auth"]
        B4["📊 Rate Limiting<br/>& Monitoring"]
    end

    subgraph "⚙️ Couche Services Backend - FastAPI"
        direction TB
        C1["📋 Server Info Router<br/>/capacity/get-servers"]
        C2["📊 Server Usage Router<br/>/capacity/server-usage"]
        C3["🔄 Comparison Router<br/>/capacity/compare"]
        C4["🚨 Threshold Router<br/>/capacity/threshold"]
        C5["📄 Reporting Router<br/>/capacity/reports"]
    end

    subgraph "🗄️ Couche Données - Oracle"
        direction TB
        D1["🏛️ Oracle Database 19c<br/>Production Ready"]
        D2["🔗 Connection Pool<br/>SQLAlchemy"]
        D3["📋 SQLModel ORM<br/>Type Safety"]
        D4["📊 Indexes & Optimization<br/>Performance"]
    end

    subgraph "🔌 Intégrations Externes"
        direction TB
        E1["📊 Zabbix API<br/>Monitoring Server"]
        E2["🐍 PyZabbix Client<br/>Python SDK"]
        E3["📈 Métriques Collector<br/>Real-time Data"]
        E4["⏰ Scheduler Tasks<br/>Background Jobs"]
    end

    subgraph "📊 Services Avancés"
        direction TB
        F1["📑 PDF Generator<br/>ReportLab"]
        F2["📊 Excel Exporter<br/>OpenPyXL"]
        F3["📧 Email Service<br/>SMTP Client"]
        F4["🔄 Background Tasks<br/>Celery/FastAPI"]
    end

    %% Flux principal vertical
    A1 ==> B1
    B1 ==> B2
    B2 ==> C1
    B2 ==> C2
    B2 ==> C3
    B2 ==> C4
    B2 ==> C5

    %% Relations internes Frontend
    A1 -.-> A2
    A1 -.-> A3
    A1 -.-> A4

    %% Relations internes API Gateway
    B1 -.-> B2
    B2 -.-> B3
    B2 -.-> B4

    %% Accès aux données
    C1 --> D1
    C2 --> D1
    C3 --> D1
    C4 --> D1
    C5 --> D1

    %% Relations internes Base de données
    D1 -.-> D2
    D2 -.-> D3
    D1 -.-> D4

    %% Intégrations externes
    C1 --> E1
    C2 --> E1
    E1 -.-> E2
    E2 -.-> E3
    E4 --> C2

    %% Services avancés
    C5 --> F1
    C5 --> F2
    C5 --> F3
    F4 --> C5

    %% Relations internes Services
    F1 -.-> F4
    F2 -.-> F4
    F3 -.-> F4

    %% Styles avec couleurs cohérentes
    style A1 fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style B2 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    style C1 fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style C2 fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style C3 fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style C4 fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style C5 fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style D1 fill:#ffebee,stroke:#d32f2f,stroke-width:3px
    style E1 fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    style F1 fill:#fce4ec,stroke:#c2185b,stroke-width:3px`,

        'modele-donnees-detaille': `erDiagram
    SERVER {
        int id PK
        string hostname UK "Nom unique du serveur"
        string server_type "Type: VM, Physical, Container"
        int total_cpus "Nombre de CPUs"
        bigint total_ram_b "RAM totale en bytes"
        int total_ram_gb "RAM totale en GB"
        bigint total_swap_b "Swap total en bytes"
        int total_swap_gb "Swap total en GB"
        datetime timestamp_update "Dernière mise à jour"
        string status "online, offline, maintenance"
        string client_name "Nom du client"
        string client_environment "prod, test, dev"
        string server_role "web, db, app, cache"
        string platform "Linux, Windows"
        string envtype "Production, Test"
    }

    USAGE_DATA_POINT {
        int id PK
        string hostname FK
        datetime timestamp "Horodatage de la mesure"
        float cpu_usage "Utilisation CPU en %"
        float memory_usage "Utilisation mémoire en %"
        float swap_usage "Utilisation swap en %"
        string metric_type "hourly, daily, monthly"
    }

    ALERT {
        int id PK
        string hostname FK
        string alert_type "cpu, memory, swap, prediction"
        float threshold_value "Seuil configuré"
        float current_value "Valeur actuelle"
        datetime timestamp "Moment de l'alerte"
        string status "active, resolved, acknowledged"
        string severity "low, medium, high, critical"
        text description "Description détaillée"
    }

    THRESHOLD {
        int id PK
        string threshold_type "cpu, memory, swap"
        float warning_value "Seuil d'avertissement"
        float critical_value "Seuil critique"
        boolean enabled "Actif ou non"
        datetime created_at "Date de création"
        datetime updated_at "Dernière modification"
    }

    MONTHLY_DATA {
        int id PK
        string hostname FK
        int year "Année"
        int month "Mois (1-12)"
        float avg_cpu_usage "CPU moyen"
        float max_cpu_usage "CPU maximum"
        float avg_memory_usage "Mémoire moyenne"
        float max_memory_usage "Mémoire maximum"
        float avg_swap_usage "Swap moyen"
        float max_swap_usage "Swap maximum"
        datetime calculated_at "Date de calcul"
    }

    REPORT {
        int id PK "Identifiant unique"
        string name "Nom du rapport"
        datetime generation_date "Date de génération"
        string format "PDF, Excel, JSON"
        string status "generating, completed, failed"
        json parameters "Paramètres du rapport"
        string file_path "Chemin du fichier"
        string created_by "Utilisateur créateur"
    }

    USER {
        int id PK "Identifiant unique"
        string username UK "Nom d'utilisateur"
        string email UK "Adresse email"
        string role "admin, user, viewer"
        datetime last_login "Dernière connexion"
        boolean active "Compte actif"
    }

    PREDICTION {
        int id PK "Identifiant unique"
        string hostname FK "Référence serveur"
        string metric_type "cpu, memory, swap"
        float predicted_value "Valeur prédite"
        datetime prediction_date "Date de prédiction"
        datetime target_date "Date cible"
        float confidence "Score de confiance 0-1"
        string algorithm "Algorithme utilisé"
    }

    %% Relations principales
    SERVER ||--o{ USAGE_DATA_POINT : "génère des métriques"
    SERVER ||--o{ ALERT : "déclenche des alertes"
    SERVER ||--o{ MONTHLY_DATA : "agrège mensuellement"
    SERVER ||--o{ PREDICTION : "prédit les surcharges"
    THRESHOLD ||--o{ ALERT : "définit les seuils"
    USER ||--o{ REPORT : "génère des rapports"
    REPORT ||--o{ USAGE_DATA_POINT : "inclut les données"
    MONTHLY_DATA ||--o{ USAGE_DATA_POINT : "agrège les points"`
    };

    // Fonction pour charger et afficher un diagramme
    function loadDiagram(diagramId, diagramContent) {
        try {
            const element = document.getElementById(diagramId);
            if (element) {
                element.textContent = diagramContent;
                mermaid.init(undefined, element);
            }
        } catch (error) {
            console.error(`Erreur lors du chargement du diagramme ${diagramId}:`, error);
            const element = document.getElementById(diagramId);
            if (element) {
                element.innerHTML = `<div class="alert alert-warning">Erreur lors du chargement du diagramme: ${diagramId}</div>`;
            }
        }
    }

    // Charger tous les diagrammes
    Object.keys(diagramsContent).forEach(diagramId => {
        loadDiagram(diagramId, diagramsContent[diagramId]);
    });

    // Fonction pour afficher/masquer les sections
    function toggleSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.style.display = section.style.display === 'none' ? 'block' : 'none';
        }
    }

    // Ajouter des gestionnaires d'événements pour les sections pliables
    document.querySelectorAll('.section-toggle').forEach(toggle => {
        toggle.addEventListener('click', function () {
            const targetId = this.getAttribute('data-target');
            toggleSection(targetId);
        });
    });

    // Fonction pour générer la table des matières automatiquement
    function generateTOC() {
        const toc = document.getElementById('auto-toc');
        if (!toc) return;

        const headings = document.querySelectorAll('h2, h3, h4');
        let tocHTML = '';

        headings.forEach((heading, index) => {
            const level = parseInt(heading.tagName.charAt(1));
            const text = heading.textContent;
            const id = `heading-${index}`;

            // Ajouter un ID au heading s'il n'en a pas
            if (!heading.id) {
                heading.id = id;
            }

            // Déterminer la classe CSS selon le niveau
            let cssClass = 'toc-item';
            if (level === 3) cssClass = 'toc-subsection';
            if (level === 4) cssClass = 'toc-subsubsection';

            tocHTML += `<div class="${cssClass}"><a href="#${heading.id}">${text}</a></div>`;
        });

        toc.innerHTML = tocHTML;
    }

    // Générer la table des matières si nécessaire
    generateTOC();

    // Fonction pour imprimer le rapport
    function printReport() {
        window.print();
    }

    // Ajouter un bouton d'impression si nécessaire
    const printButton = document.getElementById('print-button');
    if (printButton) {
        printButton.addEventListener('click', printReport);
    }

    // Fonction pour exporter en PDF (nécessite une bibliothèque comme jsPDF)
    function exportToPDF() {
        console.log('Export PDF non implémenté - utiliser la fonction d\'impression du navigateur');
        printReport();
    }

    // Ajouter des gestionnaires pour l'export
    const exportButton = document.getElementById('export-button');
    if (exportButton) {
        exportButton.addEventListener('click', exportToPDF);
    }

    console.log('Rapport PFE CloudCapacity chargé avec succès - Diagrammes intégrés');
});
