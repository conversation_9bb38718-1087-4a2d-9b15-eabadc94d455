<!DOCTYPE html>
<!-- saved from url=(0032)https://bmcdzfpu.genspark.space/ -->
<html lang="fr" data-kantu="1">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> des Charges PFE - CloudCapacity</title>
    <link rel="stylesheet" href="./Cahier des Charges PFE - CloudCapacity_files/tailwind.min.css">
    <script src="./Cahier des Charges PFE - CloudCapacity_files/mermaid.min.js.téléchargement"></script>
    <style>
        body {
            font-family: 'Latin Modern Roman', 'Times New Roman', serif;
            line-height: 1.6;
            color: #333;
            max-width: 210mm;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }

        .page {
            padding: 20px;
            margin-bottom: 20px;
        }

        h1,
        h2,
        h3,
        h4,
        h5 {
            font-family: 'Latin Modern Sans', Arial, sans-serif;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-weight: bold;
        }

        h1 {
            font-size: 2rem;
        }

        h2 {
            font-size: 1.5rem;
        }

        h3 {
            font-size: 1.25rem;
        }

        h4 {
            font-size: 1.1rem;
        }

        p,
        ul,
        ol {
            margin-bottom: 1rem;
        }

        .toc {
            margin: 2rem 0;
            padding: 1rem;
            background-color: #f9f9f9;
            border-left: 4px solid #0066cc;
        }

        .toc-item {
            margin-left: 1rem;
        }

        .toc-subsection {
            margin-left: 2rem;
        }

        .toc-subsubsection {
            margin-left: 3rem;
        }

        .latex-section {
            margin-top: 2rem;
        }

        .titlepage {
            height: 95vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .titlepage h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
        }

        .titlepage .subtitle {
            font-size: 1.8rem;
            margin-bottom: 4rem;
        }

        .titlepage .author {
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }

        .titlepage .date {
            font-size: 1.1rem;
            margin-top: 4rem;
        }

        .dedication,
        .acknowledgements {
            font-style: italic;
            text-align: center;
            margin: 4rem 0;
        }

        .abstract {
            margin: 2rem 0;
            padding: 1rem;
            font-style: italic;
            background-color: #f9f9f9;
        }

        .table-data {
            border-collapse: collapse;
            width: 100%;
            margin: 1rem 0;
        }

        .table-data th,
        .table-data td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .table-data th {
            background-color: #f2f2f2;
        }

        .mermaid {
            margin: 20px 0;
        }

        .diagram-container {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }

        .figure {
            margin: 20px 0;
            text-align: center;
        }

        .figure-caption {
            font-style: italic;
            text-align: center;
            margin-top: 10px;
        }

        .code {
            font-family: 'Courier New', Courier, monospace;
            background-color: #f6f8fa;
            padding: 10px;
            border-radius: 5px;
            border-left: 3px solid #0066cc;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
    <style>
        .genspark-badge-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #333;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 12px;
            cursor: pointer;
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .genspark-modal {
            display: none;
            position: fixed;
            bottom: 80px;
            right: 20px;
            z-index: 10000;
            justify-content: end;
        }

        .genspark-modal-content {
            background-color: white;
            border-radius: 8px;
            max-width: 450px;
            width: 100%;
            box-sizing: border-box;
            padding: 20px;
            position: relative;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .genspark-modal-content {
                max-width: 90%;
            }
        }

        .genspark-close {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 20px;
            cursor: pointer;
            background: none;
            border: none;
        }

        .genspark-title {
            margin-bottom: 8px;
            font-weight: normal;
            display: inline;
            font-size: 14px;
        }

        .genspark-report {
            color: #909499;
            text-decoration: underline;
            cursor: pointer;
            margin-bottom: 14px;
            display: inline;
        }

        .genspark-info {
            margin: 25px 0;
            color: #333;
            font-size: 14px;
        }

        .genspark-buttons {
            display: flex;
            gap: 10px;
        }

        .genspark-remove-btn {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            color: #333;
            padding: 4px 14px;
            border-radius: 8px;
            cursor: pointer;
            flex: 1;
            font-size: 14px;
            box-sizing: border-box;
        }

        .genspark-go-btn {
            background-color: #222;
            border: none;
            color: white;
            padding: 4px 14px;
            border-radius: 8px;
            cursor: pointer;
            flex: 1;
            font-size: 14px;
            box-sizing: border-box;
        }
    </style>
</head>

<body>
    <!-- Page de garde -->
    <div class="page titlepage">
        <h1>PROJET DE FIN D'ÉTUDES</h1>
        <div class="subtitle">CloudCapacity - Plateforme de Gestion Automatisée de la Capacité des Serveurs On-Premise
        </div>
        <div class="author">Présenté par : [NOM DE L'ÉTUDIANT]</div>
        <div class="author">Sous l'encadrement de : MAHMOUD Amine, DELIL Yassine</div>
        <div class="author">Pour l'obtention du diplôme de : [TYPE DE DIPLÔME]</div>
        <div class="author">En : Informatique / Génie Logiciel</div>
        <div class="date">Année Universitaire 2025-2026</div>
        <div class="logo" style="margin-top: 50px;">
            <div>[LOGO DE L'UNIVERSITÉ ET/OU DE L'ENTREPRISE]</div>
        </div>
    </div>

    <!-- Dédicace -->
    <div class="page">
        <div class="dedication">
            <h2>Dédicace</h2>
            <p>Je dédie ce travail à ma famille pour son soutien indéfectible tout au long de mon parcours académique.
            </p>
        </div>
    </div>

    <!-- Remerciements -->
    <div class="page">
        <div class="acknowledgements">
            <h2>Remerciements</h2>
            <p>Je tiens à exprimer ma profonde gratitude à mon encadrant académique, M. MEZGHANNI Dhafer, et à mes
                encadrants professionnels, M. DELIL Yassine et M. MAHMOUD Hatem, pour leur guidance et leurs précieux
                conseils.</p>
            <p>Mes remerciements vont également à l'équipe DevOps de Sopra HR Software pour leur accueil et leur
                collaboration.</p>
            <p>Je remercie également [NOM DE L'ÉCOLE/UNIVERSITÉ] et tous les enseignants qui ont contribué à ma
                formation.</p>
        </div>
    </div>

    <!-- Résumés -->
    <div class="page">
        <div class="abstract">
            <h2>Résumé</h2>
            <p>Ce projet de fin d'études porte sur le développement d'une plateforme web nommée CloudCapacity, destinée
                à la gestion automatisée de la capacité des serveurs on-premise chez Sopra HR Software. La solution vise
                à répondre à un besoin croissant d'optimisation des ressources informatiques à travers une surveillance
                proactive, un reporting automatisé et une prédiction des surcharges grâce à l'intelligence artificielle.
            </p>

            <p>Le projet s'articule autour d'une architecture moderne basée sur FastAPI et Angular, avec une
                conteneurisation via Docker et Kubernetes. L'intégration avec les systèmes existants comme Zabbix pour
                la collecte des métriques, Maia CMDB pour la gestion de la configuration et Jira pour le suivi des
                incidents est au cœur de la solution. Des algorithmes de machine learning sont implémentés pour analyser
                les tendances de consommation et prédire les besoins futurs en ressources.</p>

            <p>La plateforme CloudCapacity fournira une visibilité complète sur l'utilisation des ressources serveurs,
                automatisera la génération de rapports personnalisés et permettra une prise de décision éclairée
                concernant l'allocation des ressources informatiques, contribuant ainsi à optimiser les coûts
                d'infrastructure tout en garantissant la performance des services délivrés.</p>
        </div>

        <div class="abstract">
            <h2>Abstract</h2>
            <p>This graduation project focuses on the development of CloudCapacity, a web platform designed for
                automated capacity management of on-premise servers at Sopra HR Software. The solution aims to address
                the growing need for IT resource optimization through proactive monitoring, automated reporting, and
                overload prediction using artificial intelligence.</p>

            <p>The project is built around a modern architecture based on FastAPI and Angular, with containerization via
                Docker and Kubernetes. Integration with existing systems such as Zabbix for metrics collection, Maia
                CMDB for configuration management, and Jira for incident tracking is at the core of the solution.
                Machine learning algorithms are implemented to analyze consumption trends and predict future resource
                requirements.</p>

            <p>The CloudCapacity platform will provide complete visibility into server resource utilization, automate
                the generation of customized reports, and enable informed decision-making regarding IT resource
                allocation, thereby helping to optimize infrastructure costs while ensuring the performance of delivered
                services.</p>
        </div>
    </div>

    <!-- Table des matières -->
    <div class="page">
        <h2>Table des matières</h2>
        <div class="toc">
            <div>Dédicace</div>
            <div>Remerciements</div>
            <div>Résumé</div>
            <div>Abstract</div>
            <div>Table des matières</div>
            <div>Liste des figures</div>
            <div>Liste des tableaux</div>
            <div>Liste des abréviations et acronymes</div>

            <div class="font-bold mt-2">Introduction générale</div>

            <div class="font-bold mt-2">Chapitre 1 : Étude de l'existant</div>
            <div class="toc-item">1.1 Présentation de Sopra HR Software</div>
            <div class="toc-item">1.2 Contexte et problématique</div>
            <div class="toc-item">1.3 Solutions existantes de gestion de capacité</div>
            <div class="toc-item">1.4 Limites des solutions actuelles</div>

            <div class="font-bold mt-2">Chapitre 2 : Cahier des charges fonctionnel</div>
            <div class="toc-item">2.1 Objectifs du projet</div>
            <div class="toc-item">2.2 Périmètre du projet</div>
            <div class="toc-item">2.3 Besoins métiers</div>
            <div class="toc-item">2.4 Exigences fonctionnelles</div>
            <div class="toc-subsection">2.4.1 Collecte et analyse des données</div>
            <div class="toc-subsection">2.4.2 Visualisation et reporting</div>
            <div class="toc-subsection">2.4.3 Alertes et notifications</div>
            <div class="toc-subsection">2.4.4 Intégration avec les outils existants</div>
            <div class="toc-subsection">2.4.5 Machine learning et prédiction</div>

            <div class="font-bold mt-2">Chapitre 3 : Cahier des charges technique</div>
            <div class="toc-item">3.1 Architecture générale</div>
            <div class="toc-item">3.2 Technologies utilisées</div>
            <div class="toc-subsection">3.2.1 Frontend - Angular</div>
            <div class="toc-subsection">3.2.2 Backend - FastAPI</div>
            <div class="toc-subsection">3.2.3 Conteneurisation - Docker et Kubernetes</div>
            <div class="toc-subsection">3.2.4 Base de données et stockage</div>
            <div class="toc-subsection">3.2.5 Intégrations API</div>
            <div class="toc-item">3.3 Sécurité et gestion des accès</div>
            <div class="toc-item">3.4 Performances et scalabilité</div>
            <div class="toc-item">3.5 Déploiement et CI/CD</div>

            <div class="font-bold mt-2">Chapitre 4 : Modélisation</div>
            <div class="toc-item">4.1 Diagrammes de cas d'utilisation</div>
            <div class="toc-item">4.2 Diagrammes de classes</div>
            <div class="toc-item">4.3 Diagrammes de séquences</div>
            <div class="toc-item">4.4 Schémas d'architecture</div>
            <div class="toc-item">4.5 Modèle de données</div>

            <div class="font-bold mt-2">Chapitre 5 : Planification</div>
            <div class="toc-item">5.1 Méthodologie de travail</div>
            <div class="toc-item">5.2 Planning et jalons</div>
            <div class="toc-item">5.3 Gestion des risques</div>

            <div class="font-bold mt-2">Conclusion générale</div>

            <div class="font-bold mt-2">Bibliographie</div>

            <div class="font-bold mt-2">Annexes</div>
        </div>
    </div>

    <!-- Liste des figures -->
    <div class="page">
        <h2>Liste des figures</h2>
        <div class="toc">
            <div>Figure 1.1 : Situation actuelle de la gestion de capacité chez Sopra HR Software</div>
            <div>Figure 3.1 : Architecture générale de CloudCapacity</div>
            <div>Figure 4.1 : Diagramme de cas d'utilisation - Gestion des rapports</div>
            <div>Figure 4.2 : Diagramme de cas d'utilisation - Administration système</div>
            <div>Figure 4.3 : Diagramme de classes - Modèle de données principal</div>
            <div>Figure 4.4 : Diagramme de séquence - Génération de rapport</div>
            <div>Figure 4.5 : Diagramme de séquence - Analyse prédictive</div>
            <div>Figure 4.6 : Schéma d'architecture technique détaillée</div>
            <div>Figure 4.7 : Modèle de données et relations</div>
            <div>Figure 5.1 : Planning de développement du projet</div>
        </div>
    </div>

    <!-- Liste des tableaux -->
    <div class="page">
        <h2>Liste des tableaux</h2>
        <div class="toc">
            <div>Tableau 2.1 : Fonctionnalités prioritaires du projet</div>
            <div>Tableau 2.2 : Métriques à surveiller et analyser</div>
            <div>Tableau 3.1 : Technologies utilisées et justification</div>
            <div>Tableau 5.1 : Planning des livrables</div>
            <div>Tableau 5.2 : Analyse des risques et mesures d'atténuation</div>
        </div>
    </div>

    <!-- Liste des abréviations -->
    <div class="page">
        <h2>Liste des abréviations et acronymes</h2>
        <table class="table-data">
            <tbody>
                <tr>
                    <th>Acronyme</th>
                    <th>Définition</th>
                </tr>
                <tr>
                    <td>API</td>
                    <td>Application Programming Interface</td>
                </tr>
                <tr>
                    <td>CMDB</td>
                    <td>Configuration Management Database</td>
                </tr>
                <tr>
                    <td>CI/CD</td>
                    <td>Continuous Integration / Continuous Deployment</td>
                </tr>
                <tr>
                    <td>CPU</td>
                    <td>Central Processing Unit</td>
                </tr>
                <tr>
                    <td>HTML</td>
                    <td>HyperText Markup Language</td>
                </tr>
                <tr>
                    <td>HTTP</td>
                    <td>HyperText Transfer Protocol</td>
                </tr>
                <tr>
                    <td>IA</td>
                    <td>Intelligence Artificielle</td>
                </tr>
                <tr>
                    <td>JSON</td>
                    <td>JavaScript Object Notation</td>
                </tr>
                <tr>
                    <td>ML</td>
                    <td>Machine Learning</td>
                </tr>
                <tr>
                    <td>REST</td>
                    <td>Representational State Transfer</td>
                </tr>
                <tr>
                    <td>SHRS</td>
                    <td>Sopra HR Software</td>
                </tr>
                <tr>
                    <td>UML</td>
                    <td>Unified Modeling Language</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Introduction -->
    <div class="page latex-section">
        <h2>Introduction générale</h2>
        <p>Dans un contexte d'évolution constante des infrastructures informatiques, les entreprises font face à des
            défis majeurs concernant la gestion optimale de leurs ressources serveurs. La performance des systèmes,
            l'optimisation des coûts et la prévention des incidents sont des enjeux stratégiques pour garantir la
            continuité et la qualité des services offerts aux clients.</p>

        <p>Sopra HR Software, acteur majeur dans le domaine des solutions RH, dispose d'une infrastructure on-premise
            conséquente qui nécessite une supervision constante et une gestion proactive des ressources. Actuellement,
            le processus de suivi et d'analyse de la capacité des serveurs implique de nombreuses opérations manuelles,
            chronophages et susceptibles d'erreurs. Par ailleurs, l'absence d'un système centralisé de monitoring et de
            prédiction limite la capacité d'anticipation des équipes techniques face aux risques de surcharge.</p>

        <p>Le projet CloudCapacity s'inscrit dans une démarche d'amélioration continue et vise à développer une
            plateforme web complète pour automatiser le reporting de capacité, optimiser l'utilisation des ressources et
            prédire les surcharges potentielles grâce à des algorithmes de machine learning. Cette solution intégrée
            permettra de collecter, analyser et visualiser les métriques clés des serveurs, tout en proposant des
            recommandations d'ajustement basées sur l'analyse prédictive des tendances.</p>

        <p>Ce rapport présente le cahier des charges complet du projet CloudCapacity, depuis l'étude de l'existant
            jusqu'à la modélisation détaillée de la solution, en passant par les spécifications fonctionnelles et
            techniques. Une attention particulière est portée à l'architecture système, aux technologies utilisées et à
            l'intégration avec l'environnement technique actuel de Sopra HR Software.</p>
    </div>

    <!-- Chapitre 1 -->
    <div class="page latex-section">
        <h2>Chapitre 1 : Étude de l'existant</h2>

        <h3>1.1 Présentation de Sopra HR Software</h3>
        <p>Sopra HR Software est une filiale de Sopra Steria Group spécialisée dans les solutions de gestion des
            ressources humaines. L'entreprise accompagne plus de 900 clients dans 54 pays et propose des solutions
            complètes pour la digitalisation RH, allant de la gestion administrative à l'expérience collaborateur, en
            passant par la gestion des talents.</p>
        <p>Disposant d'une infrastructure informatique conséquente pour héberger ses solutions, Sopra HR Software
            maintient un parc de serveurs on-premise répartis sur plusieurs sites. La performance et la disponibilité de
            ces serveurs sont cruciales pour garantir la qualité de service offerte aux clients.</p>

        <h3>1.2 Contexte et problématique</h3>
        <p>La gestion actuelle de la capacité des serveurs chez Sopra HR Software présente plusieurs limitations :</p>
        <ul>
            <li>Collecte manuelle des métriques de performance, impliquant un temps considérable pour les équipes
                techniques</li>
            <li>Absence de centralisation des données de performance, rendant difficile l'analyse globale de
                l'infrastructure</li>
            <li>Génération manuelle des rapports de capacité, sujette aux erreurs et chronophage</li>
            <li>Manque d'outils prédictifs permettant d'anticiper les besoins en ressources</li>
            <li>Difficultés à corréler les incidents avec les métriques de performance pour une analyse approfondie</li>
        </ul>
        <p>Ces limitations entraînent des risques opérationnels, tels que des lenteurs potentielles dans les
            applications, des interruptions de service non anticipées, et une allocation sous-optimale des ressources,
            impactant directement les coûts d'infrastructure.</p>

        <h3>1.3 Solutions existantes de gestion de capacité</h3>
        <p>Actuellement, Sopra HR Software utilise plusieurs outils pour surveiller et gérer la capacité de ses serveurs
            :</p>
        <ul>
            <li><strong>Zabbix</strong> : Utilisé pour la collecte de métriques basiques, mais sans fonctionnalités
                avancées d'analyse et de prédiction</li>
            <li><strong>Maia CMDB</strong> : Solution de gestion de la configuration qui stocke les informations sur les
                serveurs et leur architecture</li>
            <li><strong>Jira</strong> : Utilisé pour la gestion des incidents, mais sans intégration automatisée avec
                les métriques de performance</li>
            <li><strong>Tableaux Excel</strong> : Utilisés pour la génération manuelle de rapports de capacité,
                nécessitant un traitement manuel des données</li>
        </ul>

        <h3>1.4 Limites des solutions actuelles</h3>
        <p>Bien que fonctionnelles, les solutions existantes présentent plusieurs limites :</p>
        <ul>
            <li>Absence d'automatisation du reporting, nécessitant des interventions manuelles régulières</li>
            <li>Manque d'intégration entre les différents outils, créant des silos d'information</li>
            <li>Absence de capacités prédictives pour anticiper les besoins futurs en ressources</li>
            <li>Interface utilisateur peu intuitive pour la visualisation des métriques</li>
            <li>Difficulté à personnaliser les rapports selon les besoins spécifiques des parties prenantes</li>
            <li>Absence de mécanismes d'alerte sophistiqués basés sur l'analyse des tendances</li>
        </ul>
        <p>Face à ces limitations, le projet CloudCapacity propose une solution intégrée qui combine les fonctionnalités
            de monitoring, d'analyse, de prédiction et de reporting dans une plateforme unique, s'appuyant sur les
            technologies modernes et l'intelligence artificielle.</p>
    </div>

    <!-- Chapitre 2 -->
    <div class="page latex-section">
        <h2>Chapitre 2 : Cahier des charges fonctionnel</h2>

        <h3>2.1 Objectifs du projet</h3>
        <p>Le projet CloudCapacity vise à répondre aux besoins de Sopra HR Software en matière de gestion de la capacité
            des serveurs on-premise. Les principaux objectifs sont :</p>
        <ul>
            <li><strong>Automatiser le reporting de capacité</strong> : Développer une plateforme web permettant de
                suivre de manière mensuelle les métriques des serveurs (CPU, mémoire, swap) et de générer des rapports
                de capacité personnalisés.</li>
            <li><strong>Optimiser l'utilisation des ressources</strong> : Mettre en place des algorithmes de machine
                learning pour prédire les surcharges de capacité et proposer des ajustements proactifs.</li>
            <li><strong>Centraliser la supervision</strong> : Offrir une vue unifiée de l'ensemble des métriques de
                performance à travers des tableaux de bord dynamiques et personnalisables.</li>
            <li><strong>Améliorer la réactivité</strong> : Mettre en place un système d'alertes et de notifications
                permettant d'informer rapidement les équipes techniques en cas d'anomalie.</li>
            <li><strong>Faciliter la prise de décision</strong> : Fournir des analyses prédictives et des
                recommandations pour optimiser l'allocation des ressources.</li>
        </ul>

        <h3>2.2 Périmètre du projet</h3>
        <p>Le périmètre du projet CloudCapacity inclut :</p>
        <ul>
            <li><strong>Systèmes et services</strong> : Tous les serveurs physiques et virtuels on-premise de Sopra,
                avec une intégration à l'API Zabbix pour collecter les métriques.</li>
            <li><strong>Outils et technologies</strong> : Utilisation de FastAPI et Angular pour l'application web,
                Docker et Kubernetes pour la conteneurisation et l'orchestration des services, avec un déploiement sur
                une plateforme adaptée aux environnements cloud hybrides OpenShift.</li>
            <li><strong>Intégrations</strong> : API Zabbix pour les métriques, Maia CMDB pour les données de
                configuration, Jira pour la gestion des incidents.</li>
            <li><strong>Machine learning</strong> : Implémentation d'algorithmes pour l'analyse prédictive et
                l'optimisation des ressources.</li>
        </ul>

        <p>Le projet ne couvre pas :</p>
        <ul>
            <li>La gestion des serveurs dans des environnements cloud externes (AWS, Azure, etc.)</li>
            <li>Le déploiement ou la configuration des services applicatifs sur les serveurs</li>
            <li>La gestion des coûts liés aux services cloud publics</li>
        </ul>

        <h3>2.3 Besoins métiers</h3>
        <p>Les besoins métiers identifiés pour le projet CloudCapacity sont :</p>
        <ul>
            <li><strong>Visibilité sur l'utilisation des ressources</strong> : Améliorer la compréhension de
                l'utilisation actuelle et future des ressources serveurs pour faciliter la planification des capacités.
            </li>
            <li><strong>Performance des systèmes</strong> : Garantir que les applications disposent des ressources
                nécessaires pour fonctionner de manière optimale, évitant ainsi les baisses de performance qui peuvent
                impacter les utilisateurs finaux.</li>
            <li><strong>Scalabilité</strong> : Assurer que la capacité des serveurs peut être ajustée de manière
                dynamique en fonction des besoins réels et prévus.</li>
            <li><strong>Optimisation des coûts</strong> : Réduire les dépenses liées à l'infrastructure en évitant le
                sur-provisionnement des ressources.</li>
            <li><strong>Sécurité</strong> : Prévenir les failles de sécurité potentielles liées à une mauvaise gestion
                de la capacité (serveurs surchargés, vulnérables aux attaques).</li>
            <li><strong>Productivité des équipes techniques</strong> : Libérer du temps pour les équipes en automatisant
                les tâches répétitives liées à la collecte de données et à la génération de rapports.</li>
        </ul>

        <h3>2.4 Exigences fonctionnelles</h3>

        <h4>2.4.1 Collecte et analyse des données</h4>
        <ul>
            <li><strong>Collecte des données de performance</strong> : Le système doit collecter des métriques telles
                que l'utilisation CPU, la mémoire, la bande passante, l'espace disque, et la latence des applications.
                Ces données doivent être disponibles et sur de longues périodes.</li>
            <li><strong>Analyse des tendances</strong> : La solution doit permettre d'analyser les tendances
                d'utilisation des ressources sur différentes périodes (journalière, hebdomadaire, mensuelle,
                trimestrielle).</li>
            <li><strong>Historisation et archivage des données</strong> : Le système doit permettre d'archiver les
                données de performance à long terme pour faciliter les analyses comparatives et l'identification des
                tendances saisonnières.</li>
        </ul>

        <h4>2.4.2 Visualisation et reporting</h4>
        <ul>
            <li><strong>Visualisation dynamique</strong> : L'outil doit permettre la visualisation des données sous
                forme de graphiques interactifs, de tableaux de bord dynamiques et de rapports détaillés pour faciliter
                l'analyse des tendances et la prise de décision.</li>
            <li><strong>Génération automatique de rapports</strong> : Le système doit permettre de générer
                automatiquement des rapports périodiques (quotidiens, hebdomadaires, mensuels) sur l'utilisation des
                ressources et les prévisions de capacité.</li>
            <li><strong>Envoi par email</strong> : Les rapports doivent être envoyés par email aux parties prenantes
                sous forme de fichiers PDF ou Excel, avec possibilité de personnaliser les destinataires et la fréquence
                d'envoi.</li>
            <li><strong>Personnalisation des rapports</strong> : Les rapports doivent pouvoir être personnalisés en
                fonction des besoins spécifiques des utilisateurs, avec possibilité de filtrer les données par serveur,
                par groupe de serveurs, par période, etc.</li>
        </ul>

        <h4>2.4.3 Alertes et notifications</h4>
        <ul>
            <li><strong>Seuils d'alerte configurables</strong> : L'administrateur doit pouvoir définir des seuils
                d'alerte personnalisés pour chaque métrique surveillée.</li>
            <li><strong>Alertes en cas de dépassement</strong> : Le système doit envoyer des alertes en cas de
                dépassement des seuils définis, avec différents niveaux de criticité (information, avertissement,
                critique).</li>
            <li><strong>Notification multicanal</strong> : Les alertes doivent pouvoir être envoyées via différents
                canaux (email, SMS, intégration avec des plateformes de communication comme Slack ou Microsoft Teams).
            </li>
            <li><strong>Reporting des alertes</strong> : Le système doit conserver un historique des alertes déclenchées
                et permettre la génération de rapports sur ces événements.</li>
        </ul>

        <h4>2.4.4 Intégration avec les outils existants</h4>
        <ul>
            <li><strong>API Zabbix</strong> : Intégration avec l'API Zabbix pour récupérer les métriques de performance
                des serveurs.</li>
            <li><strong>Maia CMDB</strong> : Intégration avec la base de données Maia CMDB pour récupérer les
                informations sur les serveurs et leur configuration.</li>
            <li><strong>Jira</strong> : L'intégration avec Jira permettra d'automatiser la création, le suivi et la mise
                à jour des tickets liés à la gestion de la capacité des serveurs, en corrélant les incidents aux
                métriques de performance.</li>
        </ul>

        <h4>2.4.5 Machine learning et prédiction</h4>
        <ul>
            <li><strong>Analyse prédictive</strong> : Utilisation d'algorithmes de machine learning pour analyser les
                tendances historiques et prédire l'utilisation future des ressources.</li>
            <li><strong>Recommandations d'optimisation</strong> : Le système doit proposer des recommandations pour
                optimiser l'allocation des ressources en fonction des prévisions d'utilisation.</li>
            <li><strong>Détection d'anomalies</strong> : Mise en place d'algorithmes pour détecter automatiquement les
                anomalies dans l'utilisation des ressources, permettant une intervention préventive.</li>
            <li><strong>Apprentissage continu</strong> : Le modèle de prédiction doit s'améliorer continuellement en
                intégrant les nouvelles données collectées.</li>
        </ul>
    </div>

    <!-- Chapitre 3 -->
    <div class="page latex-section">
        <h2>Chapitre 3 : Cahier des charges technique</h2>

        <h3>3.1 Architecture générale</h3>
        <p>L'architecture de CloudCapacity est conçue pour être modulaire, distribuée et scalable, permettant une
            évolution progressive du système et une adaptation aux besoins futurs. Elle s'articule autour des composants
            suivants :</p>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378152" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 408.7890625px;" viewBox="-8 -8 408.7890625 418.96875"
                    role="graphics-document document" aria-roledescription="flowchart-v2">
                    <style>
                        #mermaid-1746403378152 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378152 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378152 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378152 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378152 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378152 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378152 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378152 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378152 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378152 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378152 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378152 .label {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            color: #000000;
                        }

                        #mermaid-1746403378152 .cluster-label text {
                            fill: #333;
                        }

                        #mermaid-1746403378152 .cluster-label span,
                        #mermaid-1746403378152 p {
                            color: #333;
                        }

                        #mermaid-1746403378152 .label text,
                        #mermaid-1746403378152 span,
                        #mermaid-1746403378152 p {
                            fill: #000000;
                            color: #000000;
                        }

                        #mermaid-1746403378152 .node rect,
                        #mermaid-1746403378152 .node circle,
                        #mermaid-1746403378152 .node ellipse,
                        #mermaid-1746403378152 .node polygon,
                        #mermaid-1746403378152 .node path {
                            fill: #eee;
                            stroke: #999;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378152 .flowchart-label text {
                            text-anchor: middle;
                        }

                        #mermaid-1746403378152 .node .label {
                            text-align: center;
                        }

                        #mermaid-1746403378152 .node.clickable {
                            cursor: pointer;
                        }

                        #mermaid-1746403378152 .arrowheadPath {
                            fill: #333333;
                        }

                        #mermaid-1746403378152 .edgePath .path {
                            stroke: #666;
                            stroke-width: 2.0px;
                        }

                        #mermaid-1746403378152 .flowchart-link {
                            stroke: #666;
                            fill: none;
                        }

                        #mermaid-1746403378152 .edgeLabel {
                            background-color: white;
                            text-align: center;
                        }

                        #mermaid-1746403378152 .edgeLabel rect {
                            opacity: 0.5;
                            background-color: white;
                            fill: white;
                        }

                        #mermaid-1746403378152 .labelBkg {
                            background-color: rgba(255, 255, 255, 0.5);
                        }

                        #mermaid-1746403378152 .cluster rect {
                            fill: hsl(0, 0%, 98.9215686275%);
                            stroke: #707070;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378152 .cluster text {
                            fill: #333;
                        }

                        #mermaid-1746403378152 .cluster span,
                        #mermaid-1746403378152 p {
                            color: #333;
                        }

                        #mermaid-1746403378152 div.mermaidTooltip {
                            position: absolute;
                            text-align: center;
                            max-width: 200px;
                            padding: 2px;
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 12px;
                            background: hsl(-160, 0%, 93.3333333333%);
                            border: 1px solid #707070;
                            border-radius: 2px;
                            pointer-events: none;
                            z-index: 100;
                        }

                        #mermaid-1746403378152 .flowchartTitleText {
                            text-anchor: middle;
                            font-size: 18px;
                            fill: #000000;
                        }

                        #mermaid-1746403378152 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g>
                        <marker id="mermaid-1746403378152_flowchart-pointEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="6" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378152_flowchart-pointStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378152_flowchart-circleEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378152_flowchart-circleStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378152_flowchart-crossEnd" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378152_flowchart-crossStart" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <g class="root">
                            <g class="clusters"></g>
                            <g class="edgePaths">
                                <path
                                    d="M209.84,40.594L209.84,44.76C209.84,48.927,209.84,57.26,209.84,64.71C209.84,72.16,209.84,78.727,209.84,82.01L209.84,85.294"
                                    id="L-A-B-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378152_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M209.84,131.188L209.84,135.354C209.84,139.521,209.84,147.854,209.84,155.304C209.84,162.754,209.84,169.321,209.84,172.604L209.84,175.888"
                                    id="L-B-C-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B LE-C"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378152_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M225.462,221.781L228.669,225.948C231.875,230.115,238.289,238.448,241.496,250.164C244.703,261.88,244.703,276.979,244.703,292.078C244.703,307.177,244.703,322.276,247.804,333.331C250.906,344.385,257.108,351.395,260.21,354.901L263.311,358.406"
                                    id="L-C-D-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-D"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378152_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M143.793,219.647L127.348,224.17C110.904,228.692,78.014,237.737,61.57,245.542C45.125,253.348,45.125,259.915,45.125,263.198L45.125,266.481"
                                    id="L-C-E-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-E"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378152_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M194.218,221.781L191.011,225.948C187.804,230.115,181.39,238.448,178.183,245.898C174.977,253.348,174.977,259.915,174.977,263.198L174.977,266.481"
                                    id="L-C-F-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-F"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378152_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M261.378,221.781L271.959,225.948C282.539,230.115,303.699,238.448,314.279,245.898C324.859,253.348,324.859,259.915,324.859,263.198L324.859,266.481"
                                    id="L-C-G-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-C LE-G"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378152_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M324.859,312.375L324.859,316.542C324.859,320.708,324.859,329.042,321.758,336.713C318.657,344.385,312.454,351.395,309.353,354.901L306.252,358.406"
                                    id="L-G-D-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-D"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378152_flowchart-pointEnd)">
                                </path>
                            </g>
                            <g class="edgeLabels">
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                            <g class="nodes">
                                <g class="node default default flowchart-label" id="flowchart-A-0"
                                    transform="translate(209.83984375, 20.296875)">
                                    <rect class="basic label-container" style="fill:#f9f;stroke:#333;stroke-width:2px;"
                                        rx="0" ry="0" x="-80.9765625" y="-20.296875" width="161.953125"
                                        height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-73.4765625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="146.953125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Client Web - Angular</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B-1"
                                    transform="translate(209.83984375, 110.890625)">
                                    <rect class="basic label-container" style="fill:#bbf;stroke:#333;stroke-width:2px;"
                                        rx="0" ry="0" x="-52.5703125" y="-20.296875" width="105.140625"
                                        height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-45.0703125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="90.140625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">API Gateway</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-C-3"
                                    transform="translate(209.83984375, 201.484375)">
                                    <rect class="basic label-container" style="fill:#dfd;stroke:#333;stroke-width:2px;"
                                        rx="0" ry="0" x="-66.046875" y="-20.296875" width="132.09375" height="40.59375">
                                    </rect>
                                    <g class="label" style="" transform="translate(-58.546875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="117.09375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Backend FastAPI</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-D-5"
                                    transform="translate(284.78125, 382.671875)">
                                    <rect class="basic label-container" style="fill:#fdd;stroke:#333;stroke-width:2px;"
                                        rx="0" ry="0" x="-108.0078125" y="-20.296875" width="216.015625"
                                        height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-100.5078125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="201.015625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Base de données Maia CMDB</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-E-7"
                                    transform="translate(45.125, 292.078125)">
                                    <rect class="basic label-container" style="fill:#ddd;stroke:#333;stroke-width:2px;"
                                        rx="0" ry="0" x="-45.125" y="-20.296875" width="90.25" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-37.625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="75.25" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">API Zabbix</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-F-9"
                                    transform="translate(174.9765625, 292.078125)">
                                    <rect class="basic label-container" style="fill:#ddd;stroke:#333;stroke-width:2px;"
                                        rx="0" ry="0" x="-34.7265625" y="-20.296875" width="69.453125"
                                        height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-27.2265625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="54.453125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">API Jira</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-G-11"
                                    transform="translate(324.859375, 292.078125)">
                                    <rect class="basic label-container" style="fill:#ffd;stroke:#333;stroke-width:2px;"
                                        rx="0" ry="0" x="-45.15625" y="-20.296875" width="90.3125" height="40.59375">
                                    </rect>
                                    <g class="label" style="" transform="translate(-37.65625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="75.3125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Module ML</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg></div>
            <div class="figure-caption">Figure 3.1 : Architecture générale de CloudCapacity</div>
        </div>

        <p>Cette architecture repose sur les principes suivants :</p>
        <ul>
            <li><strong>Séparation des responsabilités</strong> : Les différentes couches (présentation, logique métier,
                accès aux données) sont clairement séparées.</li>
            <li><strong>API-centric</strong> : Toutes les interactions entre les composants se font via des API RESTful
                bien définies.</li>
            <li><strong>Conteneurisation</strong> : Les composants sont déployés sous forme de conteneurs Docker
                orchestrés par Kubernetes.</li>
            <li><strong>Evolutivité</strong> : L'architecture permet d'ajouter facilement de nouvelles fonctionnalités
                ou d'évoluer vers de nouvelles technologies.</li>
        </ul>

        <h3>3.2 Technologies utilisées</h3>
        <p>Le choix des technologies a été effectué en tenant compte des exigences fonctionnelles et non fonctionnelles
            du projet, ainsi que des compétences disponibles au sein de l'équipe.</p>

        <h4>3.2.1 Frontend - Angular</h4>
        <p>Le frontend de CloudCapacity est développé avec le framework Angular, qui offre les avantages suivants :</p>
        <ul>
            <li>Architecture MVC robuste pour des applications web complexes</li>
            <li>Performances optimisées avec le changement de détection</li>
            <li>Large écosystème de bibliothèques pour la visualisation de données (D3.js, Chart.js, etc.)</li>
            <li>Support pour la création d'applications responsive</li>
            <li>Typage fort avec TypeScript pour une meilleure maintenabilité</li>
        </ul>
        <p>Les principaux modules Angular utilisés comprennent :</p>
        <ul>
            <li>Angular Material pour l'interface utilisateur</li>
            <li>NgRx pour la gestion d'état</li>
            <li>RxJS pour la programmation réactive</li>
            <li>Angular CLI pour le développement et le build</li>
        </ul>

        <h4>3.2.2 Backend - FastAPI</h4>
        <p>Le backend est développé avec FastAPI, un framework Python moderne qui offre :</p>
        <ul>
            <li>Hautes performances grâce à Starlette et Pydantic</li>
            <li>Documentation API automatique via OpenAPI et Swagger UI</li>
            <li>Validation de données intégrée</li>
            <li>Support asynchrone natif</li>
            <li>Facilité d'intégration avec les bibliothèques Python pour le machine learning</li>
        </ul>
        <p>Les principales bibliothèques Python utilisées comprennent :</p>
        <ul>
            <li>SQLAlchemy pour l'ORM</li>
            <li>Pandas pour la manipulation de données</li>
            <li>Scikit-learn et TensorFlow pour les algorithmes de machine learning</li>
            <li>Celery pour les tâches asynchrones</li>
            <li>PyJWT pour l'authentification</li>
        </ul>

        <h4>3.2.3 Conteneurisation - Docker et Kubernetes</h4>
        <p>La solution utilise Docker pour la conteneurisation des différents composants, offrant :</p>
        <ul>
            <li>Isolation des environnements d'exécution</li>
            <li>Portabilité entre les différents environnements (développement, test, production)</li>
            <li>Gestion simplifiée des dépendances</li>
        </ul>
        <p>Kubernetes est utilisé pour l'orchestration des conteneurs, avec les avantages suivants :</p>
        <ul>
            <li>Scaling automatique en fonction de la charge</li>
            <li>Auto-réparation en cas de défaillance d'un conteneur</li>
            <li>Déploiement sans interruption de service (zero downtime)</li>
            <li>Gestion avancée du réseau et du stockage</li>
        </ul>
        <p>Le déploiement est effectué sur OpenShift, la plateforme d'orchestration de conteneurs de Red Hat basée sur
            Kubernetes, qui offre des fonctionnalités supplémentaires de sécurité et de gestion.</p>

        <h4>3.2.4 Base de données et stockage</h4>
        <p>La solution utilise principalement la base Maia CMDB existante pour stocker les informations sur les serveurs
            et leur configuration. Pour les données de métriques et les prédictions, une base de données PostgreSQL est
            utilisée avec les caractéristiques suivantes :</p>
        <ul>
            <li>Support pour les requêtes complexes et les fonctions d'agrégation</li>
            <li>Gestion efficace des séries temporelles avec l'extension TimescaleDB</li>
            <li>Haute disponibilité et réplication</li>
            <li>Support pour les transactions ACID</li>
        </ul>

        <h4>3.2.5 Intégrations API</h4>
        <p>CloudCapacity s'intègre avec plusieurs API externes :</p>
        <ul>
            <li><strong>API Zabbix</strong> : Pour la collecte des métriques de performance des serveurs</li>
            <li><strong>API Maia</strong> : Pour accéder aux informations de configuration des serveurs</li>
            <li><strong>API Jira</strong> : Pour la création et le suivi des tickets d'incident</li>
        </ul>
        <p>Ces intégrations sont réalisées via des connecteurs dédiés qui gèrent l'authentification, la transformation
            des données et la gestion des erreurs.</p>

        <h3>3.3 Sécurité et gestion des accès</h3>
        <p>La sécurité de CloudCapacity repose sur plusieurs mécanismes :</p>
        <ul>
            <li><strong>Authentification</strong> : Intégration avec le système d'authentification unique (SSO) de
                l'entreprise</li>
            <li><strong>Autorisation</strong> : Système de rôles et de permissions pour contrôler l'accès aux
                fonctionnalités</li>
            <li><strong>Communication sécurisée</strong> : Utilisation de HTTPS pour toutes les communications</li>
            <li><strong>Protection des données</strong> : Chiffrement des données sensibles au repos</li>
            <li><strong>Audit</strong> : Journalisation de toutes les actions effectuées sur le système</li>
        </ul>

        <h3>3.4 Performances et scalabilité</h3>
        <p>Pour garantir les performances et la scalabilité de la solution, plusieurs mécanismes sont mis en place :</p>
        <ul>
            <li><strong>Mise en cache</strong> : Utilisation de Redis pour le cache des requêtes fréquentes</li>
            <li><strong>Scaling horizontal</strong> : Possibilité d'ajouter des instances supplémentaires des composants
                pour répondre à une charge accrue</li>
            <li><strong>Optimisation des requêtes</strong> : Indexation appropriée des bases de données et optimisation
                des requêtes</li>
            <li><strong>Pagination</strong> : Mise en place de mécanismes de pagination pour les requêtes retournant de
                grands volumes de données</li>
            <li><strong>Traitement asynchrone</strong> : Utilisation de tâches asynchrones pour les opérations longues
                comme la génération de rapports</li>
        </ul>

        <h3>3.5 Déploiement et CI/CD</h3>
        <p>Le processus de déploiement de CloudCapacity s'appuie sur une approche DevOps avec :</p>
        <ul>
            <li><strong>GitLab CI/CD</strong> : Pour l'automatisation des builds, des tests et des déploiements</li>
            <li><strong>Environnements multiples</strong> : Développement, test, préproduction et production</li>
            <li><strong>Tests automatisés</strong> : Tests unitaires, tests d'intégration et tests de charge</li>
            <li><strong>Surveillance</strong> : Mise en place de mécanismes de surveillance pour détecter les problèmes
                en production</li>
            <li><strong>Rollback automatique</strong> : En cas de problème détecté lors du déploiement</li>
        </ul>
        <p>Le déploiement sur OpenShift permet de bénéficier des fonctionnalités avancées de cette plateforme en termes
            de gestion des ressources, de scaling automatique et de sécurité.</p>
    </div>

    <!-- Chapitre 4 -->
    <div class="page latex-section">
        <h2>Chapitre 4 : Modélisation</h2>

        <h3>4.1 Diagrammes de cas d'utilisation</h3>
        <p>Les diagrammes de cas d'utilisation présentent les interactions entre les différents acteurs et le système
            CloudCapacity.</p>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378199" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 579.953125px;" viewBox="-8 -8 579.953125 1666.28125"
                    role="graphics-document document" aria-roledescription="flowchart-v2">
                    <style>
                        #mermaid-1746403378199 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378199 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378199 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378199 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378199 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378199 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378199 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378199 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378199 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378199 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378199 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378199 .label {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            color: #000000;
                        }

                        #mermaid-1746403378199 .cluster-label text {
                            fill: #333;
                        }

                        #mermaid-1746403378199 .cluster-label span,
                        #mermaid-1746403378199 p {
                            color: #333;
                        }

                        #mermaid-1746403378199 .label text,
                        #mermaid-1746403378199 span,
                        #mermaid-1746403378199 p {
                            fill: #000000;
                            color: #000000;
                        }

                        #mermaid-1746403378199 .node rect,
                        #mermaid-1746403378199 .node circle,
                        #mermaid-1746403378199 .node ellipse,
                        #mermaid-1746403378199 .node polygon,
                        #mermaid-1746403378199 .node path {
                            fill: #eee;
                            stroke: #999;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378199 .flowchart-label text {
                            text-anchor: middle;
                        }

                        #mermaid-1746403378199 .node .label {
                            text-align: center;
                        }

                        #mermaid-1746403378199 .node.clickable {
                            cursor: pointer;
                        }

                        #mermaid-1746403378199 .arrowheadPath {
                            fill: #333333;
                        }

                        #mermaid-1746403378199 .edgePath .path {
                            stroke: #666;
                            stroke-width: 2.0px;
                        }

                        #mermaid-1746403378199 .flowchart-link {
                            stroke: #666;
                            fill: none;
                        }

                        #mermaid-1746403378199 .edgeLabel {
                            background-color: white;
                            text-align: center;
                        }

                        #mermaid-1746403378199 .edgeLabel rect {
                            opacity: 0.5;
                            background-color: white;
                            fill: white;
                        }

                        #mermaid-1746403378199 .labelBkg {
                            background-color: rgba(255, 255, 255, 0.5);
                        }

                        #mermaid-1746403378199 .cluster rect {
                            fill: hsl(0, 0%, 98.9215686275%);
                            stroke: #707070;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378199 .cluster text {
                            fill: #333;
                        }

                        #mermaid-1746403378199 .cluster span,
                        #mermaid-1746403378199 p {
                            color: #333;
                        }

                        #mermaid-1746403378199 div.mermaidTooltip {
                            position: absolute;
                            text-align: center;
                            max-width: 200px;
                            padding: 2px;
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 12px;
                            background: hsl(-160, 0%, 93.3333333333%);
                            border: 1px solid #707070;
                            border-radius: 2px;
                            pointer-events: none;
                            z-index: 100;
                        }

                        #mermaid-1746403378199 .flowchartTitleText {
                            text-anchor: middle;
                            font-size: 18px;
                            fill: #000000;
                        }

                        #mermaid-1746403378199 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g>
                        <marker id="mermaid-1746403378199_flowchart-pointEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="6" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378199_flowchart-pointStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378199_flowchart-circleEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378199_flowchart-circleStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378199_flowchart-crossEnd" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378199_flowchart-crossStart" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <g class="root">
                            <g class="clusters"></g>
                            <g class="edgePaths">
                                <path
                                    d="M96.407,283.688L124.606,251.07C152.805,218.453,209.203,153.219,255.708,120.602C302.213,87.984,338.823,87.984,357.129,87.984L375.434,87.984"
                                    id="L-A-B-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M139.852,303.984L160.81,303.984C181.768,303.984,223.685,303.984,264.61,303.984C305.535,303.984,345.469,303.984,365.436,303.984L385.403,303.984"
                                    id="L-A-C-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-C"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M98.63,324.281L126.459,352.85C154.287,381.419,209.944,438.557,260.127,467.126C310.309,495.695,355.016,495.695,377.37,495.695L399.723,495.695"
                                    id="L-A-D-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-D"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M106.702,820.461L133.185,801.155C159.668,781.849,212.635,743.237,256.215,723.931C299.796,704.625,333.99,704.625,351.087,704.625L368.184,704.625"
                                    id="L-E-F-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-E LE-F"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M157.719,840.758L175.699,840.758C193.68,840.758,229.641,840.758,268.27,849.962C306.9,859.166,348.197,877.575,368.846,886.779L389.495,895.983"
                                    id="L-E-G-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-E LE-G"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M91.275,861.055L120.33,908.551C149.384,956.047,207.493,1051.039,255.634,1098.535C303.775,1146.031,341.948,1146.031,361.035,1146.031L380.122,1146.031"
                                    id="L-E-H-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-E LE-H"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M90.093,1332.977L119.344,1280.125C148.596,1227.273,207.099,1121.57,256.857,1060.18C306.616,998.79,347.63,981.714,368.138,973.175L388.645,964.637"
                                    id="L-I-G-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-I LE-G"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M116.305,1353.273L141.188,1353.273C166.07,1353.273,215.836,1353.273,261.364,1353.273C306.892,1353.273,348.183,1353.273,368.828,1353.273L389.473,1353.273"
                                    id="L-I-J-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-I LE-J"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M96.867,1373.57L124.99,1405.267C153.112,1436.964,209.357,1500.357,256.027,1532.053C302.697,1563.75,339.792,1563.75,358.34,1563.75L376.888,1563.75"
                                    id="L-I-K-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-I LE-K"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378199_flowchart-pointEnd)">
                                </path>
                            </g>
                            <g class="edgeLabels">
                                <g class="edgeLabel" transform="translate(265.6015625, 87.984375)">
                                    <g class="label" transform="translate(-78.1640625, -12.796875)">
                                        <foreignobject width="156.328125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Configurer le système</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 303.984375)">
                                    <g class="label" transform="translate(-75.296875, -12.796875)">
                                        <foreignobject width="150.59375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Gérer les utilisateurs</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 495.6953125)">
                                    <g class="label" transform="translate(-82.8828125, -12.796875)">
                                        <foreignobject width="165.765625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Configurer Intégrations</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 704.625)">
                                    <g class="label" transform="translate(-76.4375, -12.796875)">
                                        <foreignobject width="152.875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Visualiser dashboards</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 840.7578125)">
                                    <g class="label" transform="translate(-61.5390625, -12.796875)">
                                        <foreignobject width="123.078125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Générer rapports</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 1146.03125)">
                                    <g class="label" transform="translate(-64.875, -12.796875)">
                                        <foreignobject width="129.75" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Configurer alertes</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 1015.8671875)">
                                    <g class="label" transform="translate(-66.453125, -12.796875)">
                                        <foreignobject width="132.90625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Visualiser rapports</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 1353.2734375)">
                                    <g class="label" transform="translate(-69.21875, -12.796875)">
                                        <foreignobject width="138.4375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Analyser tendances</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(265.6015625, 1563.75)">
                                    <g class="label" transform="translate(-63.3984375, -12.796875)">
                                        <foreignobject width="126.796875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Planifier capacité</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                            <g class="nodes">
                                <g class="node default default flowchart-label" id="flowchart-A-21"
                                    transform="translate(78.859375, 303.984375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-60.9921875"
                                        y="-20.296875" width="121.984375" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-53.4921875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="106.984375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Administrateur</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B-22"
                                    transform="translate(468.71875, 87.984375)">
                                    <circle style="" rx="0" ry="0" r="87.984375" width="175.96875" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-80.484375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="160.96875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Configuration Système</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-C-24"
                                    transform="translate(468.71875, 303.984375)">
                                    <circle style="" rx="0" ry="0" r="78.015625" width="156.03125" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-70.515625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="141.03125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Gestion Utilisateurs</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-D-26"
                                    transform="translate(468.71875, 495.6953125)">
                                    <circle style="" rx="0" ry="0" r="63.6953125" width="127.390625" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-56.1953125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="112.390625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Intégrations API</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-E-27"
                                    transform="translate(78.859375, 840.7578125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-78.859375"
                                        y="-20.296875" width="157.71875" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-71.359375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="142.71875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Utilisateur standard</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-F-28"
                                    transform="translate(468.71875, 704.625)">
                                    <circle style="" rx="0" ry="0" r="95.234375" width="190.46875" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-87.734375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="175.46875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Visualisation Dashboards</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-G-30"
                                    transform="translate(468.71875, 931.296875)">
                                    <circle style="" rx="0" ry="0" r="81.4375" width="162.875" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-73.9375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="147.875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Génération Rapports</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-H-32"
                                    transform="translate(468.71875, 1146.03125)">
                                    <circle style="" rx="0" ry="0" r="83.296875" width="166.59375" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-75.796875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="151.59375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Configuration Alertes</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-I-33"
                                    transform="translate(78.859375, 1353.2734375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-37.4453125"
                                        y="-20.296875" width="74.890625" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-29.9453125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="59.890625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Manager</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-J-36"
                                    transform="translate(468.71875, 1353.2734375)">
                                    <circle style="" rx="0" ry="0" r="73.9453125" width="147.890625" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-66.4453125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="132.890625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Analyse Tendances</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-K-38"
                                    transform="translate(468.71875, 1563.75)">
                                    <circle style="" rx="0" ry="0" r="86.53125" width="173.0625" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-79.03125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="158.0625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Planification Capacité</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg></div>
            <div class="figure-caption">Figure 4.1 : Diagramme de cas d'utilisation - Gestion des rapports</div>
        </div>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378230" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 494.78125px;" viewBox="-8 -8 494.78125 1660.5" role="graphics-document document"
                    aria-roledescription="flowchart-v2">
                    <style>
                        #mermaid-1746403378230 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378230 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378230 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378230 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378230 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378230 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378230 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378230 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378230 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378230 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378230 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378230 .label {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            color: #000000;
                        }

                        #mermaid-1746403378230 .cluster-label text {
                            fill: #333;
                        }

                        #mermaid-1746403378230 .cluster-label span,
                        #mermaid-1746403378230 p {
                            color: #333;
                        }

                        #mermaid-1746403378230 .label text,
                        #mermaid-1746403378230 span,
                        #mermaid-1746403378230 p {
                            fill: #000000;
                            color: #000000;
                        }

                        #mermaid-1746403378230 .node rect,
                        #mermaid-1746403378230 .node circle,
                        #mermaid-1746403378230 .node ellipse,
                        #mermaid-1746403378230 .node polygon,
                        #mermaid-1746403378230 .node path {
                            fill: #eee;
                            stroke: #999;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378230 .flowchart-label text {
                            text-anchor: middle;
                        }

                        #mermaid-1746403378230 .node .label {
                            text-align: center;
                        }

                        #mermaid-1746403378230 .node.clickable {
                            cursor: pointer;
                        }

                        #mermaid-1746403378230 .arrowheadPath {
                            fill: #333333;
                        }

                        #mermaid-1746403378230 .edgePath .path {
                            stroke: #666;
                            stroke-width: 2.0px;
                        }

                        #mermaid-1746403378230 .flowchart-link {
                            stroke: #666;
                            fill: none;
                        }

                        #mermaid-1746403378230 .edgeLabel {
                            background-color: white;
                            text-align: center;
                        }

                        #mermaid-1746403378230 .edgeLabel rect {
                            opacity: 0.5;
                            background-color: white;
                            fill: white;
                        }

                        #mermaid-1746403378230 .labelBkg {
                            background-color: rgba(255, 255, 255, 0.5);
                        }

                        #mermaid-1746403378230 .cluster rect {
                            fill: hsl(0, 0%, 98.9215686275%);
                            stroke: #707070;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378230 .cluster text {
                            fill: #333;
                        }

                        #mermaid-1746403378230 .cluster span,
                        #mermaid-1746403378230 p {
                            color: #333;
                        }

                        #mermaid-1746403378230 div.mermaidTooltip {
                            position: absolute;
                            text-align: center;
                            max-width: 200px;
                            padding: 2px;
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 12px;
                            background: hsl(-160, 0%, 93.3333333333%);
                            border: 1px solid #707070;
                            border-radius: 2px;
                            pointer-events: none;
                            z-index: 100;
                        }

                        #mermaid-1746403378230 .flowchartTitleText {
                            text-anchor: middle;
                            font-size: 18px;
                            fill: #000000;
                        }

                        #mermaid-1746403378230 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g>
                        <marker id="mermaid-1746403378230_flowchart-pointEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="6" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378230_flowchart-pointStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378230_flowchart-circleEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378230_flowchart-circleStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378230_flowchart-crossEnd" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378230_flowchart-crossStart" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <g class="root">
                            <g class="clusters"></g>
                            <g class="edgePaths">
                                <path
                                    d="M68.896,465.539L93.772,401.654C118.649,337.768,168.403,209.997,208.425,146.112C248.447,82.227,278.738,82.227,293.883,82.227L309.028,82.227"
                                    id="L-A-B-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-B"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M77.218,465.539L100.707,436.155C124.197,406.771,171.177,348.003,211.053,318.618C250.929,289.234,283.701,289.234,300.087,289.234L316.473,289.234"
                                    id="L-A-C-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-C"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M121.984,485.836L138.013,485.836C154.042,485.836,186.099,485.836,219.007,485.836C251.916,485.836,285.675,485.836,302.555,485.836L319.434,485.836"
                                    id="L-A-D-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-D"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M79.914,506.133L102.954,530.848C125.995,555.563,172.075,604.992,216.171,629.707C260.267,654.422,302.378,654.422,323.434,654.422L344.489,654.422"
                                    id="L-A-E-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-E"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M70.58,506.133L95.176,558.202C119.772,610.271,168.964,714.409,211.183,766.478C253.403,818.547,288.649,818.547,306.272,818.547L323.895,818.547"
                                    id="L-A-F-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A LE-F"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M71.701,1288.098L96.11,1241.833C120.519,1195.568,169.338,1103.038,210.163,1056.773C250.989,1010.508,283.821,1010.508,300.237,1010.508L316.653,1010.508"
                                    id="L-G-H-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-H"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M93.104,1288.098L113.946,1274.924C134.788,1261.75,176.472,1235.402,213.84,1222.229C251.207,1209.055,284.258,1209.055,300.784,1209.055L317.309,1209.055"
                                    id="L-G-I-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-I"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M93.104,1328.691L113.946,1341.865C134.788,1355.039,176.472,1381.387,213.708,1394.561C250.944,1407.734,283.732,1407.734,300.126,1407.734L316.52,1407.734"
                                    id="L-G-J-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-J"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M72.381,1328.691L96.677,1371.99C120.973,1415.289,169.565,1501.887,213.374,1545.186C257.184,1588.484,296.211,1588.484,315.725,1588.484L335.239,1588.484"
                                    id="L-G-K-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-G LE-K"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378230_flowchart-pointEnd)">
                                </path>
                            </g>
                            <g class="edgeLabels">
                                <g class="edgeLabel" transform="translate(218.15625, 82.2265625)">
                                    <g class="label" transform="translate(-63.875, -12.796875)">
                                        <foreignobject width="127.75" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Configurer Zabbix</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 289.234375)">
                                    <g class="label" transform="translate(-56.4296875, -12.796875)">
                                        <foreignobject width="112.859375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Configurer Maia</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 485.8359375)">
                                    <g class="label" transform="translate(-53.4765625, -12.796875)">
                                        <foreignobject width="106.953125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Configurer Jira</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 654.421875)">
                                    <g class="label" transform="translate(-58.9921875, -12.796875)">
                                        <foreignobject width="117.984375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Gérer ML Models</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 818.546875)">
                                    <g class="label" transform="translate(-53.0859375, -12.796875)">
                                        <foreignobject width="106.171875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Gérer Serveurs</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 1010.5078125)">
                                    <g class="label" transform="translate(-71.171875, -12.796875)">
                                        <foreignobject width="142.34375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Collecter métriques</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 1209.0546875)">
                                    <g class="label" transform="translate(-69.21875, -12.796875)">
                                        <foreignobject width="138.4375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Analyser tendances</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 1407.734375)">
                                    <g class="label" transform="translate(-56.3125, -12.796875)">
                                        <foreignobject width="112.625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Générer alertes</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel" transform="translate(218.15625, 1588.484375)">
                                    <g class="label" transform="translate(-66.1640625, -12.796875)">
                                        <foreignobject width="132.328125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">Prédire surcharges</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                            <g class="nodes">
                                <g class="node default default flowchart-label" id="flowchart-A-39"
                                    transform="translate(60.9921875, 485.8359375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-60.9921875"
                                        y="-20.296875" width="121.984375" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-53.4921875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="106.984375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Administrateur</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B-40"
                                    transform="translate(396.5546875, 82.2265625)">
                                    <circle style="" rx="0" ry="0" r="82.2265625" width="164.453125" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-74.7265625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="149.453125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Configuration Zabbix</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-C-42"
                                    transform="translate(396.5546875, 289.234375)">
                                    <circle style="" rx="0" ry="0" r="74.78125" width="149.5625" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-67.28125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="134.5625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Configuration Maia</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-D-44"
                                    transform="translate(396.5546875, 485.8359375)">
                                    <circle style="" rx="0" ry="0" r="71.8203125" width="143.640625" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-64.3203125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="128.640625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Configuration Jira</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-E-46"
                                    transform="translate(396.5546875, 654.421875)">
                                    <circle style="" rx="0" ry="0" r="46.765625" width="93.53125" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-39.265625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="78.53125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Gestion ML</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-F-48"
                                    transform="translate(396.5546875, 818.546875)">
                                    <circle style="" rx="0" ry="0" r="67.359375" width="134.71875" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-59.859375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="119.71875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Gestion Serveurs</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-G-49"
                                    transform="translate(60.9921875, 1308.39453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-32.7109375"
                                        y="-20.296875" width="65.421875" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-25.2109375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="50.421875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">System</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-H-50"
                                    transform="translate(396.5546875, 1010.5078125)">
                                    <circle style="" rx="0" ry="0" r="74.6015625" width="149.203125" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-67.1015625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="134.203125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Collecte Métriques</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-I-52"
                                    transform="translate(396.5546875, 1209.0546875)">
                                    <circle style="" rx="0" ry="0" r="73.9453125" width="147.890625" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-66.4453125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="132.890625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Analyse Tendances</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-J-54"
                                    transform="translate(396.5546875, 1407.734375)">
                                    <circle style="" rx="0" ry="0" r="74.734375" width="149.46875" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-67.234375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="134.46875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Génération Alertes</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-K-56"
                                    transform="translate(396.5546875, 1588.484375)">
                                    <circle style="" rx="0" ry="0" r="56.015625" width="112.03125" height="40.59375">
                                    </circle>
                                    <g class="label" style="" transform="translate(-48.515625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="97.03125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Prédiction ML</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg></div>
            <div class="figure-caption">Figure 4.2 : Diagramme de cas d'utilisation - Administration système</div>
        </div>

        <h3>4.2 Diagrammes de classes</h3>
        <p>Le diagramme de classes suivant représente les principales entités du système CloudCapacity et leurs
            relations.</p>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378258" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 666.52734375px;" viewBox="0 0 666.52734375 1160" role="graphics-document document"
                    aria-roledescription="classDiagram">
                    <style>
                        #mermaid-1746403378258 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378258 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378258 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378258 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378258 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378258 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378258 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378258 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378258 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378258 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378258 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378258 g.classGroup text {
                            fill: #999;
                            stroke: none;
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 10px;
                        }

                        #mermaid-1746403378258 g.classGroup text .title {
                            font-weight: bolder;
                        }

                        #mermaid-1746403378258 .nodeLabel,
                        #mermaid-1746403378258 .edgeLabel {
                            color: #111111;
                        }

                        #mermaid-1746403378258 .edgeLabel .label rect {
                            fill: #eee;
                        }

                        #mermaid-1746403378258 .label text {
                            fill: #111111;
                        }

                        #mermaid-1746403378258 .edgeLabel .label span {
                            background: #eee;
                        }

                        #mermaid-1746403378258 .classTitle {
                            font-weight: bolder;
                        }

                        #mermaid-1746403378258 .node rect,
                        #mermaid-1746403378258 .node circle,
                        #mermaid-1746403378258 .node ellipse,
                        #mermaid-1746403378258 .node polygon,
                        #mermaid-1746403378258 .node path {
                            fill: #eee;
                            stroke: #999;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378258 .divider {
                            stroke: #999;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 g.clickable {
                            cursor: pointer;
                        }

                        #mermaid-1746403378258 g.classGroup rect {
                            fill: #eee;
                            stroke: #999;
                        }

                        #mermaid-1746403378258 g.classGroup line {
                            stroke: #999;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 .classLabel .box {
                            stroke: none;
                            stroke-width: 0;
                            fill: #eee;
                            opacity: 0.5;
                        }

                        #mermaid-1746403378258 .classLabel .label {
                            fill: #999;
                            font-size: 10px;
                        }

                        #mermaid-1746403378258 .relation {
                            stroke: #666;
                            stroke-width: 1;
                            fill: none;
                        }

                        #mermaid-1746403378258 .dashed-line {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378258 .dotted-line {
                            stroke-dasharray: 1 2;
                        }

                        #mermaid-1746403378258 #compositionStart,
                        #mermaid-1746403378258 .composition {
                            fill: #666 !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #compositionEnd,
                        #mermaid-1746403378258 .composition {
                            fill: #666 !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #dependencyStart,
                        #mermaid-1746403378258 .dependency {
                            fill: #666 !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #dependencyStart,
                        #mermaid-1746403378258 .dependency {
                            fill: #666 !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #extensionStart,
                        #mermaid-1746403378258 .extension {
                            fill: transparent !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #extensionEnd,
                        #mermaid-1746403378258 .extension {
                            fill: transparent !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #aggregationStart,
                        #mermaid-1746403378258 .aggregation {
                            fill: transparent !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #aggregationEnd,
                        #mermaid-1746403378258 .aggregation {
                            fill: transparent !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #lollipopStart,
                        #mermaid-1746403378258 .lollipop {
                            fill: #eee !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 #lollipopEnd,
                        #mermaid-1746403378258 .lollipop {
                            fill: #eee !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378258 .edgeTerminals {
                            font-size: 11px;
                        }

                        #mermaid-1746403378258 .classTitleText {
                            text-anchor: middle;
                            font-size: 18px;
                            fill: #000000;
                        }

                        #mermaid-1746403378258 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-aggregationStart"
                                class="marker aggregation classDiagram" refX="18" refY="7" markerWidth="190"
                                markerHeight="240" orient="auto">
                                <path d="M 18,7 L9,13 L1,7 L9,1 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-aggregationEnd"
                                class="marker aggregation classDiagram" refX="1" refY="7" markerWidth="20"
                                markerHeight="28" orient="auto">
                                <path d="M 18,7 L9,13 L1,7 L9,1 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-extensionStart"
                                class="marker extension classDiagram" refX="18" refY="7" markerWidth="190"
                                markerHeight="240" orient="auto">
                                <path d="M 1,7 L18,13 V 1 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-extensionEnd"
                                class="marker extension classDiagram" refX="1" refY="7" markerWidth="20"
                                markerHeight="28" orient="auto">
                                <path d="M 1,1 V 13 L18,7 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-compositionStart"
                                class="marker composition classDiagram" refX="18" refY="7" markerWidth="190"
                                markerHeight="240" orient="auto">
                                <path d="M 18,7 L9,13 L1,7 L9,1 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-compositionEnd"
                                class="marker composition classDiagram" refX="1" refY="7" markerWidth="20"
                                markerHeight="28" orient="auto">
                                <path d="M 18,7 L9,13 L1,7 L9,1 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-dependencyStart"
                                class="marker dependency classDiagram" refX="6" refY="7" markerWidth="190"
                                markerHeight="240" orient="auto">
                                <path d="M 5,7 L9,13 L1,7 L9,1 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-dependencyEnd"
                                class="marker dependency classDiagram" refX="13" refY="7" markerWidth="20"
                                markerHeight="28" orient="auto">
                                <path d="M 18,7 L9,13 L14,7 L9,1 Z"></path>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-lollipopStart"
                                class="marker lollipop classDiagram" refX="13" refY="7" markerWidth="190"
                                markerHeight="240" orient="auto">
                                <circle stroke="black" fill="transparent" cx="7" cy="7" r="6"></circle>
                            </marker>
                        </defs>
                        <defs>
                            <marker id="mermaid-1746403378258_classDiagram-lollipopEnd"
                                class="marker lollipop classDiagram" refX="1" refY="7" markerWidth="190"
                                markerHeight="240" orient="auto">
                                <circle stroke="black" fill="transparent" cx="7" cy="7" r="6"></circle>
                            </marker>
                        </defs>
                        <g class="root">
                            <g class="clusters"></g>
                            <g class="edgePaths">
                                <path
                                    d="M153.227,333.471L142.634,350.546C132.042,367.621,110.857,401.772,100.264,427.612C89.672,453.453,89.672,470.984,89.672,479.75L89.672,488.516"
                                    id="id1" class="  edge-pattern-solid relation" style="fill:none"></path>
                                <path
                                    d="M89.672,760.266L89.672,769.031C89.672,777.797,89.672,795.328,89.672,810.393C89.672,825.458,89.672,838.057,89.672,844.357L89.672,850.656"
                                    id="id2" class="  edge-pattern-solid relation" style="fill:none"></path>
                                <path
                                    d="M234.125,398.125L234.125,404.424C234.125,410.724,234.125,423.323,238.923,438.388C243.722,453.453,253.318,470.984,258.117,479.75L262.915,488.516"
                                    id="id3" class="  edge-pattern-solid relation" style="fill:none"></path>
                                <path
                                    d="M440.785,338.938L433.624,355.102C426.462,371.266,412.139,403.594,402.163,428.523C392.187,453.453,386.557,470.984,383.742,479.75L380.927,488.516"
                                    id="id4" class="  edge-pattern-solid relation" style="fill:none"></path>
                                <path
                                    d="M315.023,259.526L357.145,288.926C399.267,318.325,483.51,377.123,525.632,412.822C567.754,448.521,567.754,461.12,567.754,467.419L567.754,473.719"
                                    id="id5" class="  edge-pattern-solid relation" style="fill:none"></path>
                            </g>
                            <g class="edgeLabels">
                                <g class="edgeLabel" transform="translate(89.671875, 435.921875)">
                                    <g class="label" transform="translate(-11.8125, -12.796875)">
                                        <foreignobject width="23.625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"><span class="edgeLabel">has</span></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(131.25484363620745, 340.43504017957406)">
                                    <g class="inner" transform="translate(0, 0)">
                                        <foreignobject style="width: 9px; height: 12px;">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">1</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(99.67187749999984, 466.01562964285745)">
                                    <g class="inner" transform="translate(0, 0)"></g>
                                    <foreignobject style="width: 9px; height: 12px;">
                                        <div xmlns="http://www.w3.org/1999/xhtml"
                                            style="display: inline-block; white-space: nowrap;"><span
                                                class="edgeLabel">n</span></div>
                                    </foreignobject>
                                </g>
                                <g class="edgeLabel" transform="translate(89.671875, 812.859375)">
                                    <g class="label" transform="translate(-27.3046875, -12.796875)">
                                        <foreignobject width="54.609375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"><span class="edgeLabel">triggers</span></span>
                                            </div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(74.67187750000015, 777.7656296428568)">
                                    <g class="inner" transform="translate(0, 0)">
                                        <foreignobject style="width: 9px; height: 12px;">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">1</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(99.67187749999984, 828.1562521428572)">
                                    <g class="inner" transform="translate(0, 0)"></g>
                                    <foreignobject style="width: 9px; height: 12px;">
                                        <div xmlns="http://www.w3.org/1999/xhtml"
                                            style="display: inline-block; white-space: nowrap;"><span
                                                class="edgeLabel">n</span></div>
                                    </foreignobject>
                                </g>
                                <g class="edgeLabel" transform="translate(234.125, 435.921875)">
                                    <g class="label" transform="translate(-48.484375, -12.796875)">
                                        <foreignobject width="96.96875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"><span class="edgeLabel">referenced
                                                        in</span></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(219.125, 415.625)">
                                    <g class="inner" transform="translate(0, 0)">
                                        <foreignobject style="width: 9px; height: 12px;">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">1</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(262.66959259174695, 460.9625177636931)">
                                    <g class="inner" transform="translate(0, 0)"></g>
                                    <foreignobject style="width: 9px; height: 12px;">
                                        <div xmlns="http://www.w3.org/1999/xhtml"
                                            style="display: inline-block; white-space: nowrap;"><span
                                                class="edgeLabel">n</span></div>
                                    </foreignobject>
                                </g>
                                <g class="edgeLabel" transform="translate(397.81640625, 435.921875)">
                                    <g class="label" transform="translate(-35.203125, -12.796875)">
                                        <foreignobject width="70.40625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"><span class="edgeLabel">generates</span></span>
                                            </div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(419.9821395289943, 348.86139290611953)">
                                    <g class="inner" transform="translate(0, 0)">
                                        <foreignobject style="width: 9px; height: 12px;">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">1</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(395.55919264867345, 471.43999102789417)">
                                    <g class="inner" transform="translate(0, 0)"></g>
                                    <foreignobject style="width: 9px; height: 12px;">
                                        <div xmlns="http://www.w3.org/1999/xhtml"
                                            style="display: inline-block; white-space: nowrap;"><span
                                                class="edgeLabel">n</span></div>
                                    </foreignobject>
                                </g>
                                <g class="edgeLabel" transform="translate(567.75390625, 435.921875)">
                                    <g class="label" transform="translate(-54.2109375, -12.796875)">
                                        <foreignobject width="108.421875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"><span class="edgeLabel">has
                                                        predictions</span></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(320.78865913519223, 281.8424961173656)">
                                    <g class="inner" transform="translate(0, 0)">
                                        <foreignobject style="width: 9px; height: 12px;">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">1</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeTerminals" transform="translate(577.7539081249998, 451.21875160714285)">
                                    <g class="inner" transform="translate(0, 0)"></g>
                                    <foreignobject style="width: 9px; height: 12px;">
                                        <div xmlns="http://www.w3.org/1999/xhtml"
                                            style="display: inline-block; white-space: nowrap;"><span
                                                class="edgeLabel">n</span></div>
                                    </foreignobject>
                                </g>
                            </g>
                            <g class="nodes">
                                <g class="node default" id="classId-Server-0" transform="translate(234.125, 203.0625)">
                                    <rect class="outer title-state" x="-80.8984375" y="-195.0625" width="161.796875"
                                        height="390.125"></rect>
                                    <line class="divider" x1="-80.8984375" x2="80.8984375" y1="-157.46875"
                                        y2="-157.46875"></line>
                                    <line class="divider" x1="-80.8984375" x2="80.8984375" y1="124.875" y2="124.875">
                                    </line>
                                    <g class="label">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel"></span></div>
                                        </foreignobject>
                                        <foreignobject class="classTitle" width="48.6875" height="25.59375"
                                            transform="translate( -24.34375, -187.5625)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Server</span></div>
                                        </foreignobject>
                                        <foreignobject width="46.34375" height="25.59375"
                                            transform="translate( -73.3984375, -145.96875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int id</span></div>
                                        </foreignobject>
                                        <foreignobject width="92.75" height="25.59375"
                                            transform="translate( -73.3984375, -116.375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string name</span></div>
                                        </foreignobject>
                                        <foreignobject width="67.0625" height="25.59375"
                                            transform="translate( -73.3984375, -86.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string ip</span></div>
                                        </foreignobject>
                                        <foreignobject width="85.46875" height="25.59375"
                                            transform="translate( -73.3984375, -57.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string type</span></div>
                                        </foreignobject>
                                        <foreignobject width="68.65625" height="25.59375"
                                            transform="translate( -73.3984375, -27.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string os</span></div>
                                        </foreignobject>
                                        <foreignobject width="104.765625" height="25.59375"
                                            transform="translate( -73.3984375, 2)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int cpu_cores</span></div>
                                        </foreignobject>
                                        <foreignobject width="86.109375" height="25.59375"
                                            transform="translate( -73.3984375, 31.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int ram_gb</span></div>
                                        </foreignobject>
                                        <foreignobject width="86.21875" height="25.59375"
                                            transform="translate( -73.3984375, 61.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int disk_gb</span></div>
                                        </foreignobject>
                                        <foreignobject width="146.796875" height="25.59375"
                                            transform="translate( -73.3984375, 90.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+Date creation_date</span></div>
                                        </foreignobject>
                                        <foreignobject width="94.84375" height="25.59375"
                                            transform="translate( -73.3984375, 132.375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+getMetrics()</span></div>
                                        </foreignobject>
                                        <foreignobject width="93.796875" height="25.59375"
                                            transform="translate( -73.3984375, 161.96875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+getHistory()</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default" id="classId-Metric-1"
                                    transform="translate(89.671875, 624.390625)">
                                    <rect class="outer title-state" x="-68.921875" y="-135.875" width="137.84375"
                                        height="271.75"></rect>
                                    <line class="divider" x1="-68.921875" x2="68.921875" y1="-98.28125" y2="-98.28125">
                                    </line>
                                    <line class="divider" x1="-68.921875" x2="68.921875" y1="95.28125" y2="95.28125">
                                    </line>
                                    <g class="label">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel"></span></div>
                                        </foreignobject>
                                        <foreignobject class="classTitle" width="47.265625" height="25.59375"
                                            transform="translate( -23.6328125, -128.375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Metric</span></div>
                                        </foreignobject>
                                        <foreignobject width="46.34375" height="25.59375"
                                            transform="translate( -61.421875, -86.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int id</span></div>
                                        </foreignobject>
                                        <foreignobject width="98.9375" height="25.59375"
                                            transform="translate( -61.421875, -57.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int server_id</span></div>
                                        </foreignobject>
                                        <foreignobject width="92.75" height="25.59375"
                                            transform="translate( -61.421875, -27.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string name</span></div>
                                        </foreignobject>
                                        <foreignobject width="85.609375" height="25.59375"
                                            transform="translate( -61.421875, 2)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+float value</span></div>
                                        </foreignobject>
                                        <foreignobject width="122.84375" height="25.59375"
                                            transform="translate( -61.421875, 31.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+Date timestamp</span></div>
                                        </foreignobject>
                                        <foreignobject width="81.984375" height="25.59375"
                                            transform="translate( -61.421875, 61.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string unit</span></div>
                                        </foreignobject>
                                        <foreignobject width="74.625" height="25.59375"
                                            transform="translate( -61.421875, 102.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+analyze()</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default" id="classId-Alert-2"
                                    transform="translate(89.671875, 1001.328125)">
                                    <rect class="outer title-state" x="-81.671875" y="-150.671875" width="163.34375"
                                        height="301.34375"></rect>
                                    <line class="divider" x1="-81.671875" x2="81.671875" y1="-113.078125"
                                        y2="-113.078125"></line>
                                    <line class="divider" x1="-81.671875" x2="81.671875" y1="80.484375" y2="80.484375">
                                    </line>
                                    <g class="label">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel"></span></div>
                                        </foreignobject>
                                        <foreignobject class="classTitle" width="37.234375" height="25.59375"
                                            transform="translate( -18.6171875, -143.171875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Alert</span></div>
                                        </foreignobject>
                                        <foreignobject width="46.34375" height="25.59375"
                                            transform="translate( -74.171875, -101.578125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int id</span></div>
                                        </foreignobject>
                                        <foreignobject width="101.78125" height="25.59375"
                                            transform="translate( -74.171875, -71.984375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int metric_id</span></div>
                                        </foreignobject>
                                        <foreignobject width="88.3125" height="25.59375"
                                            transform="translate( -74.171875, -42.390625)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string level</span></div>
                                        </foreignobject>
                                        <foreignobject width="113.71875" height="25.59375"
                                            transform="translate( -74.171875, -12.796875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string message</span></div>
                                        </foreignobject>
                                        <foreignobject width="122.84375" height="25.59375"
                                            transform="translate( -74.171875, 16.796875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+Date timestamp</span></div>
                                        </foreignobject>
                                        <foreignobject width="145.6875" height="25.59375"
                                            transform="translate( -74.171875, 46.390625)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+bool acknowledged</span></div>
                                        </foreignobject>
                                        <foreignobject width="148.34375" height="25.59375"
                                            transform="translate( -74.171875, 87.984375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+acknowledgeAlert()</span></div>
                                        </foreignobject>
                                        <foreignobject width="92.015625" height="25.59375"
                                            transform="translate( -74.171875, 117.578125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+closeAlert()</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default" id="classId-Report-3"
                                    transform="translate(337.29296875, 624.390625)">
                                    <rect class="outer title-state" x="-89.6875" y="-135.875" width="179.375"
                                        height="271.75"></rect>
                                    <line class="divider" x1="-89.6875" x2="89.6875" y1="-98.28125" y2="-98.28125">
                                    </line>
                                    <line class="divider" x1="-89.6875" x2="89.6875" y1="65.6875" y2="65.6875"></line>
                                    <g class="label">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel"></span></div>
                                        </foreignobject>
                                        <foreignobject class="classTitle" width="50.53125" height="25.59375"
                                            transform="translate( -25.265625, -128.375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Report</span></div>
                                        </foreignobject>
                                        <foreignobject width="46.34375" height="25.59375"
                                            transform="translate( -82.1875, -86.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int id</span></div>
                                        </foreignobject>
                                        <foreignobject width="92.75" height="25.59375"
                                            transform="translate( -82.1875, -57.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string name</span></div>
                                        </foreignobject>
                                        <foreignobject width="164.375" height="25.59375"
                                            transform="translate( -82.1875, -27.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+Date generation_date</span></div>
                                        </foreignobject>
                                        <foreignobject width="102.34375" height="25.59375"
                                            transform="translate( -82.1875, 2)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string format</span></div>
                                        </foreignobject>
                                        <foreignobject width="136.53125" height="25.59375"
                                            transform="translate( -82.1875, 31.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string[] recipients</span></div>
                                        </foreignobject>
                                        <foreignobject width="111.203125" height="25.59375"
                                            transform="translate( -82.1875, 73.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+generatePDF()</span></div>
                                        </foreignobject>
                                        <foreignobject width="109.484375" height="25.59375"
                                            transform="translate( -82.1875, 102.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+sendByEmail()</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default" id="classId-User-4" transform="translate(500.984375, 203.0625)">
                                    <rect class="outer title-state" x="-75.4375" y="-135.875" width="150.875"
                                        height="271.75"></rect>
                                    <line class="divider" x1="-75.4375" x2="75.4375" y1="-98.28125" y2="-98.28125">
                                    </line>
                                    <line class="divider" x1="-75.4375" x2="75.4375" y1="65.6875" y2="65.6875"></line>
                                    <g class="label">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel"></span></div>
                                        </foreignobject>
                                        <foreignobject class="classTitle" width="33.765625" height="25.59375"
                                            transform="translate( -16.8828125, -128.375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">User</span></div>
                                        </foreignobject>
                                        <foreignobject width="46.34375" height="25.59375"
                                            transform="translate( -67.9375, -86.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int id</span></div>
                                        </foreignobject>
                                        <foreignobject width="122.90625" height="25.59375"
                                            transform="translate( -67.9375, -57.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string username</span></div>
                                        </foreignobject>
                                        <foreignobject width="93.28125" height="25.59375"
                                            transform="translate( -67.9375, -27.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string email</span></div>
                                        </foreignobject>
                                        <foreignobject width="81.84375" height="25.59375"
                                            transform="translate( -67.9375, 2)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string role</span></div>
                                        </foreignobject>
                                        <foreignobject width="115.484375" height="25.59375"
                                            transform="translate( -67.9375, 31.59375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+Date last_login</span></div>
                                        </foreignobject>
                                        <foreignobject width="135.875" height="25.59375"
                                            transform="translate( -67.9375, 73.1875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+changePassword()</span></div>
                                        </foreignobject>
                                        <foreignobject width="117.09375" height="25.59375"
                                            transform="translate( -67.9375, 102.78125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+updateProfile()</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default" id="classId-Prediction-5"
                                    transform="translate(567.75390625, 624.390625)">
                                    <rect class="outer title-state" x="-90.7734375" y="-150.671875" width="181.546875"
                                        height="301.34375"></rect>
                                    <line class="divider" x1="-90.7734375" x2="90.7734375" y1="-113.078125"
                                        y2="-113.078125"></line>
                                    <line class="divider" x1="-90.7734375" x2="90.7734375" y1="110.078125"
                                        y2="110.078125"></line>
                                    <g class="label">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel"></span></div>
                                        </foreignobject>
                                        <foreignobject class="classTitle" width="77.296875" height="25.59375"
                                            transform="translate( -38.6484375, -143.171875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Prediction</span></div>
                                        </foreignobject>
                                        <foreignobject width="46.34375" height="25.59375"
                                            transform="translate( -83.2734375, -101.578125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int id</span></div>
                                        </foreignobject>
                                        <foreignobject width="98.9375" height="25.59375"
                                            transform="translate( -83.2734375, -71.984375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+int server_id</span></div>
                                        </foreignobject>
                                        <foreignobject width="148.1875" height="25.59375"
                                            transform="translate( -83.2734375, -42.390625)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+string metric_name</span></div>
                                        </foreignobject>
                                        <foreignobject width="163.25" height="25.59375"
                                            transform="translate( -83.2734375, -12.796875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+float predicted_value</span></div>
                                        </foreignobject>
                                        <foreignobject width="160.78125" height="25.59375"
                                            transform="translate( -83.2734375, 16.796875)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+Date prediction_date</span></div>
                                        </foreignobject>
                                        <foreignobject width="131.359375" height="25.59375"
                                            transform="translate( -83.2734375, 46.390625)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+Date target_date</span></div>
                                        </foreignobject>
                                        <foreignobject width="125.9375" height="25.59375"
                                            transform="translate( -83.2734375, 75.984375)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+float confidence</span></div>
                                        </foreignobject>
                                        <foreignobject width="166.546875" height="25.59375"
                                            transform="translate( -83.2734375, 117.578125)">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">+getPredictionHistory()</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg></div>
            <div class="figure-caption">Figure 4.3 : Diagramme de classes - Modèle de données principal</div>
        </div>

        <h3>4.3 Diagrammes de séquences</h3>
        <p>Les diagrammes de séquence suivants illustrent les interactions entre les différents composants du système
            lors de l'exécution de certaines fonctionnalités clés.</p>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378321" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 1401px;" viewBox="-50 -10 1401 699" role="graphics-document document"
                    aria-roledescription="sequence">
                    <g>
                        <rect x="1151" y="613" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="1226" y="645.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="1226" dy="0">Service Email</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="951" y="613" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="1026" y="645.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="1026" dy="0">Service de Rapport</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="751" y="613" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="826" y="645.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="826" dy="0">Base de données</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="496" y="613" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="571" y="645.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="571" dy="0">API Backend</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="255" y="613" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="330" y="645.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="330" dy="0">Interface Utilisateur</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="0" y="613" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="75" y="645.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="75" dy="0">Utilisateur</tspan>
                        </text>
                    </g>
                    <g>
                        <line id="actor5" x1="1226" y1="5" x2="1226" y2="613" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-5">
                            <rect x="1151" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="1226" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="1226" dy="0">Service Email</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor4" x1="1026" y1="5" x2="1026" y2="613" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-4">
                            <rect x="951" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="1026" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="1026" dy="0">Service de Rapport</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor3" x1="826" y1="5" x2="826" y2="613" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-3">
                            <rect x="751" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="826" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="826" dy="0">Base de données</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor2" x1="571" y1="5" x2="571" y2="613" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-2">
                            <rect x="496" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="571" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="571" dy="0">API Backend</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor1" x1="330" y1="5" x2="330" y2="613" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-1">
                            <rect x="255" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="330" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="330" dy="0">Interface Utilisateur</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor0" x1="75" y1="5" x2="75" y2="613" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-0">
                            <rect x="0" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="75" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="75" dy="0">Utilisateur</tspan>
                            </text>
                        </g>
                    </g>
                    <style>
                        #mermaid-1746403378321 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378321 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378321 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378321 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378321 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378321 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378321 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378321 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378321 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378321 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378321 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378321 .actor {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                        }

                        #mermaid-1746403378321 text.actor&gt;

                        tspan {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378321 .actor-line {
                            stroke: #666;
                        }

                        #mermaid-1746403378321 .messageLine0 {
                            stroke-width: 1.5;
                            stroke-dasharray: none;
                            stroke: #333;
                        }

                        #mermaid-1746403378321 .messageLine1 {
                            stroke-width: 1.5;
                            stroke-dasharray: 2, 2;
                            stroke: #333;
                        }

                        #mermaid-1746403378321 #arrowhead path {
                            fill: #333;
                            stroke: #333;
                        }

                        #mermaid-1746403378321 .sequenceNumber {
                            fill: white;
                        }

                        #mermaid-1746403378321 #sequencenumber {
                            fill: #333;
                        }

                        #mermaid-1746403378321 #crosshead path {
                            fill: #333;
                            stroke: #333;
                        }

                        #mermaid-1746403378321 .messageText {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378321 .labelBox {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                        }

                        #mermaid-1746403378321 .labelText,
                        #mermaid-1746403378321 .labelText&gt;

                        tspan {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378321 .loopText,
                        #mermaid-1746403378321 .loopText&gt;

                        tspan {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378321 .loopLine {
                            stroke-width: 2px;
                            stroke-dasharray: 2, 2;
                            stroke: hsl(0, 0%, 83%);
                            fill: hsl(0, 0%, 83%);
                        }

                        #mermaid-1746403378321 .note {
                            stroke: #999;
                            fill: #666;
                        }

                        #mermaid-1746403378321 .noteText,
                        #mermaid-1746403378321 .noteText&gt;

                        tspan {
                            fill: #fff;
                            stroke: none;
                        }

                        #mermaid-1746403378321 .activation0 {
                            fill: #f4f4f4;
                            stroke: #666;
                        }

                        #mermaid-1746403378321 .activation1 {
                            fill: #f4f4f4;
                            stroke: #666;
                        }

                        #mermaid-1746403378321 .activation2 {
                            fill: #f4f4f4;
                            stroke: #666;
                        }

                        #mermaid-1746403378321 .actorPopupMenu {
                            position: absolute;
                        }

                        #mermaid-1746403378321 .actorPopupMenuPanel {
                            position: absolute;
                            fill: #eee;
                            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
                            filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
                        }

                        #mermaid-1746403378321 .actor-man line {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                        }

                        #mermaid-1746403378321 .actor-man circle,
                        #mermaid-1746403378321 line {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378321 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g></g>
                    <defs>
                        <symbol id="computer" width="24" height="24">
                            <path transform="scale(.5)"
                                d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z">
                            </path>
                        </symbol>
                    </defs>
                    <defs>
                        <symbol id="database" fill-rule="evenodd" clip-rule="evenodd">
                            <path transform="scale(.5)"
                                d="M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z">
                            </path>
                        </symbol>
                    </defs>
                    <defs>
                        <symbol id="clock" width="24" height="24">
                            <path transform="scale(.5)"
                                d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z">
                            </path>
                        </symbol>
                    </defs>
                    <defs>
                        <marker id="arrowhead" refX="7.9" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 0 L 10 5 L 0 10 z"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="crosshead" markerWidth="15" markerHeight="8" orient="auto" refX="4" refY="4.5">
                            <path fill="none" stroke="#000000" stroke-width="1pt" d="M 1,2 L 6,7 M 6,2 L 1,7"
                                style="stroke-dasharray: 0, 0;"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="filled-head" refX="15.5" refY="7" markerWidth="20" markerHeight="28" orient="auto">
                            <path d="M 18,7 L9,13 L14,7 L9,1 Z"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="sequencenumber" refX="15" refY="15" markerWidth="60" markerHeight="40"
                            orient="auto">
                            <circle cx="15" cy="15" r="6"></circle>
                        </marker>
                    </defs><text x="201" y="80" text-anchor="middle" dominant-baseline="middle"
                        alignment-baseline="middle" class="messageText" dy="1em"
                        style="font-size: 16px; font-weight: 400;">Demande génération rapport</text>
                    <line x1="76" y1="109" x2="326" y2="109" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="449" y="124"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">POST /reports/generate</text>
                    <line x1="331" y1="153" x2="567" y2="153" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="697" y="168"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Récupère données métriques</text>
                    <line x1="572" y1="197" x2="822" y2="197" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="700" y="212"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Retourne données</text>
                    <line x1="825" y1="241" x2="575" y2="241" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="797"
                        y="256" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Génère rapport
                        PDF/Excel</text>
                    <line x1="572" y1="285" x2="1022" y2="285" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="800" y="300"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Rapport généré</text>
                    <line x1="1025" y1="329" x2="575" y2="329" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="697"
                        y="344" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Enregistre
                        rapport</text>
                    <line x1="572" y1="373" x2="822" y2="373" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="700" y="388"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Confirmation</text>
                    <line x1="825" y1="417" x2="575" y2="417" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="897"
                        y="432" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Envoie rapport par
                        email</text>
                    <line x1="572" y1="461" x2="1222" y2="461" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="900" y="476"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Confirmation envoi</text>
                    <line x1="1225" y1="505" x2="575" y2="505" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="452"
                        y="520" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Retourne statut
                        génération</text>
                    <line x1="570" y1="549" x2="334" y2="549" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="204"
                        y="564" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Affiche confirmation
                        succès</text>
                    <line x1="329" y1="593" x2="79" y2="593" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line>
                </svg></div>
            <div class="figure-caption">Figure 4.4 : Diagramme de séquence - Génération de rapport</div>
        </div>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378356" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 1200.5px;" viewBox="-50 -10 1200.5 814" role="graphics-document document"
                    aria-roledescription="sequence">
                    <g>
                        <rect x="938" y="728" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="1013" y="760.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="1013" dy="0">Service d'Alerte</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="738" y="728" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="813" y="760.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="813" dy="0">Base de données</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="476" y="728" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="551" y="760.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="551" dy="0">API Backend</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="200" y="728" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="275" y="760.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="275" dy="0">Module ML</tspan>
                        </text>
                    </g>
                    <g>
                        <rect x="0" y="728" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                            class="actor"></rect><text x="75" y="760.5" dominant-baseline="central"
                            alignment-baseline="central" class="actor"
                            style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                            <tspan x="75" dy="0">Système</tspan>
                        </text>
                    </g>
                    <g>
                        <line id="actor10" x1="1013" y1="5" x2="1013" y2="728" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-10">
                            <rect x="938" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="1013" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="1013" dy="0">Service d'Alerte</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor9" x1="813" y1="5" x2="813" y2="728" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-9">
                            <rect x="738" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="813" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="813" dy="0">Base de données</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor8" x1="551" y1="5" x2="551" y2="728" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-8">
                            <rect x="476" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="551" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="551" dy="0">API Backend</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor7" x1="275" y1="5" x2="275" y2="728" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-7">
                            <rect x="200" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="275" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="275" dy="0">Module ML</tspan>
                            </text>
                        </g>
                    </g>
                    <g>
                        <line id="actor6" x1="75" y1="5" x2="75" y2="728" class="200" stroke-width="0.5px"
                            stroke="#999"></line>
                        <g id="root-6">
                            <rect x="0" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3"
                                class="actor"></rect><text x="75" y="32.5" dominant-baseline="central"
                                alignment-baseline="central" class="actor"
                                style="text-anchor: middle; font-size: 16px; font-weight: 400;">
                                <tspan x="75" dy="0">Système</tspan>
                            </text>
                        </g>
                    </g>
                    <style>
                        #mermaid-1746403378356 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378356 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378356 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378356 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378356 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378356 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378356 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378356 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378356 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378356 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378356 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378356 .actor {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                        }

                        #mermaid-1746403378356 text.actor&gt;

                        tspan {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378356 .actor-line {
                            stroke: #666;
                        }

                        #mermaid-1746403378356 .messageLine0 {
                            stroke-width: 1.5;
                            stroke-dasharray: none;
                            stroke: #333;
                        }

                        #mermaid-1746403378356 .messageLine1 {
                            stroke-width: 1.5;
                            stroke-dasharray: 2, 2;
                            stroke: #333;
                        }

                        #mermaid-1746403378356 #arrowhead path {
                            fill: #333;
                            stroke: #333;
                        }

                        #mermaid-1746403378356 .sequenceNumber {
                            fill: white;
                        }

                        #mermaid-1746403378356 #sequencenumber {
                            fill: #333;
                        }

                        #mermaid-1746403378356 #crosshead path {
                            fill: #333;
                            stroke: #333;
                        }

                        #mermaid-1746403378356 .messageText {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378356 .labelBox {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                        }

                        #mermaid-1746403378356 .labelText,
                        #mermaid-1746403378356 .labelText&gt;

                        tspan {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378356 .loopText,
                        #mermaid-1746403378356 .loopText&gt;

                        tspan {
                            fill: #333;
                            stroke: none;
                        }

                        #mermaid-1746403378356 .loopLine {
                            stroke-width: 2px;
                            stroke-dasharray: 2, 2;
                            stroke: hsl(0, 0%, 83%);
                            fill: hsl(0, 0%, 83%);
                        }

                        #mermaid-1746403378356 .note {
                            stroke: #999;
                            fill: #666;
                        }

                        #mermaid-1746403378356 .noteText,
                        #mermaid-1746403378356 .noteText&gt;

                        tspan {
                            fill: #fff;
                            stroke: none;
                        }

                        #mermaid-1746403378356 .activation0 {
                            fill: #f4f4f4;
                            stroke: #666;
                        }

                        #mermaid-1746403378356 .activation1 {
                            fill: #f4f4f4;
                            stroke: #666;
                        }

                        #mermaid-1746403378356 .activation2 {
                            fill: #f4f4f4;
                            stroke: #666;
                        }

                        #mermaid-1746403378356 .actorPopupMenu {
                            position: absolute;
                        }

                        #mermaid-1746403378356 .actorPopupMenuPanel {
                            position: absolute;
                            fill: #eee;
                            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
                            filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
                        }

                        #mermaid-1746403378356 .actor-man line {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                        }

                        #mermaid-1746403378356 .actor-man circle,
                        #mermaid-1746403378356 line {
                            stroke: hsl(0, 0%, 83%);
                            fill: #eee;
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378356 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g></g>
                    <defs>
                        <symbol id="computer" width="24" height="24">
                            <path transform="scale(.5)"
                                d="M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z">
                            </path>
                        </symbol>
                    </defs>
                    <defs>
                        <symbol id="database" fill-rule="evenodd" clip-rule="evenodd">
                            <path transform="scale(.5)"
                                d="M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z">
                            </path>
                        </symbol>
                    </defs>
                    <defs>
                        <symbol id="clock" width="24" height="24">
                            <path transform="scale(.5)"
                                d="M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z">
                            </path>
                        </symbol>
                    </defs>
                    <defs>
                        <marker id="arrowhead" refX="7.9" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 0 L 10 5 L 0 10 z"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="crosshead" markerWidth="15" markerHeight="8" orient="auto" refX="4" refY="4.5">
                            <path fill="none" stroke="#000000" stroke-width="1pt" d="M 1,2 L 6,7 M 6,2 L 1,7"
                                style="stroke-dasharray: 0, 0;"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="filled-head" refX="15.5" refY="7" markerWidth="20" markerHeight="28" orient="auto">
                            <path d="M 18,7 L9,13 L14,7 L9,1 Z"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="sequencenumber" refX="15" refY="15" markerWidth="60" markerHeight="40"
                            orient="auto">
                            <circle cx="15" cy="15" r="6"></circle>
                        </marker>
                    </defs>
                    <g>
                        <line x1="540" y1="457" x2="1100.5" y2="457" class="loopLine"></line>
                        <line x1="1100.5" y1="457" x2="1100.5" y2="664" class="loopLine"></line>
                        <line x1="540" y1="664" x2="1100.5" y2="664" class="loopLine"></line>
                        <line x1="540" y1="457" x2="540" y2="664" class="loopLine"></line>
                        <polygon points="540,457 590,457 590,470 581.6,477 540,477" class="labelBox"></polygon><text
                            x="565" y="470" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                            class="labelText" style="font-size: 16px; font-weight: 400;">alt</text><text x="845.25"
                            y="475" text-anchor="middle" class="loopText" style="font-size: 16px; font-weight: 400;">
                            <tspan x="845.25">[Risque de surcharge détecté]</tspan>
                        </text>
                    </g><text x="312" y="80" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Déclenche analyse
                        prédictive</text>
                    <line x1="76" y1="109" x2="547" y2="109" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="681" y="124"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Récupère données historiques</text>
                    <line x1="552" y1="153" x2="809" y2="153" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="684" y="168"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Retourne données</text>
                    <line x1="812" y1="197" x2="555" y2="197" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="415"
                        y="212" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Envoie données pour
                        prédiction</text>
                    <line x1="550" y1="241" x2="279" y2="241" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="276" y="256"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Analyse tendances et prédit
                        surcharges</text>
                    <path d="M 276,285 C 336,275 336,315 276,305" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></path><text x="412" y="330"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Retourne prédictions</text>
                    <line x1="276" y1="359" x2="547" y2="359" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="681"
                        y="374" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Enregistre
                        prédictions</text>
                    <line x1="552" y1="403" x2="809" y2="403" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="684" y="418"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Confirmation</text>
                    <line x1="812" y1="447" x2="555" y2="447" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="781"
                        y="507" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Crée alerte
                        prédictive</text>
                    <line x1="552" y1="536" x2="1009" y2="536" class="messageLine0" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="fill: none;"></line><text x="1014" y="551"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Détermine destinataires</text>
                    <path d="M 1014,580 C 1074,570 1074,610 1014,600" class="messageLine0" stroke-width="2"
                        stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></path><text x="784" y="625"
                        text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText"
                        dy="1em" style="font-size: 16px; font-weight: 400;">Confirmation alerte</text>
                    <line x1="1012" y1="654" x2="555" y2="654" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line><text x="315"
                        y="679" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle"
                        class="messageText" dy="1em" style="font-size: 16px; font-weight: 400;">Retourne résultats
                        analyse</text>
                    <line x1="550" y1="708" x2="79" y2="708" class="messageLine1" stroke-width="2" stroke="none"
                        marker-end="url(#arrowhead)" style="stroke-dasharray: 3, 3; fill: none;"></line>
                </svg></div>
            <div class="figure-caption">Figure 4.5 : Diagramme de séquence - Analyse prédictive</div>
        </div>

        <h3>4.4 Schémas d'architecture</h3>
        <p>Le schéma suivant présente l'architecture technique détaillée de CloudCapacity, mettant en évidence les
            différents composants et leurs interactions.</p>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378383" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 1336.015625px;" viewBox="-7.5 -8 1336.015625 825.75"
                    role="graphics-document document" aria-roledescription="flowchart-v2">
                    <style>
                        #mermaid-1746403378383 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378383 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378383 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378383 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378383 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378383 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378383 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378383 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378383 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378383 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378383 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378383 .label {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            color: #000000;
                        }

                        #mermaid-1746403378383 .cluster-label text {
                            fill: #333;
                        }

                        #mermaid-1746403378383 .cluster-label span,
                        #mermaid-1746403378383 p {
                            color: #333;
                        }

                        #mermaid-1746403378383 .label text,
                        #mermaid-1746403378383 span,
                        #mermaid-1746403378383 p {
                            fill: #000000;
                            color: #000000;
                        }

                        #mermaid-1746403378383 .node rect,
                        #mermaid-1746403378383 .node circle,
                        #mermaid-1746403378383 .node ellipse,
                        #mermaid-1746403378383 .node polygon,
                        #mermaid-1746403378383 .node path {
                            fill: #eee;
                            stroke: #999;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378383 .flowchart-label text {
                            text-anchor: middle;
                        }

                        #mermaid-1746403378383 .node .label {
                            text-align: center;
                        }

                        #mermaid-1746403378383 .node.clickable {
                            cursor: pointer;
                        }

                        #mermaid-1746403378383 .arrowheadPath {
                            fill: #333333;
                        }

                        #mermaid-1746403378383 .edgePath .path {
                            stroke: #666;
                            stroke-width: 2.0px;
                        }

                        #mermaid-1746403378383 .flowchart-link {
                            stroke: #666;
                            fill: none;
                        }

                        #mermaid-1746403378383 .edgeLabel {
                            background-color: white;
                            text-align: center;
                        }

                        #mermaid-1746403378383 .edgeLabel rect {
                            opacity: 0.5;
                            background-color: white;
                            fill: white;
                        }

                        #mermaid-1746403378383 .labelBkg {
                            background-color: rgba(255, 255, 255, 0.5);
                        }

                        #mermaid-1746403378383 .cluster rect {
                            fill: hsl(0, 0%, 98.9215686275%);
                            stroke: #707070;
                            stroke-width: 1px;
                        }

                        #mermaid-1746403378383 .cluster text {
                            fill: #333;
                        }

                        #mermaid-1746403378383 .cluster span,
                        #mermaid-1746403378383 p {
                            color: #333;
                        }

                        #mermaid-1746403378383 div.mermaidTooltip {
                            position: absolute;
                            text-align: center;
                            max-width: 200px;
                            padding: 2px;
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 12px;
                            background: hsl(-160, 0%, 93.3333333333%);
                            border: 1px solid #707070;
                            border-radius: 2px;
                            pointer-events: none;
                            z-index: 100;
                        }

                        #mermaid-1746403378383 .flowchartTitleText {
                            text-anchor: middle;
                            font-size: 18px;
                            fill: #000000;
                        }

                        #mermaid-1746403378383 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g>
                        <marker id="mermaid-1746403378383_flowchart-pointEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="6" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378383_flowchart-pointStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="12"
                            markerHeight="12" orient="auto">
                            <path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378383_flowchart-circleEnd" class="marker flowchart"
                            viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378383_flowchart-circleStart" class="marker flowchart"
                            viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <circle cx="5" cy="5" r="5" class="arrowMarkerPath"
                                style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle>
                        </marker>
                        <marker id="mermaid-1746403378383_flowchart-crossEnd" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <marker id="mermaid-1746403378383_flowchart-crossStart" class="marker cross flowchart"
                            viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11"
                            markerHeight="11" orient="auto">
                            <path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath"
                                style="stroke-width: 2; stroke-dasharray: 1, 0;"></path>
                        </marker>
                        <g class="root">
                            <g class="clusters">
                                <g class="cluster default flowchart-label" id="subGraph3">
                                    <rect style="" rx="0" ry="0" x="199.78125" y="719.15625" width="420.28125"
                                        height="90.59375"></rect>
                                    <g class="cluster-label" transform="translate(349.1953125, 719.15625)">
                                        <foreignobject width="121.453125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">External Systems</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="cluster default flowchart-label" id="subGraph2">
                                    <rect style="" rx="0" ry="0" x="640.0625" y="719.15625" width="678.9921875"
                                        height="90.59375"></rect>
                                    <g class="cluster-label" transform="translate(940.98828125, 719.15625)">
                                        <foreignobject width="77.140625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Data Layer</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="cluster default flowchart-label" id="subGraph1">
                                    <rect style="" rx="0" ry="0" x="182.9140625" y="578.5625" width="1137.6015625"
                                        height="90.59375"></rect>
                                    <g class="cluster-label" transform="translate(690.30078125, 578.5625)">
                                        <foreignobject width="122.828125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Backend Services</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="cluster default flowchart-label" id="Frontend">
                                    <rect style="" rx="0" ry="0" x="250.53125" y="0" width="1043.58203125"
                                        height="528.5625"></rect>
                                    <g class="cluster-label" transform="translate(739.986328125, 0)">
                                        <foreignobject width="64.671875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Frontend</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                            <g class="edgePaths">
                                <path
                                    d="M902.641,226.484L902.641,259.599C902.641,292.714,902.641,358.943,902.641,397.473C902.641,436.004,902.641,446.836,902.641,452.253L902.641,457.669"
                                    id="L-A1-A2-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A1 LE-A2"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M850.07,495.578L826.599,501.076C803.128,506.573,756.185,517.568,732.714,527.232C709.242,536.896,709.242,545.229,709.242,553.563C709.242,561.896,709.242,570.229,709.242,577.679C709.242,585.129,709.242,591.696,709.242,594.979L709.242,598.263"
                                    id="L-A2-B1-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A2 LE-B1"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M850.07,487.313L760.778,494.188C671.486,501.063,492.901,514.813,403.609,525.854C314.316,536.896,314.316,545.229,314.316,553.563C314.316,561.896,314.316,570.229,314.316,577.679C314.316,585.129,314.316,591.696,314.316,594.979L314.316,598.263"
                                    id="L-A2-B2-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A2 LE-B2"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M955.211,496.949L975.453,502.218C995.695,507.487,1036.18,518.025,1056.422,527.46C1076.664,536.896,1076.664,545.229,1076.664,553.563C1076.664,561.896,1076.664,570.229,1076.664,577.679C1076.664,585.129,1076.664,591.696,1076.664,594.979L1076.664,598.263"
                                    id="L-A2-B3-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A2 LE-B3"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M902.165,503.563L902.067,507.729C901.969,511.896,901.774,520.229,901.676,528.563C901.578,536.896,901.578,545.229,901.578,553.563C901.578,561.896,901.578,570.229,901.578,577.679C901.578,585.129,901.578,591.696,901.578,594.979L901.578,598.263"
                                    id="L-A2-B4-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A2 LE-B4"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M955.211,490.317L1002.73,496.692C1050.25,503.066,1145.289,515.814,1192.809,526.355C1240.328,536.896,1240.328,545.229,1240.328,553.563C1240.328,561.896,1240.328,570.229,1240.328,577.679C1240.328,585.129,1240.328,591.696,1240.328,594.979L1240.328,598.263"
                                    id="L-A2-B5-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-A2 LE-B5"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M709.718,644.156L709.816,648.323C709.914,652.49,710.109,660.823,710.207,669.156C710.305,677.49,710.305,685.823,710.305,694.156C710.305,702.49,710.305,710.823,710.305,718.273C710.305,725.723,710.305,732.29,710.305,735.573L710.305,738.856"
                                    id="L-B1-C1-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B1 LE-C1"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M375.871,629.525L447.638,636.13C519.405,642.735,662.939,655.946,734.706,666.718C806.473,677.49,806.473,685.823,806.473,694.156C806.473,702.49,806.473,710.823,815.654,718.947C824.836,727.07,843.199,734.984,852.381,738.941L861.562,742.898"
                                    id="L-B2-C2-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B2 LE-C2"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M1076.664,644.156L1076.664,648.323C1076.664,652.49,1076.664,660.823,1076.664,669.156C1076.664,677.49,1076.664,685.823,1076.664,694.156C1076.664,702.49,1076.664,710.823,1076.664,718.273C1076.664,725.723,1076.664,732.29,1076.664,735.573L1076.664,738.856"
                                    id="L-B3-C3-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B3 LE-C3"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M906.535,644.156L907.553,648.323C908.57,652.49,910.605,660.823,911.623,669.156C912.641,677.49,912.641,685.823,912.641,694.156C912.641,702.49,912.641,710.823,912.564,718.273C912.487,725.723,912.333,732.291,912.256,735.574L912.179,738.858"
                                    id="L-B4-C2-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B4 LE-C2"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M1195.141,632.173L1161.637,638.337C1128.134,644.501,1061.128,656.828,1027.624,667.159C994.121,677.49,994.121,685.823,994.121,694.156C994.121,702.49,994.121,710.823,987.303,718.731C980.484,726.64,966.848,734.123,960.029,737.865L953.211,741.606"
                                    id="L-B5-C2-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B5 LE-C2"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M1240.328,644.156L1240.328,648.323C1240.328,652.49,1240.328,660.823,1240.328,669.156C1240.328,677.49,1240.328,685.823,1240.328,694.156C1240.328,702.49,1240.328,710.823,1240.328,718.273C1240.328,725.723,1240.328,732.29,1240.328,735.573L1240.328,738.856"
                                    id="L-B5-C4-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B5 LE-C4"
                                    style="fill:none;" marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)">
                                </path>
                                <path
                                    d="M295.47,648.357L292.803,651.824C290.136,655.29,284.802,662.223,282.136,669.856C279.469,677.49,279.469,685.823,279.469,694.156C279.469,702.49,279.469,710.823,279.469,718.273C279.469,725.723,279.469,732.29,279.469,735.573L279.469,738.856"
                                    id="L-B2-D1-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B2 LE-D1"
                                    style="fill:none;" marker-start="url(#mermaid-1746403378383_flowchart-pointStart)"
                                    marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)"></path>
                                <path
                                    d="M1011.892,628.33L913.298,635.134C814.704,641.938,617.516,655.547,518.922,666.518C420.328,677.49,420.328,685.823,420.328,694.156C420.328,702.49,420.328,710.823,420.328,718.273C420.328,725.723,420.328,732.29,420.328,735.573L420.328,738.856"
                                    id="L-B3-D2-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B3 LE-D2"
                                    style="fill:none;" marker-start="url(#mermaid-1746403378383_flowchart-pointStart)"
                                    marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)"></path>
                                <path
                                    d="M842.845,631.443L794.168,637.729C745.491,644.014,648.136,656.585,599.459,667.037C550.781,677.49,550.781,685.823,550.781,694.156C550.781,702.49,550.781,710.823,550.781,718.273C550.781,725.723,550.781,732.29,550.781,735.573L550.781,738.856"
                                    id="L-B4-D3-0"
                                    class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-B4 LE-D3"
                                    style="fill:none;" marker-start="url(#mermaid-1746403378383_flowchart-pointStart)"
                                    marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)"></path>
                            </g>
                            <g class="edgeLabels">
                                <g class="edgeLabel" transform="translate(902.640625, 425.171875)">
                                    <g class="label" transform="translate(-46.015625, -12.796875)">
                                        <foreignobject width="92.03125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel">HTTP/HTTPS</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="edgeLabel">
                                    <g class="label" transform="translate(0, 0)">
                                        <foreignobject width="0" height="0">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="edgeLabel"></span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                            <g class="nodes">
                                <g class="root" transform="translate(-7.5, 17)">
                                    <g class="clusters">
                                        <g class="cluster default flowchart-label" id="Infrastructure">
                                            <rect style="" rx="0" ry="0" x="8" y="8" width="215.53125" height="362.375">
                                            </rect>
                                            <g class="cluster-label" transform="translate(66.03125, 8)">
                                                <foreignobject width="99.46875" height="25.59375">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="nodeLabel">Infrastructure</span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                    </g>
                                    <g class="edgePaths">
                                        <path
                                            d="M115.766,73.594L115.766,77.76C115.766,81.927,115.766,90.26,115.766,97.71C115.766,105.16,115.766,111.727,115.766,115.01L115.766,118.294"
                                            id="L-E4-E1-0"
                                            class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-E4 LE-E1"
                                            style="fill:none;"
                                            marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)"></path>
                                        <path
                                            d="M115.766,164.188L115.766,168.354C115.766,172.521,115.766,180.854,115.766,188.304C115.766,195.754,115.766,202.321,115.766,205.604L115.766,208.888"
                                            id="L-E1-E2-0"
                                            class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-E1 LE-E2"
                                            style="fill:none;"
                                            marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)"></path>
                                        <path
                                            d="M115.766,254.781L115.766,258.948C115.766,263.115,115.766,271.448,115.766,278.898C115.766,286.348,115.766,292.915,115.766,296.198L115.766,299.481"
                                            id="L-E2-E3-0"
                                            class=" edge-thickness-normal edge-pattern-solid flowchart-link LS-E2 LE-E3"
                                            style="fill:none;"
                                            marker-end="url(#mermaid-1746403378383_flowchart-pointEnd)"></path>
                                    </g>
                                    <g class="edgeLabels">
                                        <g class="edgeLabel">
                                            <g class="label" transform="translate(0, 0)">
                                                <foreignobject width="0" height="0">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="edgeLabel"></span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                        <g class="edgeLabel">
                                            <g class="label" transform="translate(0, 0)">
                                                <foreignobject width="0" height="0">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="edgeLabel"></span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                        <g class="edgeLabel">
                                            <g class="label" transform="translate(0, 0)">
                                                <foreignobject width="0" height="0">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="edgeLabel"></span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                    </g>
                                    <g class="nodes">
                                        <g class="node default default flowchart-label" id="flowchart-E1-87"
                                            transform="translate(115.765625, 143.890625)">
                                            <rect class="basic label-container" style="" rx="0" ry="0" x="-72.765625"
                                                y="-20.296875" width="145.53125" height="40.59375"></rect>
                                            <g class="label" style="" transform="translate(-65.265625, -12.796875)">
                                                <rect></rect>
                                                <foreignobject width="130.53125" height="25.59375">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="nodeLabel">Docker Containers</span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                        <g class="node default default flowchart-label" id="flowchart-E4-90"
                                            transform="translate(115.765625, 53.296875)">
                                            <rect class="basic label-container" style="" rx="0" ry="0" x="-59.375"
                                                y="-20.296875" width="118.75" height="40.59375"></rect>
                                            <g class="label" style="" transform="translate(-51.875, -12.796875)">
                                                <rect></rect>
                                                <foreignobject width="103.75" height="25.59375">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="nodeLabel">CI/CD Pipeline</span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                        <g class="node default default flowchart-label" id="flowchart-E2-88"
                                            transform="translate(115.765625, 234.484375)">
                                            <rect class="basic label-container" style="" rx="0" ry="0" x="-47.6640625"
                                                y="-20.296875" width="95.328125" height="40.59375"></rect>
                                            <g class="label" style="" transform="translate(-40.1640625, -12.796875)">
                                                <rect></rect>
                                                <foreignobject width="80.328125" height="25.59375">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="nodeLabel">Kubernetes</span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                        <g class="node default default flowchart-label" id="flowchart-E3-89"
                                            transform="translate(115.765625, 325.078125)">
                                            <rect class="basic label-container" style="" rx="0" ry="0" x="-42.7109375"
                                                y="-20.296875" width="85.421875" height="40.59375"></rect>
                                            <g class="label" style="" transform="translate(-35.2109375, -12.796875)">
                                                <rect></rect>
                                                <foreignobject width="70.421875" height="25.59375">
                                                    <div xmlns="http://www.w3.org/1999/xhtml"
                                                        style="display: inline-block; white-space: nowrap;"><span
                                                            class="nodeLabel">OpenShift</span></div>
                                                </foreignobject>
                                            </g>
                                        </g>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-D1-82"
                                    transform="translate(279.46875, 764.453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-44.6875"
                                        y="-20.296875" width="89.375" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-37.1875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="74.375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Zabbix API</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-D2-84"
                                    transform="translate(420.328125, 764.453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-46.171875"
                                        y="-20.296875" width="92.34375" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-38.671875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="77.34375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Maia CMDB</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-D3-86"
                                    transform="translate(550.78125, 764.453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-34.28125"
                                        y="-20.296875" width="68.5625" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-26.78125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="53.5625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Jira API</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-C1-70"
                                    transform="translate(710.3046875, 764.453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-35.2421875"
                                        y="-20.296875" width="70.484375" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-27.7421875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="55.484375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">User DB</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-C2-72"
                                    transform="translate(911.578125, 764.453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-45.1484375"
                                        y="-20.296875" width="90.296875" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-37.6484375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="75.296875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Metrics DB</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-C3-74"
                                    transform="translate(1076.6640625, 764.453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-69.9375"
                                        y="-20.296875" width="139.875" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-62.4375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="124.875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Report Templates</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-C4-80"
                                    transform="translate(1240.328125, 764.453125)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-43.7265625"
                                        y="-20.296875" width="87.453125" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-36.2265625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="72.453125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">ML Models</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B1-60"
                                    transform="translate(709.2421875, 623.859375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-88.859375"
                                        y="-20.296875" width="177.71875" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-81.359375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="162.71875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Authentication Service</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B2-62"
                                    transform="translate(314.31640625, 623.859375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-61.5546875"
                                        y="-20.296875" width="123.109375" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-54.0546875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="108.109375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Metrics Service</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B3-64"
                                    transform="translate(1076.6640625, 623.859375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-59.484375"
                                        y="-20.296875" width="118.96875" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-51.984375, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="103.96875" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Report Service</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B4-66"
                                    transform="translate(901.578125, 623.859375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-53.4765625"
                                        y="-20.296875" width="106.953125" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-45.9765625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="91.953125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Alert Service</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-B5-68"
                                    transform="translate(1240.328125, 623.859375)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-45.1875"
                                        y="-20.296875" width="90.375" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-37.6875, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="75.375" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">ML Service</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-A2-58"
                                    transform="translate(902.640625, 483.265625)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-52.5703125"
                                        y="-20.296875" width="105.140625" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-45.0703125, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="90.140625" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">API Gateway</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                                <g class="node default default flowchart-label" id="flowchart-A1-57"
                                    transform="translate(902.640625, 206.1875)">
                                    <rect class="basic label-container" style="" rx="0" ry="0" x="-44.4765625"
                                        y="-20.296875" width="88.953125" height="40.59375"></rect>
                                    <g class="label" style="" transform="translate(-36.9765625, -12.796875)">
                                        <rect></rect>
                                        <foreignobject width="73.953125" height="25.59375">
                                            <div xmlns="http://www.w3.org/1999/xhtml"
                                                style="display: inline-block; white-space: nowrap;"><span
                                                    class="nodeLabel">Angular UI</span></div>
                                        </foreignobject>
                                    </g>
                                </g>
                            </g>
                        </g>
                    </g>
                </svg></div>
            <div class="figure-caption">Figure 4.6 : Schéma d'architecture technique détaillée</div>
        </div>

        <h3>4.5 Modèle de données</h3>
        <p>Le modèle de données suivant présente les principales tables de la base de données et leurs relations.</p>

        <div class="diagram-container">
            <div class="mermaid" data-processed="true"><svg id="mermaid-1746403378463" width="100%"
                    xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                    style="max-width: 638.7538452148438px;" viewBox="0 0 638.7538452148438 1030"
                    role="graphics-document document" aria-roledescription="er">
                    <style>
                        #mermaid-1746403378463 {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                            fill: #000000;
                        }

                        #mermaid-1746403378463 .error-icon {
                            fill: #552222;
                        }

                        #mermaid-1746403378463 .error-text {
                            fill: #552222;
                            stroke: #552222;
                        }

                        #mermaid-1746403378463 .edge-thickness-normal {
                            stroke-width: 2px;
                        }

                        #mermaid-1746403378463 .edge-thickness-thick {
                            stroke-width: 3.5px;
                        }

                        #mermaid-1746403378463 .edge-pattern-solid {
                            stroke-dasharray: 0;
                        }

                        #mermaid-1746403378463 .edge-pattern-dashed {
                            stroke-dasharray: 3;
                        }

                        #mermaid-1746403378463 .edge-pattern-dotted {
                            stroke-dasharray: 2;
                        }

                        #mermaid-1746403378463 .marker {
                            fill: #666;
                            stroke: #666;
                        }

                        #mermaid-1746403378463 .marker.cross {
                            stroke: #666;
                        }

                        #mermaid-1746403378463 svg {
                            font-family: "trebuchet ms", verdana, arial, sans-serif;
                            font-size: 16px;
                        }

                        #mermaid-1746403378463 .entityBox {
                            fill: #eee;
                            stroke: #999;
                        }

                        #mermaid-1746403378463 .attributeBoxOdd {
                            fill: #ffffff;
                            stroke: #999;
                        }

                        #mermaid-1746403378463 .attributeBoxEven {
                            fill: #f2f2f2;
                            stroke: #999;
                        }

                        #mermaid-1746403378463 .relationshipLabelBox {
                            fill: hsl(-160, 0%, 93.3333333333%);
                            opacity: 0.7;
                            background-color: hsl(-160, 0%, 93.3333333333%);
                        }

                        #mermaid-1746403378463 .relationshipLabelBox rect {
                            opacity: 0.5;
                        }

                        #mermaid-1746403378463 .relationshipLine {
                            stroke: #666;
                        }

                        #mermaid-1746403378463 .entityTitleText {
                            text-anchor: middle;
                            font-size: 18px;
                            fill: #000000;
                        }

                        #mermaid-1746403378463 #MD_PARENT_START {
                            fill: #f5f5f5 !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378463 #MD_PARENT_END {
                            fill: #f5f5f5 !important;
                            stroke: #666 !important;
                            stroke-width: 1;
                        }

                        #mermaid-1746403378463 :root {
                            --mermaid-font-family: "trebuchet ms", verdana, arial, sans-serif;
                        }
                    </style>
                    <g></g>
                    <defs>
                        <marker id="MD_PARENT_START" refX="0" refY="7" markerWidth="190" markerHeight="240"
                            orient="auto">
                            <path d="M 18,7 L9,13 L1,7 L9,1 Z"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="MD_PARENT_END" refX="19" refY="7" markerWidth="20" markerHeight="28" orient="auto">
                            <path d="M 18,7 L9,13 L1,7 L9,1 Z"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ONLY_ONE_START" refX="0" refY="9" markerWidth="18" markerHeight="18" orient="auto">
                            <path stroke="gray" fill="none" d="M9,0 L9,18 M15,0 L15,18"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ONLY_ONE_END" refX="18" refY="9" markerWidth="18" markerHeight="18" orient="auto">
                            <path stroke="gray" fill="none" d="M3,0 L3,18 M9,0 L9,18"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ZERO_OR_ONE_START" refX="0" refY="9" markerWidth="30" markerHeight="18"
                            orient="auto">
                            <circle stroke="gray" fill="white" cx="21" cy="9" r="6"></circle>
                            <path stroke="gray" fill="none" d="M9,0 L9,18"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ZERO_OR_ONE_END" refX="30" refY="9" markerWidth="30" markerHeight="18"
                            orient="auto">
                            <circle stroke="gray" fill="white" cx="9" cy="9" r="6"></circle>
                            <path stroke="gray" fill="none" d="M21,0 L21,18"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ONE_OR_MORE_START" refX="18" refY="18" markerWidth="45" markerHeight="36"
                            orient="auto">
                            <path stroke="gray" fill="none" d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ONE_OR_MORE_END" refX="27" refY="18" markerWidth="45" markerHeight="36"
                            orient="auto">
                            <path stroke="gray" fill="none" d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ZERO_OR_MORE_START" refX="18" refY="18" markerWidth="57" markerHeight="36"
                            orient="auto">
                            <circle stroke="gray" fill="white" cx="48" cy="18" r="6"></circle>
                            <path stroke="gray" fill="none" d="M0,18 Q18,0 36,18 Q18,36 0,18"></path>
                        </marker>
                    </defs>
                    <defs>
                        <marker id="ZERO_OR_MORE_END" refX="39" refY="18" markerWidth="57" markerHeight="36"
                            orient="auto">
                            <circle stroke="gray" fill="white" cx="9" cy="18" r="6"></circle>
                            <path stroke="gray" fill="none" d="M21,18 Q39,0 57,18 Q39,36 21,18"></path>
                        </marker>
                    </defs>
                    <path class="er relationshipLine"
                        d="M178.675,421.443L161.541,438.869C144.408,456.295,110.141,491.148,93.007,518.74C75.874,546.333,75.874,566.667,75.874,576.833L75.874,587"
                        marker-end="url(#ZERO_OR_MORE_END)" marker-start="url(#ONLY_ONE_START)"
                        style="stroke: gray; fill: none;"></path>
                    <path class="er relationshipLine"
                        d="M75.874,743L75.874,753.167C75.874,763.333,75.874,783.667,75.874,802.167C75.874,820.667,75.874,837.333,75.874,845.667L75.874,854"
                        marker-end="url(#ZERO_OR_MORE_END)" marker-start="url(#ONLY_ONE_START)"
                        style="stroke: gray; fill: none;"></path>
                    <path class="er relationshipLine"
                        d="M231.561,476L231.366,484.333C231.17,492.667,230.778,509.333,244.217,535.167C257.655,561,284.924,596,298.559,613.5L312.193,631"
                        marker-end="url(#ZERO_OR_MORE_END)" marker-start="url(#ONLY_ONE_START)"
                        style="stroke: gray; fill: none;"></path>
                    <path class="er relationshipLine"
                        d="M461.686,432L451.148,447.667C440.61,463.333,419.533,494.667,401.47,527.833C383.406,561,368.355,596,360.829,613.5L353.304,631"
                        marker-end="url(#ZERO_OR_MORE_END)" marker-start="url(#ONLY_ONE_START)"
                        style="stroke: gray; fill: none;"></path>
                    <path class="er relationshipLine"
                        d="M506.753,154L506.753,162.333C506.753,170.667,506.753,187.333,506.753,211.333C506.753,235.333,506.753,266.667,506.753,282.333L506.753,298"
                        marker-end="url(#ZERO_OR_MORE_END)" marker-start="url(#ONLY_ONE_START)"
                        style="stroke: gray; fill: none;"></path>
                    <path class="er relationshipLine"
                        d="M289.665,392.571L334.426,414.809C379.187,437.047,468.709,481.524,513.47,512.095C558.231,542.667,558.231,559.333,558.231,567.667L558.231,576"
                        marker-end="url(#ZERO_OR_MORE_END)" marker-start="url(#ONLY_ONE_START)"
                        style="stroke: gray; fill: none;"></path>
                    <g id="entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1"
                        transform="translate(178.67507553100586,254 )">
                        <rect class="er entityBox" x="0" y="0" width="110.99008178710938" height="222"></rect><text
                            class="er entityLabel" id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1" x="0"
                            y="0" style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;"
                            transform="translate(55.49504089355469,12)">SERVER</text>
                        <rect class="er attributeBoxOdd" x="0" y="24" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-1-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)">int</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="24" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-1-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,35)">id</text>
                        <rect class="er attributeBoxEven" x="0" y="46" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-2-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,57)">string</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="46" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-2-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,57)">name</text>
                        <rect class="er attributeBoxOdd" x="0" y="68" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-3-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,79)">string</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="68" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-3-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,79)">ip</text>
                        <rect class="er attributeBoxEven" x="0" y="90" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-4-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,101)">string</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="90" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-4-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,101)">type</text>
                        <rect class="er attributeBoxOdd" x="0" y="112" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-5-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,123)">string</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="112" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-5-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,123)">os</text>
                        <rect class="er attributeBoxEven" x="0" y="134" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-6-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,145)">int</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="134" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-6-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,145)">cpu_cores</text>
                        <rect class="er attributeBoxOdd" x="0" y="156" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-7-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,167)">int</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="156" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-7-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,167)">ram_gb</text>
                        <rect class="er attributeBoxEven" x="0" y="178" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-8-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,189)">int</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="178" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-8-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,189)">disk_gb</text>
                        <rect class="er attributeBoxOdd" x="0" y="200" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-9-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,211)">date</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="200" width="74.37098693847656"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-SERVER-3fcb8347-36a4-5246-84ea-4ebbdf9b24b1-attr-9-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,211)">creation_date</text>
                    </g>
                    <g id="entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43"
                        transform="translate(25.873565673828125,587 )">
                        <rect class="er entityBox" x="0" y="0" width="100" height="156"></rect><text
                            class="er entityLabel" id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43" x="0"
                            y="0" style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;"
                            transform="translate(50,12)">METRIC</text>
                        <rect class="er attributeBoxOdd" x="0" y="24" width="38.81885528564453" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-1-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)">int</text>
                        <rect class="er attributeBoxOdd" x="38.81885528564453" y="24" width="61.18114471435547"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-1-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(43.81885528564453,35)">id</text>
                        <rect class="er attributeBoxEven" x="0" y="46" width="38.81885528564453" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-2-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)">int</text>
                        <rect class="er attributeBoxEven" x="38.81885528564453" y="46" width="61.18114471435547"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-2-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(43.81885528564453,57)">server_id</text>
                        <rect class="er attributeBoxOdd" x="0" y="68" width="38.81885528564453" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-3-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,79)">string</text>
                        <rect class="er attributeBoxOdd" x="38.81885528564453" y="68" width="61.18114471435547"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-3-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(43.81885528564453,79)">name</text>
                        <rect class="er attributeBoxEven" x="0" y="90" width="38.81885528564453" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-4-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,101)">float</text>
                        <rect class="er attributeBoxEven" x="38.81885528564453" y="90" width="61.18114471435547"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-4-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(43.81885528564453,101)">value</text>
                        <rect class="er attributeBoxOdd" x="0" y="112" width="38.81885528564453" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-5-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,123)">date</text>
                        <rect class="er attributeBoxOdd" x="38.81885528564453" y="112" width="61.18114471435547"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-5-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(43.81885528564453,123)">timestamp</text>
                        <rect class="er attributeBoxEven" x="0" y="134" width="38.81885528564453" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-6-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,145)">string</text>
                        <rect class="er attributeBoxEven" x="38.81885528564453" y="134" width="61.18114471435547"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-METRIC-312ae411-6054-5ecd-b069-1fefc6a4aa43-attr-6-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(43.81885528564453,145)">unit</text>
                    </g>
                    <g id="entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560" transform="translate(20,854 )">
                        <rect class="er entityBox" x="0" y="0" width="111.74713134765625" height="156"></rect><text
                            class="er entityLabel" id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560" x="0"
                            y="0" style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;"
                            transform="translate(55.873565673828125,12)">ALERT</text>
                        <rect class="er attributeBoxOdd" x="0" y="24" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-1-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)">int</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="24" width="75.12803649902344"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-1-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,35)">id</text>
                        <rect class="er attributeBoxEven" x="0" y="46" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-2-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)">int</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="46" width="75.12803649902344"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-2-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,57)">metric_id</text>
                        <rect class="er attributeBoxOdd" x="0" y="68" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-3-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,79)">string</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="68" width="75.12803649902344"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-3-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,79)">level</text>
                        <rect class="er attributeBoxEven" x="0" y="90" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-4-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,101)">string</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="90" width="75.12803649902344"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-4-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,101)">message</text>
                        <rect class="er attributeBoxOdd" x="0" y="112" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-5-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,123)">date</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="112" width="75.12803649902344"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-5-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,123)">timestamp</text>
                        <rect class="er attributeBoxEven" x="0" y="134" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-6-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,145)">bool</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="134" width="75.12803649902344"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-ALERT-2749889f-e9d1-5df4-b3d9-85dfbb849560-attr-6-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,145)">acknowledged</text>
                    </g>
                    <g id="entity-REPORTSERVER-5b979323-afd8-5252-878a-b4c089e0c48f"
                        transform="translate(279.65742111206055,631 )">
                        <rect class="er entityBox" x="0" y="0" width="118.05078125" height="68"></rect><text
                            class="er entityLabel" id="text-entity-REPORTSERVER-5b979323-afd8-5252-878a-b4c089e0c48f"
                            x="0" y="0" style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;"
                            transform="translate(59.025390625,12)">REPORT_SERVER</text>
                        <rect class="er attributeBoxOdd" x="0" y="24" width="44.291236877441406" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-REPORTSERVER-5b979323-afd8-5252-878a-b4c089e0c48f-attr-1-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)">int</text>
                        <rect class="er attributeBoxOdd" x="44.291236877441406" y="24" width="73.7595443725586"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-REPORTSERVER-5b979323-afd8-5252-878a-b4c089e0c48f-attr-1-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(49.291236877441406,35)">report_id</text>
                        <rect class="er attributeBoxEven" x="0" y="46" width="44.291236877441406" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-REPORTSERVER-5b979323-afd8-5252-878a-b4c089e0c48f-attr-2-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)">int</text>
                        <rect class="er attributeBoxEven" x="44.291236877441406" y="46" width="73.7595443725586"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-REPORTSERVER-5b979323-afd8-5252-878a-b4c089e0c48f-attr-2-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(49.291236877441406,57)">server_id</text>
                    </g>
                    <g id="entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98"
                        transform="translate(445.6554069519043,298 )">
                        <rect class="er entityBox" x="0" y="0" width="122.19612121582031" height="134"></rect><text
                            class="er entityLabel" id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98" x="0"
                            y="0" style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;"
                            transform="translate(61.098060607910156,12)">REPORT</text>
                        <rect class="er attributeBoxOdd" x="0" y="24" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-1-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)">int</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="24" width="85.5770263671875"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-1-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,35)">id</text>
                        <rect class="er attributeBoxEven" x="0" y="46" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-2-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,57)">string</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="46" width="85.5770263671875"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-2-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,57)">name</text>
                        <rect class="er attributeBoxOdd" x="0" y="68" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-3-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,79)">date</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="68" width="85.5770263671875"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-3-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,79)">generation_date</text>
                        <rect class="er attributeBoxEven" x="0" y="90" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-4-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,101)">string</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="90" width="85.5770263671875"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-4-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,101)">format</text>
                        <rect class="er attributeBoxOdd" x="0" y="112" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-5-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,123)">string</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="112" width="85.5770263671875"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-REPORT-5c49cc59-f6fa-5b89-8907-e4ddb797db98-attr-5-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,123)">status</text>
                    </g>
                    <g id="entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8"
                        transform="translate(456.75346755981445,20 )">
                        <rect class="er entityBox" x="0" y="0" width="100" height="134"></rect><text
                            class="er entityLabel" id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8" x="0"
                            y="0" style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;"
                            transform="translate(50,12)">USER</text>
                        <rect class="er attributeBoxOdd" x="0" y="24" width="40.99531555175781" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-1-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)">int</text>
                        <rect class="er attributeBoxOdd" x="40.99531555175781" y="24" width="59.00468444824219"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-1-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(45.99531555175781,35)">id</text>
                        <rect class="er attributeBoxEven" x="0" y="46" width="40.99531555175781" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-2-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,57)">string</text>
                        <rect class="er attributeBoxEven" x="40.99531555175781" y="46" width="59.00468444824219"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-2-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(45.99531555175781,57)">username</text>
                        <rect class="er attributeBoxOdd" x="0" y="68" width="40.99531555175781" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-3-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,79)">string</text>
                        <rect class="er attributeBoxOdd" x="40.99531555175781" y="68" width="59.00468444824219"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-3-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(45.99531555175781,79)">email</text>
                        <rect class="er attributeBoxEven" x="0" y="90" width="40.99531555175781" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-4-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,101)">string</text>
                        <rect class="er attributeBoxEven" x="40.99531555175781" y="90" width="59.00468444824219"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-4-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(45.99531555175781,101)">role</text>
                        <rect class="er attributeBoxOdd" x="0" y="112" width="40.99531555175781" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-5-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,123)">date</text>
                        <rect class="er attributeBoxOdd" x="40.99531555175781" y="112" width="59.00468444824219"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-USER-6e7e2962-0c6f-577d-a7fd-2ce3a0cfd2c8-attr-5-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(45.99531555175781,123)">last_login</text>
                    </g>
                    <g id="entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e"
                        transform="translate(497.70820236206055,576 )">
                        <rect class="er entityBox" x="0" y="0" width="121.04563903808594" height="178"></rect><text
                            class="er entityLabel" id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e"
                            x="0" y="0" style="dominant-baseline: middle; text-anchor: middle; font-size: 12px;"
                            transform="translate(60.52281951904297,12)">PREDICTION</text>
                        <rect class="er attributeBoxOdd" x="0" y="24" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-1-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,35)">int</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="24" width="84.42654418945312"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-1-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,35)">id</text>
                        <rect class="er attributeBoxEven" x="0" y="46" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-2-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;" transform="translate(5,57)">int</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="46" width="84.42654418945312"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-2-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,57)">server_id</text>
                        <rect class="er attributeBoxOdd" x="0" y="68" width="36.61909484863281" height="22"></rect><text
                            class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-3-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,79)">string</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="68" width="84.42654418945312"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-3-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,79)">metric_name</text>
                        <rect class="er attributeBoxEven" x="0" y="90" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-4-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,101)">float</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="90" width="84.42654418945312"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-4-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,101)">predicted_value</text>
                        <rect class="er attributeBoxOdd" x="0" y="112" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-5-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,123)">date</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="112" width="84.42654418945312"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-5-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,123)">prediction_date</text>
                        <rect class="er attributeBoxEven" x="0" y="134" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-6-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,145)">date</text>
                        <rect class="er attributeBoxEven" x="36.61909484863281" y="134" width="84.42654418945312"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-6-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,145)">target_date</text>
                        <rect class="er attributeBoxOdd" x="0" y="156" width="36.61909484863281" height="22"></rect>
                        <text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-7-type" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(5,167)">float</text>
                        <rect class="er attributeBoxOdd" x="36.61909484863281" y="156" width="84.42654418945312"
                            height="22"></rect><text class="er entityLabel"
                            id="text-entity-PREDICTION-a402094a-8db0-5fff-a90b-b3a6125ba87e-attr-7-name" x="0" y="0"
                            style="dominant-baseline: middle; font-size: 10.2px;"
                            transform="translate(41.61909484863281,167)">confidence</text>
                    </g>
                    <rect class="er relationshipLabelBox" x="101.13219451904297" y="488.1584167480469"
                        width="17.861328125" height="14"></rect><text class="er relationshipLabel" id="rel1"
                        x="110.06285858154297" y="495.1584167480469"
                        style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;">has</text>
                    <rect class="er relationshipLabelBox" x="55.324195861816406" y="791.5" width="41.099609375"
                        height="14"></rect><text class="er relationshipLabel" id="rel2" x="75.8740005493164" y="798.5"
                        style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;">triggers</text>
                    <rect class="er relationshipLabelBox" x="227.37408447265625" y="552.2568969726562"
                        width="62.646484375" height="14"></rect><text class="er relationshipLabel" id="rel3"
                        x="258.69732666015625" y="559.2568969726562"
                        style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;">included_in</text>
                    <rect class="er relationshipLabelBox" x="378.8197021484375" y="521.1296997070312"
                        width="44.978515625" height="14"></rect><text class="er relationshipLabel" id="rel4"
                        x="401.3089599609375" y="528.1296997070312"
                        style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;">contains</text>
                    <rect class="er relationshipLabelBox" x="480.28228759765625" y="219" width="52.94140625"
                        height="14"></rect><text class="er relationshipLabel" id="rel5" x="506.75299072265625" y="226"
                        style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;">generates</text>
                    <rect class="er relationshipLabelBox" x="430.70709228515625" y="461.6905517578125"
                        width="17.861328125" height="14"></rect><text class="er relationshipLabel" id="rel6"
                        x="439.63775634765625" y="468.6905517578125"
                        style="text-anchor: middle; dominant-baseline: middle; font-size: 12px;">has</text>
                </svg></div>
            <div class="figure-caption">Figure 4.7 : Modèle de données et relations</div>
        </div>
    </div>

    <!-- Chapitre 5 -->
    <div class="page latex-section">
        <h2>Chapitre 5 : Planification</h2>

        <h3>5.1 Méthodologie de travail</h3>
        <p>Le développement de CloudCapacity suivra une approche agile, plus précisément la méthodologie Scrum, qui
            permettra une livraison incrémentale et itérative du produit. Cette approche présente plusieurs avantages :
        </p>
        <ul>
            <li>Adaptation rapide aux changements de priorités ou d'exigences</li>
            <li>Livraison régulière de fonctionnalités utilisables</li>
            <li>Feedback continu permettant d'améliorer le produit</li>
            <li>Visibilité accrue sur l'avancement du projet</li>
            <li>Identification précoce des risques et des problèmes</li>
        </ul>

        <p>Le projet sera divisé en sprints de deux semaines, chacun se terminant par une démonstration des
            fonctionnalités développées et une rétrospective pour améliorer constamment le processus de développement.
        </p>

        <h3>5.2 Planning et jalons</h3>
        <p>Le projet CloudCapacity est planifié sur une durée de 6 mois, avec les jalons suivants :</p>

        <table class="table-data">
            <tbody>
                <tr>
                    <th>Phase</th>
                    <th>Durée</th>
                    <th>Livrables</th>
                    <th>Date</th>
                </tr>
                <tr>
                    <td>Analyse et conception</td>
                    <td>1 mois</td>
                    <td>Spécifications détaillées, maquettes UI, architecture technique</td>
                    <td>M1</td>
                </tr>
                <tr>
                    <td>Développement - MVP</td>
                    <td>2 mois</td>
                    <td>Version minimale avec collecte de métriques et dashboards basiques</td>
                    <td>M3</td>
                </tr>
                <tr>
                    <td>Développement - Rapports et alertes</td>
                    <td>1 mois</td>
                    <td>Système de reporting automatisé et alertes configurables</td>
                    <td>M4</td>
                </tr>
                <tr>
                    <td>Développement - ML et prédiction</td>
                    <td>1 mois</td>
                    <td>Intégration des algorithmes de prédiction et optimisation</td>
                    <td>M5</td>
                </tr>
                <tr>
                    <td>Tests et déploiement</td>
                    <td>1 mois</td>
                    <td>Version finale testée et déployée en production</td>
                    <td>M6</td>
                </tr>
            </tbody>
        </table>

        <h3>5.3 Gestion des risques</h3>
        <p>Les principaux risques identifiés pour le projet et les mesures d'atténuation associées sont présentés
            ci-dessous :</p>

        <table class="table-data">
            <tbody>
                <tr>
                    <th>Risque</th>
                    <th>Impact</th>
                    <th>Probabilité</th>
                    <th>Mesures d'atténuation</th>
                </tr>
                <tr>
                    <td>Difficulté d'intégration avec les systèmes existants (Zabbix, Maia, Jira)</td>
                    <td>Élevé</td>
                    <td>Moyen</td>
                    <td>Prévoir une phase de POC dédiée à l'intégration, impliquer les experts des systèmes existants
                    </td>
                </tr>
                <tr>
                    <td>Performance insuffisante pour le traitement des volumes de données</td>
                    <td>Élevé</td>
                    <td>Faible</td>
                    <td>Tests de charge précoces, optimisation des requêtes, mise en place de mécanismes de cache</td>
                </tr>
                <tr>
                    <td>Précision insuffisante des algorithmes de prédiction</td>
                    <td>Moyen</td>
                    <td>Moyen</td>
                    <td>Approche itérative, tests avec des données historiques réelles, ajustement continu des modèles
                    </td>
                </tr>
                <tr>
                    <td>Acceptation utilisateur limitée</td>
                    <td>Élevé</td>
                    <td>Faible</td>
                    <td>Implication des utilisateurs dès le début du projet, sessions de formation, support dédié</td>
                </tr>
                <tr>
                    <td>Dérive du périmètre</td>
                    <td>Moyen</td>
                    <td>Moyen</td>
                    <td>Définition claire du MVP, priorisation des fonctionnalités, gestion stricte du backlog</td>
                </tr>
            </tbody>
        </table>

        <p>Un suivi régulier des risques sera effectué lors des réunions de sprint, avec mise à jour du registre des
            risques et ajustement des mesures d'atténuation si nécessaire.</p>
    </div>

    <!-- Conclusion -->
    <div class="page latex-section">
        <h2>Conclusion générale</h2>
        <p>Le projet CloudCapacity répond à un besoin critique pour Sopra HR Software : optimiser la gestion de la
            capacité des serveurs on-premise pour améliorer les performances, réduire les coûts et anticiper les
            évolutions des besoins en ressources. Grâce à une approche centrée sur l'automatisation, la visualisation
            dynamique et l'intelligence artificielle, cette solution permettra de transformer radicalement la façon dont
            l'entreprise supervise et gère son infrastructure.</p>

        <p>Les choix technologiques (FastAPI, Angular, Docker, Kubernetes, OpenShift) s'inscrivent dans une démarche de
            modernisation des outils et pratiques, tout en assurant une intégration harmonieuse avec les systèmes
            existants (Zabbix, Maia CMDB, Jira). L'architecture modulaire et l'approche DevOps garantissent par ailleurs
            la flexibilité et l'évolutivité de la solution face aux besoins futurs.</p>

        <p>La mise en place de CloudCapacity permettra d'atteindre plusieurs objectifs stratégiques :</p>
        <ul>
            <li>Améliorer la visibilité sur l'utilisation des ressources grâce à des tableaux de bord interactifs et
                personnalisables</li>
            <li>Automatiser la génération et la distribution des rapports de capacité, libérant du temps pour les
                équipes techniques</li>
            <li>Anticiper les surcharges de capacité grâce aux algorithmes de prédiction, permettant une intervention
                proactive</li>
            <li>Optimiser l'allocation des ressources en fonction des besoins réels et prévus, contribuant à la
                réduction des coûts d'infrastructure</li>
            <li>Améliorer la prise de décision concernant les investissements en infrastructure grâce à des analyses
                détaillées et des recommandations basées sur les données</li>
        </ul>

        <p>Les prochaines étapes du projet consistent à finaliser les spécifications détaillées, à mettre en place
            l'environnement de développement et à commencer le développement du MVP selon la planification établie. Une
            attention particulière sera portée à l'implication des utilisateurs finaux tout au long du processus pour
            garantir que la solution répond pleinement à leurs besoins et attentes.</p>

        <p>En conclusion, CloudCapacity représente une étape importante dans la transformation numérique de la gestion
            d'infrastructure de Sopra HR Software, alliant innovation technologique et réponse à des besoins métiers
            concrets. Le succès de ce projet permettra de poser les bases pour de futures améliorations et extensions de
            la plateforme.</p>
    </div>

    <!-- Bibliographie -->
    <div class="page latex-section">
        <h2>Bibliographie</h2>
        <ul>
            <li>[1] Documentation Sopra HR Software, "PTS_SHRS Expression de besoins Cloud V1.0", 2025.</li>
            <li>[2] Documentation technique Zabbix, "Zabbix API Reference", <a
                    href="https://www.zabbix.com/documentation/current/en/manual/api">https://www.zabbix.com/documentation/current/en/manual/api</a>,
                consulté en février 2025.</li>
            <li>[3] Documentation technique FastAPI, "FastAPI Documentation", <a
                    href="https://fastapi.tiangolo.com/">https://fastapi.tiangolo.com/</a>, consulté en février 2025.
            </li>
            <li>[4] Documentation Angular, "Angular Documentation", <a
                    href="https://angular.io/docs">https://angular.io/docs</a>, consulté en février 2025.</li>
            <li>[5] Documentation technique Kubernetes, "Kubernetes Documentation", <a
                    href="https://kubernetes.io/docs/home/">https://kubernetes.io/docs/home/<USER>/a>, consulté en février
                2025.</li>
            <li>[6] Documentation technique OpenShift, "OpenShift Container Platform Documentation", <a
                    href="https://docs.openshift.com/">https://docs.openshift.com/</a>, consulté en février 2025.</li>
            <li>[7] Documentation technique Docker, "Docker Documentation", <a
                    href="https://docs.docker.com/">https://docs.docker.com/</a>, consulté en février 2025.</li>
            <li>[8] Atlassian, "Jira REST API Reference", <a
                    href="https://developer.atlassian.com/cloud/jira/platform/rest/v3/intro/">https://developer.atlassian.com/cloud/jira/platform/rest/v3/intro/</a>,
                consulté en février 2025.</li>
            <li>[9] Scikit-learn Documentation, "User Guide: Supervised Learning", <a
                    href="https://scikit-learn.org/stable/supervised_learning.html">https://scikit-learn.org/stable/supervised_learning.html</a>,
                consulté en février 2025.</li>
            <li>[10] TensorFlow Documentation, "Time Series Forecasting", <a
                    href="https://www.tensorflow.org/tutorials/structured_data/time_series">https://www.tensorflow.org/tutorials/structured_data/time_series</a>,
                consulté en février 2025.</li>
        </ul>
    </div>

    <!-- Annexes -->
    <div class="page latex-section">
        <h2>Annexes</h2>
        <h3>Annexe 1 : Glossaire technique</h3>
        <table class="table-data">
            <tbody>
                <tr>
                    <th>Terme</th>
                    <th>Définition</th>
                </tr>
                <tr>
                    <td>API (Application Programming Interface)</td>
                    <td>Interface permettant à différents logiciels de communiquer entre eux.</td>
                </tr>
                <tr>
                    <td>CMDB (Configuration Management Database)</td>
                    <td>Base de données contenant les informations relatives aux composants d'un système informatique.
                    </td>
                </tr>
                <tr>
                    <td>Conteneurisation</td>
                    <td>Méthode de virtualisation au niveau du système d'exploitation permettant d'exécuter des
                        applications dans des environnements isolés appelés conteneurs.</td>
                </tr>
                <tr>
                    <td>Docker</td>
                    <td>Plateforme de conteneurisation permettant de créer, déployer et exécuter des applications dans
                        des conteneurs.</td>
                </tr>
                <tr>
                    <td>FastAPI</td>
                    <td>Framework Python moderne pour le développement d'API RESTful.</td>
                </tr>
                <tr>
                    <td>Kubernetes</td>
                    <td>Système open-source d'orchestration de conteneurs.</td>
                </tr>
                <tr>
                    <td>Machine Learning</td>
                    <td>Sous-domaine de l'intelligence artificielle qui utilise des algorithmes statistiques pour
                        permettre aux systèmes informatiques d'apprendre à partir de données.</td>
                </tr>
                <tr>
                    <td>OpenShift</td>
                    <td>Plateforme d'application conteneurisée de Red Hat basée sur Kubernetes.</td>
                </tr>
                <tr>
                    <td>Zabbix</td>
                    <td>Logiciel de surveillance réseau open-source.</td>
                </tr>
            </tbody>
        </table>

        <h3>Annexe 2 : Exemples de maquettes d'interface utilisateur</h3>
        <p>Les maquettes détaillées de l'interface utilisateur seraient normalement incluses ici, avec des captures
            d'écran des principaux écrans de l'application.</p>

        <h3>Annexe 3 : Format des API</h3>
        <div class="code">
            <pre>// Exemple de format d'API pour la récupération des métriques
GET /api/v1/servers/{server_id}/metrics
Response:
{
  "server_id": "12345",
  "server_name": "prod-app-01",
  "metrics": [
    {
      "name": "cpu_usage",
      "value": 45.7,
      "unit": "%",
      "timestamp": "2025-02-10T14:30:00Z"
    },
    {
      "name": "memory_usage",
      "value": 6.2,
      "unit": "GB",
      "timestamp": "2025-02-10T14:30:00Z"
    }
  ]
}
            </pre>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'neutral',
                flowchart: { useMaxWidth: true },
                securityLevel: 'loose'
            });
        });
    </script>


    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDo5neXYPodaT3NcEaCA9ifEcVHorXCda29uWICnSKSWWO6FgMAKgXJ8zvaisPE2bd1VYmubBNzeqsvfv%2B3JfP1p0VTLBeHq5MiF5QyU7KpyqydLz7FTDsiWL000JpaFVX4elKGF2vuqPJxJZEtsbGjd2A1L4RtrefhYIwI7bgWFwv2f%2FZ3He92r7g1OOCkW2EzoddIjPuu2OmRcNR7tlV4y2CVHa2BNHafOaK1TN2VJNRcnR9O%2FcT7NM5vMYepjSreondwO3I3XSOuGFqmcOP0S9CTQXbhrFd8VeVovjGVd9YSnjFhqB9yM0mfn2zXpeAkivyFCHSgRJW%2FAeo3Swnk8PsMNUW%2Be9OjtTFp1ywWkDE%2B9YHUsF8tAhfAX%2BfDWu%2BgKwgBPnVmI4R31%2FFc0CuyEjSNVDFptG7d9tn3LgLLdvG3NOKOFsxBnslG89bw9jrY5dz8B%2Bj0dTX8Oj8y%2FOiB6wgIT7N2cPYc0LIOMlVn1xgwmOuOfsTjdj3IXf%2BHPIRKwwqr%2BZSIoi2atOtOYdEdw%3D";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDo5neXYPodaT3NcEaCA9ifEcVHorXCda29uWICnSKSWWO6FgMAKgXJ8zvaisPE2bd1VYmubBNzeqsvfv+3JfP1p0VTLBeHq5MiF5QyU7KpyqydLz7FTDsiWL000JpaFVX4elKGF2vuqPJxJZEtsbGjd2A1L4RtrefhYIwI7bgWFwv2f/Z3He92r7g1OOCkW2EzoddIjPuu2OmRcNR7tlV4y2CVHa2BNHafOaK1TN2VJNRcnR9O/cT7NM5vMYepjSreondwO3I3XSOuGFqmcOP0S9CTQXbhrFd8VeVovjGVd9YSnjFhqB9yM0mfn2zXpeAkivyFCHSgRJW/Aeo3Swnk8PsMNUW+e9OjtTFp1ywWkDE+9YHUsF8tAhfAX+fDWu+gKwgBPnVmI4R31/Fc0CuyEjSNVDFptG7d9tn3LgLLdvG3NOKOFsxBnslG89bw9jrY5dz8B+j0dTX8Oj8y/OiB6wgIT7N2cPYc0LIOMlVn1xgwmOuOfsTjdj3IXf+HPIRKwwqr+ZSIoi2atOtOYdEdw=";
    </script>

    <script id="html_badge_script2"
        src="./Cahier des Charges PFE - CloudCapacity_files/html_badge.js.téléchargement"></script><button
        class="genspark-badge-button"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14"
            fill="none">
            <path
                d="M11.3412 0H2.65879C1.19038 0 0 1.19038 0 2.65879V11.3412C0 12.8096 1.19038 14 2.65879 14H11.3412C12.8096 14 14 12.8096 14 11.3412V2.65879C14 1.19038 12.8096 0 11.3412 0Z"
                fill="white"></path>
            <path
                d="M11.7403 10.7031H2.29243C2.09641 10.7031 1.9375 10.862 1.9375 11.0581V11.8033C1.9375 11.9993 2.09641 12.1582 2.29243 12.1582H11.7403C11.9363 12.1582 12.0952 11.9993 12.0952 11.8033V11.0581C12.0952 10.862 11.9363 10.7031 11.7403 10.7031Z"
                fill="#232425"></path>
            <path
                d="M5.09178 9.18166C5.03494 9.18166 4.98695 9.13998 4.97811 9.08314C4.60803 6.63655 4.34025 6.42056 1.91134 6.05427C1.83682 6.0429 1.78125 5.97848 1.78125 5.9027C1.78125 5.82691 1.83682 5.7625 1.91134 5.75113C4.32762 5.3861 4.54235 5.17011 4.90738 2.7551C4.91874 2.68058 4.98316 2.625 5.05894 2.625C5.13473 2.625 5.19914 2.68058 5.21051 2.7551C5.57554 5.17011 5.79153 5.3861 8.20655 5.75113C8.28107 5.7625 8.33664 5.82691 8.33664 5.9027C8.33664 5.97848 8.28107 6.0429 8.20655 6.05427C5.78017 6.42056 5.57302 6.63655 5.20546 9.08314C5.19662 9.13871 5.14862 9.18166 5.09178 9.18166Z"
                fill="#232425"></path>
            <path
                d="M9.70174 5.949C9.66637 5.949 9.63606 5.92248 9.63101 5.88711C9.39986 4.35878 9.23188 4.22363 7.71492 3.99501C7.66818 3.98743 7.63281 3.94828 7.63281 3.90028C7.63281 3.85355 7.66692 3.81313 7.71492 3.80555C9.2243 3.5782 9.35945 3.44305 9.5868 1.93366C9.59438 1.88693 9.63354 1.85156 9.68153 1.85156C9.72827 1.85156 9.76869 1.88567 9.77627 1.93366C10.0036 3.44305 10.1388 3.5782 11.6482 3.80555C11.6949 3.81313 11.7302 3.85228 11.7302 3.90028C11.7302 3.94702 11.6962 3.98743 11.6482 3.99501C10.1325 4.22363 10.0024 4.35878 9.77247 5.88711C9.76742 5.92248 9.73711 5.949 9.70174 5.949Z"
                fill="#232425"></path>
            <path
                d="M9.69114 9.76325C9.6684 9.76325 9.64946 9.74683 9.64567 9.7241C9.49915 8.75152 9.39179 8.66563 8.42679 8.52038C8.39648 8.51533 8.375 8.49007 8.375 8.45975C8.375 8.42944 8.39648 8.40418 8.42679 8.39912C9.38673 8.25387 9.47262 8.16798 9.61788 7.20804C9.62293 7.17772 9.64819 7.15625 9.6785 7.15625C9.70882 7.15625 9.73408 7.17772 9.73913 7.20804C9.88439 8.16798 9.97028 8.25387 10.9302 8.39912C10.9605 8.40418 10.982 8.42944 10.982 8.45975C10.982 8.49007 10.9605 8.51533 10.9302 8.52038C9.96523 8.66563 9.88312 8.75152 9.73661 9.7241C9.73282 9.74683 9.71387 9.76325 9.69114 9.76325Z"
                fill="#232425"></path>
        </svg> Made with Genspark</button>
    <div class="genspark-modal" style="display: none;">
        <div class="genspark-modal-content">
            <button class="genspark-close"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14"
                    viewBox="0 0 14 14" fill="none">
                    <path d="M11 3L3 11M3 3L11 11" stroke="#232425" stroke-linecap="round" stroke-linejoin="round">
                    </path>
                </svg></button>
            <h3 class="genspark-title">This page was created by users with AI.</h3>
            <a class="genspark-report"
                href="mailto:<EMAIL>?subject=Report%20inappropriate%20content&amp;body=Current%20URL:%20https://bmcdzfpu.genspark.space/">Report
                inappropriate content.</a>
            <p class="genspark-info">Page owner with Plus Plan can remove badge.</p>
            <div class="genspark-buttons">
                <button class="genspark-remove-btn">Remove Badge</button>
                <button class="genspark-go-btn">Go to Genspark</button>
            </div>
        </div>
    </div>
    <div class="mermaidTooltip" style="opacity: 0;"></div>
</body>

</html>