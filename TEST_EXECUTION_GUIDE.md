# 🧪 CloudCapacity Test Execution Guide

This guide provides step-by-step instructions for running all tests in the CloudCapacity application, including backend and frontend test suites.

## 📋 Quick Start

### Prerequisites
- **Backend**: Python 3.8+, Virtual Environment
- **Frontend**: Node.js 16+, npm/yarn
- **System**: Windows (PowerShell), macOS, or Linux

### 🚀 Run All Tests (Quick)
```bash
# Backend Tests
cd be_cloudcapacity
python run_tests.py --all --coverage

# Frontend Tests
cd fe_cloudcapacity
node run-tests.js --all --coverage
```

## 📊 Test Execution Flow

### Visual Flow Diagram

```
🚀 Start Testing
       ↓
   Choose Test Type
       ↓
   ┌─────────────────┐
   ↓                 ↓
🐍 Backend        🅰️ Frontend
   ↓                 ↓
📦 Install Deps   📦 Check Deps
   ↓                 ↓
🧪 Run Tests      🧪 Run Tests
   ↓                 ↓
📊 View Coverage  📊 View Coverage
   ↓                 ↓
   └─────────────────┘
           ↓
    ✅ Tests Complete
           ↓
    All Tests Passed?
       ↓         ↓
   ✅ Yes    ❌ No
       ↓         ↓
  🎉 Success  🔍 Debug
               ↓
          Fix Issues
               ↓
         (Back to Run Tests)
```

### Detailed Flow Steps

#### 🐍 **Backend Testing Path**:
```bash
1. cd be_cloudcapacity
2. python run_tests.py --install-deps
3. python run_tests.py --all --coverage
4. Open htmlcov/index.html
```

#### 🅰️ **Frontend Testing Path**:
```bash
1. cd fe_cloudcapacity
2. npm install (if needed)
3. node run-tests.js --all --coverage
4. Open coverage/index.html
```

### Flow Explanation
1. **🚀 Start**: Choose between backend or frontend testing
2. **📦 Setup**: Install/check dependencies
3. **🧪 Execute**: Run comprehensive test suite
4. **📊 Review**: Check coverage reports
5. **🔍 Debug**: If failures occur, investigate and fix
6. **🎉 Complete**: All tests pass successfully

## 🐍 Backend Test Execution

### Step 1: Environment Setup
```bash
# Navigate to backend directory
cd be_cloudcapacity

# Activate virtual environment
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # macOS/Linux

# Install test dependencies
python run_tests.py --install-deps
```

### Step 2: Run Backend Tests

#### **All Tests with Coverage**
```bash
python run_tests.py --all --coverage
```
**Expected Output:**
```
📦 Installing test dependencies...
✅ Test dependencies installed successfully
🔍 Running code linting...
✅ All linting checks passed
🧪 Running unit tests...
✅ Unit tests passed
🔗 Running integration tests...
✅ Integration tests passed
📊 Generating test coverage report...
✅ Test coverage report generated
📁 HTML report available at: htmlcov/index.html
🎉 All tests completed successfully!
```

#### **Unit Tests Only**
```bash
python run_tests.py --unit
```

#### **Specific Test Files**
```bash
# Test reporting capacity endpoints
python run_tests.py --test tests/test_reporting_capacity.py

# Test server info endpoints
python run_tests.py --test tests/test_server_info.py

# Test specific function
python run_tests.py --test tests/test_reporting_capacity.py::TestReportingCapacityEndpoints::test_generate_pptx_report_success
```

#### **Linting and Code Quality**
```bash
# Run linting only
python run_tests.py --lint

# Run type checking
python run_tests.py --type-check
```

#### **Coverage Report Generation**
```bash
python run_tests.py --report
```
**View Coverage:** Open `htmlcov/index.html` in browser

### Step 3: Backend Test Options

| Command | Description |
|---------|-------------|
| `--all` | Run all tests (unit + integration + linting) |
| `--unit` | Run unit tests only |
| `--integration` | Run integration tests only |
| `--coverage` | Generate coverage report |
| `--lint` | Run code linting (flake8, black, isort) |
| `--type-check` | Run type checking (mypy) |
| `--report` | Generate detailed coverage report |
| `--clean` | Clean test artifacts and cache |
| `--verbose` | Verbose test output |

## 🅰️ Frontend Test Execution

### Step 1: Environment Setup
```bash
# Navigate to frontend directory
cd fe_cloudcapacity

# Install dependencies (if needed)
npm install

# Verify Angular CLI
ng version
```

### Step 2: Run Frontend Tests

#### **All Tests with Coverage**
```bash
node run-tests.js --all --coverage
```
**Expected Output:**
```
ℹ️ Checking dependencies...
✅ Dependencies are ready
🔍 Running linting...
✅ Linting passed
🧪 Running unit tests...
✅ Unit tests passed
🧪 Testing build...
✅ Build test passed
✅ Coverage report generated at: coverage/index.html

📊 Test Summary:
================
LINT: ✅ PASSED
UNIT: ✅ PASSED
BUILD: ✅ PASSED

🎉 All tests completed successfully!
```

#### **Unit Tests Only**
```bash
node run-tests.js --unit
```

#### **Specific Test Files**
```bash
# Test reporting capacity component
node run-tests.js --test "**/reporting-capacity*.spec.ts"

# Test export dialogs
node run-tests.js --test "**/export*.spec.ts"

# Test resource usage component
node run-tests.js --test "**/resource-usage*.spec.ts"
```

#### **Linting Only**
```bash
node run-tests.js --lint
```

#### **Build Test**
```bash
node run-tests.js --build
```

#### **Coverage Report**
```bash
node run-tests.js --coverage
```
**View Coverage:** Open `coverage/index.html` in browser

### Step 3: Frontend Test Options

| Command | Description |
|---------|-------------|
| `--all` | Run all tests (lint + unit + build) |
| `--unit` | Run unit tests only |
| `--e2e` | Run end-to-end tests |
| `--lint` | Run linting (ESLint) |
| `--build` | Test production build |
| `--coverage` | Generate coverage report |
| `--clean` | Clean test artifacts |
| `--include-e2e` | Include E2E tests with --all |

## 🎯 Specific Test Scenarios

### Export Functionality Tests

#### **Backend Export Tests**
```bash
cd be_cloudcapacity

# Test PPTX generation
python run_tests.py --test tests/test_reporting_capacity.py::TestReportingCapacityEndpoints::test_generate_pptx_report_success

# Test capacity report generation
python run_tests.py --test tests/test_reporting_capacity.py::TestReportingCapacityEndpoints::test_generate_capacity_report_success

# Test server configurations
python run_tests.py --test tests/test_reporting_capacity.py::TestReportingCapacityEndpoints::test_get_server_configurations_all
```

#### **Frontend Export Tests**
```bash
cd fe_cloudcapacity

# Test export dialog functionality
node run-tests.js --test "**/reporting-capacity-export-dialog*.spec.ts"

# Test PNG export
node run-tests.js --test "**/export-dialog*.spec.ts"

# Test multi-server export
node run-tests.js --test "**/resource-usage/export*.spec.ts"
```

### Component-Specific Tests

#### **Reporting Capacity Component**
```bash
cd fe_cloudcapacity

# Test main component
node run-tests.js --test "**/reporting-capacity.component.spec.ts"

# Test export dialog
node run-tests.js --test "**/reporting-capacity-export-dialog.component.spec.ts"
```

#### **Resource Usage Component**
```bash
cd fe_cloudcapacity

# Test export functionality
node run-tests.js --test "**/resource-usage/export-dialog.component.spec.ts"
```

## 📊 Coverage Reports

### Backend Coverage
```bash
cd be_cloudcapacity
python run_tests.py --coverage --report

# View coverage report
# Windows: start htmlcov/index.html
# macOS: open htmlcov/index.html
# Linux: xdg-open htmlcov/index.html
```

### Frontend Coverage
```bash
cd fe_cloudcapacity
node run-tests.js --coverage

# View coverage report
# Windows: start coverage/index.html
# macOS: open coverage/index.html
# Linux: xdg-open coverage/index.html
```

## 🧹 Cleanup and Maintenance

### Clean Test Artifacts
```bash
# Backend cleanup
cd be_cloudcapacity
python run_tests.py --clean

# Frontend cleanup
cd fe_cloudcapacity
node run-tests.js --clean
```

### Reinstall Dependencies
```bash
# Backend
cd be_cloudcapacity
python run_tests.py --install-deps

# Frontend
cd fe_cloudcapacity
npm install
```

## ❌ Troubleshooting

### Common Issues

#### **Backend Issues**
```bash
# Virtual environment not activated
.\venv\Scripts\activate

# Missing test dependencies
python run_tests.py --install-deps

# Database connection issues
python run_tests.py --clean
```

#### **Frontend Issues**
```bash
# Node modules issues
rm -rf node_modules package-lock.json
npm install

# Angular CLI issues
npm install -g @angular/cli

# Chrome headless issues (Windows)
# Install Chrome browser if not present
```

### Test Failures

#### **Check Test Output**
```bash
# Backend verbose output
python run_tests.py --unit --verbose

# Frontend detailed output
node run-tests.js --unit
```

#### **Individual Test Debugging**
```bash
# Run single test file
python run_tests.py --test tests/test_reporting_capacity.py
node run-tests.js --test "**/reporting-capacity.component.spec.ts"
```

## 🚀 Continuous Integration

### GitHub Actions Example
```yaml
name: Run Tests
on: [push, pull_request]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Run Backend Tests
        run: |
          cd be_cloudcapacity
          python -m venv venv
          source venv/bin/activate
          python run_tests.py --all --coverage

  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Run Frontend Tests
        run: |
          cd fe_cloudcapacity
          npm ci
          node run-tests.js --all --coverage
```

## 📝 Test Results Interpretation

### Success Indicators
- ✅ **All tests passed**
- ✅ **Coverage above 85%**
- ✅ **No linting errors**
- ✅ **Build successful**

### Failure Investigation
1. **Read error messages carefully**
2. **Check test logs for specific failures**
3. **Verify environment setup**
4. **Run individual failing tests**
5. **Check dependencies and versions**

## 📋 Quick Reference Card

### Backend Commands
```bash
cd be_cloudcapacity
python run_tests.py --all --coverage    # Run everything
python run_tests.py --unit              # Unit tests only
python run_tests.py --lint              # Linting only
python run_tests.py --clean             # Clean artifacts
```

### Frontend Commands
```bash
cd fe_cloudcapacity
node run-tests.js --all --coverage      # Run everything
node run-tests.js --unit                # Unit tests only
node run-tests.js --lint                # Linting only
node run-tests.js --clean               # Clean artifacts
```

### Export-Specific Tests
```bash
# Backend PPTX tests
python run_tests.py --test tests/test_reporting_capacity.py

# Frontend export tests
node run-tests.js --test "**/export*.spec.ts"
```

### Coverage Reports
```bash
# Backend: htmlcov/index.html
# Frontend: coverage/index.html
```

---

**Need Help?**
- Check the main [TESTING.md](TESTING.md) for detailed test information
- Review individual test files for specific test cases
- Ensure all prerequisites are installed and configured correctly
