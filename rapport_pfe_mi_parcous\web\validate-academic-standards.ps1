# Script de Validation - Standards Académiques PFE
# Vérifie la conformité du rapport aux standards académiques tunisiens

param(
    [string]$HtmlFile = "rapport_latex.html"
)

Write-Host "=== Validation Standards Académiques PFE ===" -ForegroundColor Cyan
Write-Host ""

$errors = @()
$warnings = @()
$success = @()

# Vérification de l'existence du fichier
if (-not (Test-Path $HtmlFile)) {
    $errors += "❌ Fichier HTML non trouvé: $HtmlFile"
    exit 1
}

$htmlContent = Get-Content $HtmlFile -Raw

# 1. Vérifier la structure de la page de titre
Write-Host "🔍 Vérification de la page de titre..." -ForegroundColor Yellow

$titleElements = @(
    "RAPPORT DE PROJET DE FIN D'ÉTUDES",
    "Présenté en vue de l'obtention du diplôme",
    "CloudCapacity",
    "Encadrant Académique",
    "Encadrant Professionnel",
    "INGÉNIEUR EN INFORMATIQUE",
    "Année Universitaire 2024-2025"
)

foreach ($element in $titleElements) {
    if ($htmlContent -match [regex]::Escape($element)) {
        $success += "✅ Élément page de titre trouvé: $element"
    } else {
        $errors += "❌ Élément page de titre manquant: $element"
    }
}

# 2. Vérifier les sections préliminaires
Write-Host "🔍 Vérification des sections préliminaires..." -ForegroundColor Yellow

$preliminarySections = @(
    "Dédicaces",
    "Remerciements", 
    "Résumé",
    "Abstract",
    "Table des matières",
    "Liste des figures",
    "Liste des tableaux",
    "Liste des abréviations"
)

foreach ($section in $preliminarySections) {
    if ($htmlContent -match "<h2>$section</h2>") {
        $success += "✅ Section préliminaire trouvée: $section"
    } else {
        $warnings += "⚠️ Section préliminaire manquante: $section"
    }
}

# 3. Vérifier la structure du résumé
Write-Host "🔍 Vérification de la structure du résumé..." -ForegroundColor Yellow

$resumeStructure = @(
    "Contexte :",
    "Problématique :",
    "Objectif :",
    "Méthodologie :",
    "Résultats :",
    "Impact :",
    "Mots-clés :"
)

foreach ($element in $resumeStructure) {
    if ($htmlContent -match [regex]::Escape($element)) {
        $success += "✅ Élément résumé structuré trouvé: $element"
    } else {
        $warnings += "⚠️ Élément résumé structuré manquant: $element"
    }
}

# 4. Vérifier l'introduction structurée
Write-Host "🔍 Vérification de l'introduction structurée..." -ForegroundColor Yellow

$introSections = @(
    "Contexte général",
    "Problématique", 
    "Objectifs du projet",
    "Approche méthodologique",
    "Structure du rapport"
)

foreach ($section in $introSections) {
    if ($htmlContent -match [regex]::Escape($section)) {
        $success += "✅ Section introduction trouvée: $section"
    } else {
        $warnings += "⚠️ Section introduction manquante: $section"
    }
}

# 5. Vérifier les chapitres académiques
Write-Host "🔍 Vérification des chapitres académiques..." -ForegroundColor Yellow

$chapters = @(
    "Introduction générale",
    "Chapitre 1 : Étude de l'existant",
    "Chapitre 2 : Analyse des besoins",
    "Chapitre 3 : Conception et architecture", 
    "Chapitre 4 : Réalisation et implémentation",
    "Chapitre 5 : Tests et validation",
    "Chapitre 6 : Bilan et perspectives",
    "Conclusion générale"
)

$foundChapters = 0
foreach ($chapter in $chapters) {
    if ($htmlContent -match [regex]::Escape($chapter)) {
        $foundChapters++
        $success += "✅ Chapitre trouvé: $chapter"
    } else {
        $warnings += "⚠️ Chapitre manquant: $chapter"
    }
}

# 6. Vérifier les classes CSS académiques
Write-Host "🔍 Vérification des styles académiques..." -ForegroundColor Yellow

$academicClasses = @(
    "project-title",
    "supervision-section",
    "dedication-content",
    "acknowledgments-content",
    "abstract-content",
    "introduction-section",
    "objectives-list",
    "latex-section"
)

foreach ($class in $academicClasses) {
    if ($htmlContent -match "class=`"[^`"]*$class[^`"]*`"") {
        $success += "✅ Classe CSS académique trouvée: $class"
    } else {
        $warnings += "⚠️ Classe CSS académique manquante: $class"
    }
}

# 7. Vérifier les diagrammes
Write-Host "🔍 Vérification des diagrammes..." -ForegroundColor Yellow

$diagrams = @(
    "architecture-generale",
    "cas-utilisation",
    "sequence-collecte",
    "modele-donnees"
)

foreach ($diagram in $diagrams) {
    if ($htmlContent -match "id=`"$diagram`"") {
        $success += "✅ Diagramme trouvé: $diagram"
    } else {
        $warnings += "⚠️ Diagramme manquant: $diagram"
    }
}

# 8. Vérifier la longueur et la qualité du contenu
Write-Host "🔍 Vérification de la qualité du contenu..." -ForegroundColor Yellow

$wordCount = ($htmlContent -split '\s+').Count
if ($wordCount -gt 5000) {
    $success += "✅ Longueur du rapport appropriée ($wordCount mots)"
} else {
    $warnings += "⚠️ Rapport potentiellement trop court ($wordCount mots)"
}

# Vérifier la présence de termes académiques
$academicTerms = @("problématique", "méthodologie", "objectif", "analyse", "conception", "implémentation", "validation")
$foundTerms = 0
foreach ($term in $academicTerms) {
    if ($htmlContent -match $term) {
        $foundTerms++
    }
}

if ($foundTerms -ge 5) {
    $success += "✅ Vocabulaire académique approprié ($foundTerms/$($academicTerms.Count) termes)"
} else {
    $warnings += "⚠️ Vocabulaire académique insuffisant ($foundTerms/$($academicTerms.Count) termes)"
}

# Affichage des résultats
Write-Host ""
Write-Host "📊 RÉSULTATS DE LA VALIDATION" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

if ($success.Count -gt 0) {
    Write-Host ""
    Write-Host "✅ CONFORMITÉ ACADÉMIQUE ($($success.Count)):" -ForegroundColor Green
    foreach ($item in $success) {
        Write-Host "  $item" -ForegroundColor Green
    }
}

if ($warnings.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️ AMÉLIORATIONS POSSIBLES ($($warnings.Count)):" -ForegroundColor Yellow
    foreach ($item in $warnings) {
        Write-Host "  $item" -ForegroundColor Yellow
    }
}

if ($errors.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ PROBLÈMES CRITIQUES ($($errors.Count)):" -ForegroundColor Red
    foreach ($item in $errors) {
        Write-Host "  $item" -ForegroundColor Red
    }
}

# Calcul du score de conformité
$totalChecks = $success.Count + $warnings.Count + $errors.Count
$conformityScore = if ($totalChecks -gt 0) { [math]::Round(($success.Count / $totalChecks) * 100, 1) } else { 0 }

Write-Host ""
Write-Host "📈 SCORE DE CONFORMITÉ ACADÉMIQUE:" -ForegroundColor Cyan
Write-Host "  Score global: $conformityScore%" -ForegroundColor White
Write-Host "  Chapitres trouvés: $foundChapters/$($chapters.Count)" -ForegroundColor White
Write-Host "  Sections préliminaires: $($preliminarySections.Count - $warnings.Count)/$($preliminarySections.Count)" -ForegroundColor White

# Conclusion
Write-Host ""
if ($errors.Count -eq 0 -and $conformityScore -ge 80) {
    Write-Host "🎓 VALIDATION RÉUSSIE - Rapport conforme aux standards académiques !" -ForegroundColor Green
    Write-Host "   Le rapport respecte les critères des PFE tunisiens." -ForegroundColor Green
    exit 0
} elseif ($errors.Count -eq 0) {
    Write-Host "📝 VALIDATION PARTIELLE - Rapport globalement conforme." -ForegroundColor Yellow
    Write-Host "   Quelques améliorations mineures possibles." -ForegroundColor Yellow
    exit 0
} else {
    Write-Host "💥 VALIDATION ÉCHOUÉE - Problèmes critiques détectés." -ForegroundColor Red
    Write-Host "   Veuillez corriger les erreurs avant la soutenance." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Fin de la Validation ===" -ForegroundColor Cyan
