<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport PFE - CloudCapacity</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <link rel="stylesheet" href="styles/rapport-styles.css">
    <script src="scripts/diagram-loader.js"></script>
</head>

<body>
    <!-- Page de garde -->
    <div class="page titlepage">
        <div class="titlepage-header">
            <div class="logo-container">
                <img src="../PFE_docs/rapports_CloudCapacity_PFE/Logo-SESAME-png.png" alt="Logo SESAME" class="logo">
            </div>
            <div class="republic-info">
                <div><strong>RÉPUBLIQUE TUNISIENNE</strong></div>
                <div>Ministère de l'Enseignement Supérieur</div>
                <div>et de la Recherche Scientifique</div>
            </div>
        </div>

        <h1>RAPPORT DE PROJET DE FIN D'ÉTUDES</h1>
        <div class="subtitle">Présenté en vue de l'obtention du diplôme d'Ingénieur en Informatique</div>

        <div class="project-title">
            <h2>CloudCapacity</h2>
            <h3>Plateforme Web de Monitoring et d'Analyse de la Capacité des Serveurs</h3>
        </div>

        <div class="author-section">
            <div class="author-label">Réalisé par :</div>
            <div class="author-name"><strong>MAHMOUD Mohamed Amine</strong></div>
            <div class="student-id">Étudiant en 4ème année Sécurité, Cloud et Iot</div>
        </div>

        <div class="supervision-section">
            <div class="supervision-title">Sous la direction de :</div>
            <div class="supervisor">
                <strong>Encadrant Académique :</strong><br>
                M. MEZGHANNI Dhafer<br>
                <em>Enseignant à l'Université Sésame</em>
            </div>
            <div class="supervisor">
                <strong>Encadrants Professionnels :</strong><br>
                M. DELIL Yassine<br>
                <em>Manager DevOps - Sopra HR Software</em><br><br>
                M. MAHMOUD Hatem<br>
                <em>Senior Technical Lead - Sopra HR Software</em>
            </div>
        </div>

        <div class="diploma-section">
            <div class="diploma-title">Pour l'obtention du diplôme de :</div>
            <div class="diploma-name"><strong>INGÉNIEUR EN INFORMATIQUE</strong></div>
            <div class="speciality">Spécialité : <strong>Sécurité, Cloud & IoT</strong></div>
        </div>

        <div class="academic-year">
            <div class="year">Année Universitaire 2024-2025</div>
            <div class="report-type"><strong>RAPPORT DE MI-PARCOURS</strong></div>
            <div class="date">Juin 2025</div>
        </div>

        <div class="company-section">
            <div class="company-info">
                <div class="company-title">Entreprise d'accueil</div>
                <div><strong>Sopra HR Software</strong></div>
                <div>Pôle DevOps & Infrastructure</div>
            </div>
        </div>
    </div>

    <!-- Dédicace -->
    <div class="page">
        <div class="dedication">
            <h2>Dédicaces</h2>
            <div class="dedication-content">
                <p class="dedication-text">
                    À mes chers parents,<br>
                    Pour leur amour inconditionnel, leurs sacrifices et leur soutien constant tout au long de mon
                    parcours académique.
                </p>

                <p class="dedication-text">
                    À ma famille,<br>
                    Pour leur patience, leurs encouragements et leur confiance en mes capacités.
                </p>

                <p class="dedication-text">
                    À tous mes enseignants,<br>
                    Qui ont contribué à ma formation et m'ont transmis leur passion pour l'informatique.
                </p>

                <p class="dedication-text">
                    À mes amis et collègues,<br>
                    Pour leur soutien moral et les moments partagés durant ces années d'études.
                </p>
            </div>
        </div>
    </div>

    <!-- Remerciements -->
    <div class="page">
        <div class="acknowledgements">
            <h2>Remerciements</h2>
            <div class="acknowledgments-content">
                <p>
                    Au terme de ce travail, je tiens à exprimer ma profonde gratitude à toutes les personnes qui ont
                    contribué, de près ou de loin, à la réalisation de ce projet de fin d'études.
                </p>

                <p>
                    Mes remerciements s'adressent tout d'abord à <strong>Monsieur MEZGHANNI Dhafer</strong>, mon
                    encadrant
                    académique, pour ses conseils précieux, sa disponibilité constante et son suivi rigoureux tout au
                    long de ce projet. Ses orientations méthodologiques et ses remarques constructives ont été
                    déterminantes pour la qualité de ce travail.
                </p>

                <p>
                    Je remercie également <strong>Monsieur DELIL Yassine</strong>, mon manager et encadrant
                    professionnel chez
                    Sopra HR Software, pour son expertise technique, ses orientations pratiques et pour m'avoir fait
                    confiance dans la réalisation de ce projet innovant. Son expérience dans le domaine du DevOps et
                    de l'infrastructure a été un atout majeur.
                </p>

                <p>
                    Je tiens aussi à remercier <strong>Monsieur MAHMOUD Hatem</strong>, architecte technique spécialisé
                    dans les solutions complexes, pour son accompagnement technique et ses conseils avisés. Son
                    expertise
                    approfondie dans les domaines des bases de données et de l'intelligence artificielle a grandement
                    enrichi ce projet et ouvert des perspectives d'évolution prometteuses.
                </p>

                <p>
                    Ma reconnaissance va aussi à l'ensemble de l'équipe DevOps et Infrastructure de Sopra HR Software
                    qui m'a accueilli dans un environnement professionnel stimulant et m'a permis de découvrir les
                    enjeux réels du monitoring des systèmes d'information en entreprise.
                </p>

                <p>
                    Je remercie l'ensemble du corps professoral de l'Université Sésame, et particulièrement les
                    enseignants du département Informatique, pour la qualité de la formation dispensée durant ces cinq
                    années d'études et pour m'avoir donné les bases solides nécessaires à la réalisation de ce projet.
                </p>

                <p>
                    Enfin, je remercie ma famille et mes amis pour leur soutien moral, leur patience et leurs
                    encouragements constants qui m'ont permis de mener à bien ce projet dans les meilleures conditions.
                </p>
            </div>
        </div>
    </div>

    <!-- Résumés -->
    <div class="page">
        <div class="abstract">
            <h2>Résumé</h2>
            <div class="abstract-content">
                <p>
                    <strong>Contexte :</strong> Dans un environnement informatique en constante évolution, la gestion
                    optimale des ressources serveurs constitue un enjeu majeur pour les entreprises. Sopra HR Software,
                    éditeur de solutions RH, fait face à des défis de monitoring et d'analyse de la capacité de son
                    infrastructure on-premise composée de plus de 400 serveurs.
                </p>

                <p>
                    <strong>Problématique :</strong> L'absence d'une solution centralisée de monitoring de capacité
                    entraîne des difficultés dans la planification des ressources, la détection proactive des surcharges
                    et la génération de rapports de capacité pour les équipes techniques et managériales.
                </p>

                <p>
                    <strong>Objectif :</strong> Ce projet de fin d'études vise à concevoir et développer CloudCapacity,
                    une plateforme web moderne permettant d'automatiser le monitoring, l'analyse et le reporting de la
                    capacité des serveurs, tout en intégrant des fonctionnalités d'alertes intelligentes et de
                    comparaisons temporelles.
                </p>

                <p>
                    <strong>Méthodologie :</strong> L'approche adoptée combine une analyse approfondie des besoins
                    métier, une conception architecturale basée sur des technologies modernes (Angular 19, FastAPI,
                    Oracle), et une intégration native avec l'infrastructure de monitoring existante (Zabbix).
                </p>

                <p>
                    <strong>Résultats :</strong> À mi-parcours, la solution CloudCapacity présente un taux d'avancement
                    de 85% avec l'implémentation complète du frontend Angular 19, du backend FastAPI modulaire, et de
                    l'intégration Zabbix. Les fonctionnalités opérationnelles incluent un dashboard interactif, un
                    système de comparaison d'usage sophistiqué, et un module d'alertes configurables.
                </p>

                <p>
                    <strong>Impact :</strong> La plateforme démontre déjà sa capacité à centraliser la supervision des
                    ressources, automatiser le reporting de capacité et améliorer la prise de décision concernant
                    l'évolution de l'infrastructure, répondant ainsi aux objectifs initiaux du projet.
                </p>

                <p>
                    <strong>Mots-clés :</strong> Monitoring, Capacité serveurs, Angular 19, FastAPI, Zabbix, Dashboard,
                    Alertes, Reporting, Infrastructure, DevOps.
                </p>
            </div>
        </div>

        <div class="abstract">
            <h2>Abstract</h2>
            <div class="abstract-content">
                <p>
                    <strong>Context:</strong> In a constantly evolving IT environment, optimal server resource
                    management constitutes a major challenge for enterprises. Sopra HR Software, an HR solutions
                    publisher, faces monitoring and capacity analysis challenges for its on-premise infrastructure
                    comprising over 50 servers.
                </p>

                <p>
                    <strong>Problem Statement:</strong> The absence of a centralized capacity monitoring solution leads
                    to difficulties in resource planning, proactive overload detection, and capacity report generation
                    for technical and managerial teams.
                </p>

                <p>
                    <strong>Objective:</strong> This graduation project aims to design and develop CloudCapacity, a
                    modern web platform enabling automation of server capacity monitoring, analysis, and reporting,
                    while integrating intelligent alerting features and temporal comparisons.
                </p>

                <p>
                    <strong>Methodology:</strong> The adopted approach combines thorough business needs analysis,
                    architectural design based on modern technologies (Angular 19, FastAPI, Oracle), and native
                    integration with existing monitoring infrastructure (Zabbix).
                </p>

                <p>
                    <strong>Results:</strong> At mid-term, the CloudCapacity solution presents an 85% completion rate
                    with full implementation of the Angular 19 frontend, modular FastAPI backend, and Zabbix
                    integration. Operational features include an interactive dashboard, sophisticated usage comparison
                    system, and configurable alert module.
                </p>

                <p>
                    <strong>Impact:</strong> The platform already demonstrates its ability to centralize resource
                    supervision, automate capacity reporting, and improve decision-making regarding infrastructure
                    evolution, thus meeting the initial project objectives.
                </p>

                <p>
                    <strong>Keywords:</strong> Monitoring, Server Capacity, Angular 19, FastAPI, Zabbix, Dashboard,
                    Alerts, Reporting, Infrastructure, DevOps.
                </p>
            </div>
        </div>
    </div>

    <!-- Table des matières -->
    <div class="page">
        <h2>Table des matières</h2>
        <div class="toc">
            <div>Dédicace</div>
            <div>Remerciements</div>
            <div>Résumé</div>
            <div>Abstract</div>
            <div>Table des matières</div>
            <div>Liste des figures</div>
            <div>Liste des tableaux</div>
            <div>Liste des abréviations et acronymes</div>

            <div class="font-bold mt-2">Introduction générale</div>

            <div class="font-bold mt-2">Chapitre 1 : Cadre général du projet</div>
            <div class="toc-item">1.1 Présentation de l'organisme d'accueil</div>
            <div class="toc-subsection">1.1.1 Sopra HR Software</div>
            <div class="toc-subsection">1.1.2 Pôle DevOps & Infrastructure</div>
            <div class="toc-item">1.2 Contexte et problématique</div>
            <div class="toc-subsection">1.2.1 Situation actuelle</div>
            <div class="toc-subsection">1.2.2 Problèmes identifiés</div>
            <div class="toc-item">1.3 Objectifs du projet</div>
            <div class="toc-item">1.4 Méthodologie de travail</div>

            <div class="font-bold mt-2">Chapitre 2 : Étude de l'existant et analyse des besoins</div>
            <div class="toc-item">2.1 Analyse de l'infrastructure existante</div>
            <div class="toc-subsection">2.1.1 Architecture serveurs on-premise</div>
            <div class="toc-subsection">2.1.2 Outils de monitoring actuels (Zabbix)</div>
            <div class="toc-item">2.2 Analyse critique et limites</div>
            <div class="toc-item">2.3 Spécification des besoins</div>
            <div class="toc-subsection">2.3.1 Besoins fonctionnels</div>
            <div class="toc-subsection">2.3.2 Besoins non fonctionnels</div>

            <div class="font-bold mt-2">Chapitre 3 : Conception et architecture</div>
            <div class="toc-item">3.1 Architecture générale du système</div>
            <div class="toc-subsection">3.1.1 Vue d'ensemble</div>
            <div class="toc-subsection">3.1.2 Composants principaux</div>
            <div class="toc-item">3.2 Modélisation UML</div>
            <div class="toc-subsection">3.2.1 Diagrammes de cas d'utilisation</div>
            <div class="toc-subsection">3.2.2 Diagrammes de séquence</div>
            <div class="toc-subsection">3.2.3 Modèle de données</div>
            <div class="toc-item">3.3 Choix technologiques</div>
            <div class="toc-subsection">3.3.1 Frontend - Angular 19</div>
            <div class="toc-subsection">3.3.2 Backend - FastAPI</div>
            <div class="toc-subsection">3.3.3 Base de données - Oracle</div>
            <div class="toc-subsection">3.3.4 Intégration Zabbix</div>

            <div class="font-bold mt-2">Chapitre 4 : Réalisation et implémentation</div>
            <div class="toc-item">4.1 Environnement de développement</div>
            <div class="toc-item">4.2 Implémentation du backend</div>
            <div class="toc-subsection">4.2.1 API REST avec FastAPI</div>
            <div class="toc-subsection">4.2.2 Intégration API Zabbix</div>
            <div class="toc-subsection">4.2.3 Endpoints et services</div>
            <div class="toc-item">4.3 Implémentation du frontend</div>
            <div class="toc-subsection">4.3.1 Interface utilisateur Angular</div>
            <div class="toc-subsection">4.3.2 Composants développés</div>
            <div class="toc-subsection">4.3.3 Thème Modern Dark Dashboard</div>
            <div class="toc-item">4.4 Fonctionnalités implémentées</div>
            <div class="toc-subsection">4.4.1 Dashboard et métriques</div>
            <div class="toc-subsection">4.4.2 Gestion des serveurs</div>
            <div class="toc-subsection">4.4.3 Comparaison d'usage</div>
            <div class="toc-subsection">4.4.4 Système d'alertes</div>

            <div class="font-bold mt-2">Chapitre 5 : Tests et validation</div>
            <div class="toc-item">5.1 Tests réalisés</div>
            <div class="toc-item">5.2 Validation des fonctionnalités</div>
            <div class="toc-item">5.3 Performances et optimisations</div>

            <div class="font-bold mt-2">Bilan et perspectives</div>
            <div class="toc-item">6.1 État d'avancement</div>
            <div class="toc-item">6.2 Difficultés rencontrées</div>
            <div class="toc-item">6.3 Perspectives et améliorations futures</div>

            <div class="font-bold mt-2">Bibliographie</div>

            <div class="font-bold mt-2">Annexes</div>
            <div class="toc-item">Annexe A : Code source principal</div>
            <div class="toc-item">Annexe B : Documentation API</div>
            <div class="toc-item">Annexe C : Guide d'installation</div>
        </div>
    </div>

    <!-- Liste des figures -->
    <div class="page">
        <h2>Liste des figures</h2>
        <div class="toc">
            <div>Figure 1.1 : Infrastructure serveurs Sopra HR Software</div>
            <div>Figure 2.1 : Processus de monitoring actuel avec Zabbix</div>
            <div>Figure 3.1 : Architecture générale de CloudCapacity</div>
            <div>Figure 3.2 : Diagramme de cas d'utilisation</div>
            <div>Figure 3.3 : Diagramme de séquence - Collecte de métriques</div>
            <div>Figure 3.4 : Modèle de données relationnel</div>
            <div>Figure 4.1 : Interface Dashboard principal</div>
            <div>Figure 4.2 : Interface Liste des serveurs avec filtrage</div>
            <div>Figure 4.3 : Interface Détails serveur avec métriques</div>
            <div>Figure 4.4 : Interface Comparaison d'usage entre serveurs</div>
            <div>Figure 4.5 : Interface Comparaison d'usage temporelle</div>
            <div>Figure 4.6 : Interface Alertes de seuil</div>
            <div>Figure 4.7 : Thème Modern Dark Dashboard</div>
            <div>Figure 4.8 : Architecture des composants Angular</div>
            <div>Figure 4.9 : Structure des endpoints API FastAPI</div>
            <div>Figure 5.1 : Résultats des tests fonctionnels</div>
            <div>Figure 5.2 : Métriques de performance</div>
        </div>
    </div>

    <!-- Liste des tableaux -->
    <div class="page">
        <h2>Liste des tableaux</h2>
        <div class="toc">
            <div>Tableau 1.1 : Caractéristiques de l'infrastructure actuelle</div>
            <div>Tableau 2.1 : Besoins fonctionnels identifiés</div>
            <div>Tableau 2.2 : Besoins non fonctionnels</div>
            <div>Tableau 3.1 : Comparaison des technologies frontend</div>
            <div>Tableau 3.2 : Comparaison des technologies backend</div>
            <div>Tableau 4.1 : Endpoints API implémentés</div>
            <div>Tableau 4.2 : Composants Angular développés</div>
            <div>Tableau 4.3 : Services backend implémentés</div>
            <div>Tableau 4.4 : Modèles de données SQLModel</div>
            <div>Tableau 4.5 : Fonctionnalités par composant</div>
            <div>Tableau 5.1 : État d'avancement des fonctionnalités</div>
            <div>Tableau 5.2 : Résultats des tests fonctionnels</div>
            <div>Tableau 6.1 : Planning de développement restant</div>
        </div>
    </div>

    <!-- Liste des abréviations -->
    <div class="page">
        <h2>Liste des abréviations et acronymes</h2>
        <table class="table-data">
            <tr>
                <th>Acronyme</th>
                <th>Définition</th>
            </tr>
            <tr>
                <td>API</td>
                <td>Application Programming Interface</td>
            </tr>
            <tr>
                <td>CMDB</td>
                <td>Configuration Management Database</td>
            </tr>
            <tr>
                <td>CI/CD</td>
                <td>Continuous Integration / Continuous Deployment</td>
            </tr>
            <tr>
                <td>CPU</td>
                <td>Central Processing Unit</td>
            </tr>
            <tr>
                <td>HTML</td>
                <td>HyperText Markup Language</td>
            </tr>
            <tr>
                <td>HTTP</td>
                <td>HyperText Transfer Protocol</td>
            </tr>
            <tr>
                <td>IA</td>
                <td>Intelligence Artificielle</td>
            </tr>
            <tr>
                <td>JSON</td>
                <td>JavaScript Object Notation</td>
            </tr>
            <tr>
                <td>ML</td>
                <td>Machine Learning</td>
            </tr>
            <tr>
                <td>REST</td>
                <td>Representational State Transfer</td>
            </tr>
            <tr>
                <td>SHRS</td>
                <td>Sopra HR Software</td>
            </tr>
            <tr>
                <td>UML</td>
                <td>Unified Modeling Language</td>
            </tr>
        </table>
    </div>

    <!-- Introduction -->
    <div class="page latex-section">
        <h2>Introduction générale</h2>

        <div class="introduction-section">
            <h3>Contexte général</h3>
            <p>
                À l'ère de la transformation numérique, la gestion optimale des infrastructures informatiques constitue
                un enjeu stratégique majeur pour les entreprises. Dans un environnement où la disponibilité des services
                et la performance des systèmes conditionnent directement la satisfaction client et la compétitivité, les
                organisations doivent relever des défis complexes liés au monitoring, à l'optimisation des ressources et
                à la prévention proactive des incidents.
            </p>

            <p>
                Les infrastructures on-premise, bien qu'offrant un contrôle total sur les données et les processus,
                nécessitent une expertise technique approfondie et des outils de supervision sophistiqués pour garantir
                leur efficacité opérationnelle. La complexité croissante des architectures distribuées, combinée à
                l'augmentation des volumes de données à traiter, rend indispensable l'adoption de solutions automatisées
                de monitoring et d'analyse prédictive.
            </p>
        </div>

        <div class="introduction-section">
            <h3>Problématique</h3>
            <p>
                Sopra HR Software, acteur de référence dans le domaine des solutions de gestion des ressources humaines,
                opère une infrastructure on-premise critique composée de plus de 400 serveurs supportant les applications
                métier de centaines de clients à travers le monde. Cette infrastructure, garante de la continuité de
                service et de la sécurité des données RH sensibles, nécessite une supervision constante et une gestion
                proactive des capacités.
            </p>

            <p>
                Actuellement, le processus de monitoring et d'analyse de la capacité des serveurs repose sur des
                approches largement manuelles, impliquant la consultation de multiples outils disparates, la génération
                manuelle de rapports et l'analyse ponctuelle des métriques. Cette situation engendre plusieurs
                problématiques majeures : temps de traitement élevé, risque d'erreurs humaines, difficultés de
                corrélation des données, et surtout, capacité limitée d'anticipation des surcharges et des besoins
                d'évolution de l'infrastructure.
            </p>
        </div>

        <div class="introduction-section">
            <h3>Objectifs du projet</h3>
            <p>
                Le projet CloudCapacity s'inscrit dans une démarche d'innovation et d'amélioration continue visant à
                moderniser et automatiser la gestion de la capacité des serveurs chez Sopra HR Software. L'objectif
                principal consiste à concevoir et développer une plateforme web intégrée permettant de :
            </p>

            <ul class="objectives-list">
                <li>Centraliser la collecte et la visualisation des métriques de capacité serveurs</li>
                <li>Automatiser la génération de rapports de capacité personnalisés</li>
                <li>Implémenter un système d'alertes intelligentes basé sur des seuils configurables</li>
                <li>Fournir des outils de comparaison et d'analyse temporelle des usages</li>
                <li>Intégrer des fonctionnalités d'analyse prédictive pour anticiper les surcharges</li>
                <li>Améliorer la prise de décision concernant l'évolution de l'infrastructure</li>
            </ul>
        </div>

        <div class="introduction-section">
            <h3>Approche méthodologique</h3>
            <p>
                La réalisation de ce projet s'appuie sur une méthodologie rigoureuse combinant analyse des besoins
                métier, conception architecturale moderne et développement itératif. L'approche adoptée privilégie
                l'intégration native avec l'écosystème technique existant, notamment l'infrastructure de monitoring
                Zabbix, tout en introduisant des technologies web modernes pour garantir une expérience utilisateur
                optimale et une maintenabilité à long terme.
            </p>
        </div>

        <div class="introduction-section">
            <h3>Structure du rapport</h3>
            <p>
                Ce rapport de mi-parcours présente l'avancement du projet CloudCapacity selon une structure académique
                classique. Le premier chapitre expose l'étude de l'existant et l'analyse du contexte organisationnel et
                technique. Le deuxième chapitre détaille l'analyse des besoins et les spécifications fonctionnelles et
                non fonctionnelles. Le troisième chapitre présente la conception architecturale et les choix
                technologiques. Le quatrième chapitre décrit la réalisation et l'implémentation des fonctionnalités. Le
                cinquième chapitre expose les tests réalisés et la validation des développements. Enfin, le sixième
                chapitre dresse un bilan de l'avancement et présente les perspectives d'évolution du projet.
            </p>
        </div>
    </div>

    <!-- Chapitre 1 -->
    <div class="page latex-section">
        <h2>Chapitre 1 : Étude de l'existant</h2>

        <h3>1.1 Présentation de Sopra HR Software</h3>
        <p>Sopra HR Software est une filiale de Sopra Steria Group spécialisée dans les solutions de gestion des
            ressources humaines. L'entreprise accompagne plus de 900 clients dans 54 pays et propose des solutions
            complètes pour la digitalisation RH, allant de la gestion administrative à l'expérience collaborateur, en
            passant par la gestion des talents.</p>
        <p>Disposant d'une infrastructure informatique conséquente pour héberger ses solutions, Sopra HR Software
            maintient un parc de serveurs on-premise répartis sur plusieurs sites. La performance et la disponibilité de
            ces serveurs sont cruciales pour garantir la qualité de service offerte aux clients.</p>

        <h3>1.2 Contexte et problématique</h3>
        <p>La gestion actuelle de la capacité des serveurs chez Sopra HR Software présente plusieurs limitations :</p>
        <ul>
            <li>Collecte manuelle des métriques de performance, impliquant un temps considérable pour les équipes
                techniques</li>
            <li>Absence de centralisation des données de performance, rendant difficile l'analyse globale de
                l'infrastructure</li>
            <li>Génération manuelle des rapports de capacité, sujette aux erreurs et chronophage</li>
            <li>Manque d'outils prédictifs permettant d'anticiper les besoins en ressources</li>
            <li>Difficultés à corréler les incidents avec les métriques de performance pour une analyse approfondie</li>
        </ul>
        <p>Ces limitations entraînent des risques opérationnels, tels que des lenteurs potentielles dans les
            applications, des interruptions de service non anticipées, et une allocation sous-optimale des ressources,
            impactant directement les coûts d'infrastructure.</p>

        <h3>1.3 Solutions existantes de gestion de capacité</h3>
        <p>Actuellement, Sopra HR Software utilise plusieurs outils pour surveiller et gérer la capacité de ses serveurs
            :</p>
        <ul>
            <li><strong>Zabbix</strong> : Utilisé pour la collecte de métriques basiques, mais sans fonctionnalités
                avancées d'analyse et de prédiction</li>
            <li><strong>Maia CMDB</strong> : Solution de gestion de la configuration qui stocke les informations sur les
                serveurs et leur architecture</li>
            <li><strong>Jira</strong> : Utilisé pour la gestion des incidents, mais sans intégration automatisée avec
                les métriques de performance</li>
            <li><strong>Tableaux Excel</strong> : Utilisés pour la génération manuelle de rapports de capacité,
                nécessitant un traitement manuel des données</li>
        </ul>

        <h3>1.4 Limites des solutions actuelles</h3>
        <p>Bien que fonctionnelles, les solutions existantes présentent plusieurs limites :</p>
        <ul>
            <li>Absence d'automatisation du reporting, nécessitant des interventions manuelles régulières</li>
            <li>Manque d'intégration entre les différents outils, créant des silos d'information</li>
            <li>Absence de capacités prédictives pour anticiper les besoins futurs en ressources</li>
            <li>Interface utilisateur peu intuitive pour la visualisation des métriques</li>
            <li>Difficulté à personnaliser les rapports selon les besoins spécifiques des parties prenantes</li>
            <li>Absence de mécanismes d'alerte sophistiqués basés sur l'analyse des tendances</li>
        </ul>
        <p>Face à ces limitations, le projet CloudCapacity propose une solution intégrée qui combine les fonctionnalités
            de monitoring, d'analyse, de prédiction et de reporting dans une plateforme unique, s'appuyant sur les
            technologies modernes et l'intelligence artificielle.</p>
    </div>

    <!-- Chapitre 2 -->
    <div class="page latex-section">
        <h2>Chapitre 2 : Cahier des charges fonctionnel</h2>

        <h3>2.1 Objectifs du projet</h3>
        <p>Le projet CloudCapacity vise à répondre aux besoins de Sopra HR Software en matière de gestion de la capacité
            des serveurs on-premise. Les principaux objectifs sont :</p>
        <ul>
            <li><strong>Automatiser le reporting de capacité</strong> : Développer une plateforme web permettant de
                suivre de manière mensuelle les métriques des serveurs (CPU, mémoire, swap) et de générer des rapports
                de capacité personnalisés.</li>
            <li><strong>Optimiser l'utilisation des ressources</strong> : Mettre en place des algorithmes de machine
                learning pour prédire les surcharges de capacité et proposer des ajustements proactifs.</li>
            <li><strong>Centraliser la supervision</strong> : Offrir une vue unifiée de l'ensemble des métriques de
                performance à travers des tableaux de bord dynamiques et personnalisables.</li>
            <li><strong>Améliorer la réactivité</strong> : Mettre en place un système d'alertes et de notifications
                permettant d'informer rapidement les équipes techniques en cas d'anomalie.</li>
            <li><strong>Faciliter la prise de décision</strong> : Fournir des analyses prédictives et des
                recommandations pour optimiser l'allocation des ressources.</li>
        </ul>

        <h3>2.2 Périmètre du projet</h3>
        <p>Le périmètre du projet CloudCapacity inclut :</p>
        <ul>
            <li><strong>Systèmes et services</strong> : Tous les serveurs physiques et virtuels on-premise de Sopra,
                avec une intégration à l'API Zabbix pour collecter les métriques.</li>
            <li><strong>Outils et technologies</strong> : Utilisation de FastAPI et Angular pour l'application web,
                Docker et Kubernetes pour la conteneurisation et l'orchestration des services, avec un déploiement sur
                une plateforme adaptée aux environnements cloud hybrides OpenShift.</li>
            <li><strong>Intégrations</strong> : API Zabbix pour les métriques, Maia CMDB pour les données de
                configuration, Jira pour la gestion des incidents.</li>
            <li><strong>Machine learning</strong> : Implémentation d'algorithmes pour l'analyse prédictive et
                l'optimisation des ressources.</li>
        </ul>

        <p>Le projet ne couvre pas :</p>
        <ul>
            <li>La gestion des serveurs dans des environnements cloud externes (AWS, Azure, etc.)</li>
            <li>Le déploiement ou la configuration des services applicatifs sur les serveurs</li>
            <li>La gestion des coûts liés aux services cloud publics</li>
        </ul>

        <h3>2.3 Besoins métiers</h3>
        <p>Les besoins métiers identifiés pour le projet CloudCapacity sont :</p>
        <ul>
            <li><strong>Visibilité sur l'utilisation des ressources</strong> : Améliorer la compréhension de
                l'utilisation actuelle et future des ressources serveurs pour faciliter la planification des capacités.
            </li>
            <li><strong>Performance des systèmes</strong> : Garantir que les applications disposent des ressources
                nécessaires pour fonctionner de manière optimale, évitant ainsi les baisses de performance qui peuvent
                impacter les utilisateurs finaux.</li>
            <li><strong>Scalabilité</strong> : Assurer que la capacité des serveurs peut être ajustée de manière
                dynamique en fonction des besoins réels et prévus.</li>
            <li><strong>Optimisation des coûts</strong> : Réduire les dépenses liées à l'infrastructure en évitant le
                sur-provisionnement des ressources.</li>
            <li><strong>Sécurité</strong> : Prévenir les failles de sécurité potentielles liées à une mauvaise gestion
                de la capacité (serveurs surchargés, vulnérables aux attaques).</li>
            <li><strong>Productivité des équipes techniques</strong> : Libérer du temps pour les équipes en automatisant
                les tâches répétitives liées à la collecte de données et à la génération de rapports.</li>
        </ul>

        <h3>2.4 Exigences fonctionnelles</h3>

        <h4>2.4.1 Collecte et analyse des données</h4>
        <ul>
            <li><strong>Collecte des données de performance</strong> : Le système doit collecter des métriques telles
                que l'utilisation CPU, la mémoire, la bande passante, l'espace disque, et la latence des applications.
                Ces données doivent être disponibles et sur de longues périodes.</li>
            <li><strong>Analyse des tendances</strong> : La solution doit permettre d'analyser les tendances
                d'utilisation des ressources sur différentes périodes (journalière, hebdomadaire, mensuelle,
                trimestrielle).</li>
            <li><strong>Historisation et archivage des données</strong> : Le système doit permettre d'archiver les
                données de performance à long terme pour faciliter les analyses comparatives et l'identification des
                tendances saisonnières.</li>
        </ul>

        <h4>2.4.2 Visualisation et reporting</h4>
        <ul>
            <li><strong>Visualisation dynamique</strong> : L'outil doit permettre la visualisation des données sous
                forme de graphiques interactifs, de tableaux de bord dynamiques et de rapports détaillés pour faciliter
                l'analyse des tendances et la prise de décision.</li>
            <li><strong>Génération automatique de rapports</strong> : Le système doit permettre de générer
                automatiquement des rapports périodiques (quotidiens, hebdomadaires, mensuels) sur l'utilisation des
                ressources et les prévisions de capacité.</li>
            <li><strong>Envoi par email</strong> : Les rapports doivent être envoyés par email aux parties prenantes
                sous forme de fichiers PDF ou Excel, avec possibilité de personnaliser les destinataires et la fréquence
                d'envoi.</li>
            <li><strong>Personnalisation des rapports</strong> : Les rapports doivent pouvoir être personnalisés en
                fonction des besoins spécifiques des utilisateurs, avec possibilité de filtrer les données par serveur,
                par groupe de serveurs, par période, etc.</li>
        </ul>

        <h4>2.4.3 Alertes et notifications</h4>
        <ul>
            <li><strong>Seuils d'alerte configurables</strong> : L'administrateur doit pouvoir définir des seuils
                d'alerte personnalisés pour chaque métrique surveillée.</li>
            <li><strong>Alertes en cas de dépassement</strong> : Le système doit envoyer des alertes en cas de
                dépassement des seuils définis, avec différents niveaux de criticité (information, avertissement,
                critique).</li>
            <li><strong>Notification multicanal</strong> : Les alertes doivent pouvoir être envoyées via différents
                canaux (email, SMS, intégration avec des plateformes de communication comme Slack ou Microsoft Teams).
            </li>
            <li><strong>Reporting des alertes</strong> : Le système doit conserver un historique des alertes déclenchées
                et permettre la génération de rapports sur ces événements.</li>
        </ul>

        <h4>2.4.4 Intégration avec les outils existants</h4>
        <ul>
            <li><strong>API Zabbix</strong> : Intégration avec l'API Zabbix pour récupérer les métriques de performance
                des serveurs.</li>
            <li><strong>Maia CMDB</strong> : Intégration avec la base de données Maia CMDB pour récupérer les
                informations sur les serveurs et leur configuration.</li>
            <li><strong>Jira</strong> : L'intégration avec Jira permettra d'automatiser la création, le suivi et la mise
                à jour des tickets liés à la gestion de la capacité des serveurs, en corrélant les incidents aux
                métriques de performance.</li>
        </ul>

        <h4>2.4.5 Machine learning et prédiction</h4>
        <ul>
            <li><strong>Analyse prédictive</strong> : Utilisation d'algorithmes de machine learning pour analyser les
                tendances historiques et prédire l'utilisation future des ressources.</li>
            <li><strong>Recommandations d'optimisation</strong> : Le système doit proposer des recommandations pour
                optimiser l'allocation des ressources en fonction des prévisions d'utilisation.</li>
            <li><strong>Détection d'anomalies</strong> : Mise en place d'algorithmes pour détecter automatiquement les
                anomalies dans l'utilisation des ressources, permettant une intervention préventive.</li>
            <li><strong>Apprentissage continu</strong> : Le modèle de prédiction doit s'améliorer continuellement en
                intégrant les nouvelles données collectées.</li>
        </ul>
    </div>

    <!-- Chapitre 3 -->
    <div class="page latex-section">
        <h2>Chapitre 3 : Cahier des charges technique</h2>

        <h3>3.1 Architecture générale</h3>
        <p>L'architecture de CloudCapacity est conçue pour être modulaire, distribuée et scalable, permettant une
            évolution progressive du système et une adaptation aux besoins futurs. Elle s'articule autour des composants
            suivants :</p>

        <div class="diagram-container">
            <div id="architecture-generale" class="mermaid">
                <!-- Le diagramme sera chargé depuis diagrams/architecture-generale.mmd -->
            </div>
            <div class="figure-caption">Figure 3.1 : Architecture générale de CloudCapacity</div>
        </div>

        <h3>3.3 Modélisation UML</h3>

        <h4>3.3.1 Diagramme de cas d'utilisation</h4>
        <div class="diagram-container">
            <div id="cas-utilisation" class="mermaid">
                <!-- Le diagramme sera chargé depuis diagrams/cas-utilisation.mmd -->
            </div>
            <div class="figure-caption">Figure 3.2 : Diagramme de cas d'utilisation</div>
        </div>

        <h4>3.3.2 Diagramme de séquence - Collecte de métriques</h4>
        <div class="diagram-container">
            <div id="sequence-collecte" class="mermaid">
                <!-- Le diagramme sera chargé depuis diagrams/sequence-collecte-metriques.mmd -->
            </div>
            <div class="figure-caption">Figure 3.3 : Diagramme de séquence - Collecte de métriques</div>
        </div>

        <h4>3.3.3 Modèle de données</h4>
        <div class="diagram-container">
            <div id="modele-donnees" class="mermaid">
                <!-- Le diagramme sera chargé depuis diagrams/modele-donnees.mmd -->
            </div>
            <div class="figure-caption">Figure 3.4 : Modèle de données relationnel</div>
        </div>

        <p>Cette architecture repose sur les principes suivants :</p>
        <ul>
            <li><strong>Séparation des responsabilités</strong> : Les différentes couches (présentation, logique métier,
                accès aux données) sont clairement séparées.</li>
            <li><strong>API-centric</strong> : Toutes les interactions entre les composants se font via des API RESTful
                bien définies.</li>
            <li><strong>Conteneurisation</strong> : Les composants sont déployés sous forme de conteneurs Docker
                orchestrés par Kubernetes.</li>
            <li><strong>Evolutivité</strong> : L'architecture permet d'ajouter facilement de nouvelles fonctionnalités
                ou d'évoluer vers de nouvelles technologies.</li>
        </ul>

        <h3>3.2 Technologies utilisées</h3>
        <p>Le choix des technologies a été effectué en tenant compte des exigences fonctionnelles et non fonctionnelles
            du projet, ainsi que des compétences disponibles au sein de l'équipe.</p>

        <h4>3.2.1 Frontend - Angular 19</h4>
        <p>Le frontend de CloudCapacity est développé avec Angular 19 en utilisant des composants autonomes
            (standalone),
            offrant une architecture moderne et modulaire :</p>
        <ul>
            <li><strong>Thème Modern Dark Dashboard</strong> : Interface sombre moderne avec palette de couleurs
                cohérente</li>
            <li><strong>Composants autonomes</strong> : Architecture modulaire sans modules NgModule traditionnels</li>
            <li><strong>Chart.js intégré</strong> : Visualisation de données avec ng2-charts et composant BrowserChart
                personnalisé</li>
            <li><strong>Angular Material</strong> : Composants UI avec personnalisation du thème</li>
            <li><strong>TypeScript strict</strong> : Typage fort pour une meilleure maintenabilité</li>
            <li><strong>SCSS modulaire</strong> : Styles organisés avec variables et mixins centralisés</li>
        </ul>

        <h4>3.2.2 Composants Angular implémentés</h4>
        <table class="table-data">
            <tr>
                <th>Composant</th>
                <th>Fonctionnalité</th>
                <th>État</th>
            </tr>
            <tr>
                <td>DashboardComponent</td>
                <td>Vue d'ensemble avec métriques et alertes</td>
                <td class="status-implemented">✓ Implémenté</td>
            </tr>
            <tr>
                <td>ServerListComponent</td>
                <td>Liste des serveurs avec filtrage avancé</td>
                <td class="status-implemented">✓ Implémenté</td>
            </tr>
            <tr>
                <td>ServerDetailComponent</td>
                <td>Détails serveur avec graphiques métriques</td>
                <td class="status-implemented">✓ Implémenté</td>
            </tr>
            <tr>
                <td>UsageComparisonComponent</td>
                <td>Comparaison serveurs/périodes</td>
                <td class="status-implemented">✓ Implémenté</td>
            </tr>
            <tr>
                <td>ThresholdAlertsComponent</td>
                <td>Gestion des alertes de seuil</td>
                <td class="status-implemented">✓ Implémenté</td>
            </tr>
            <tr>
                <td>ModernTableComponent</td>
                <td>Tableaux réutilisables avec tri/filtrage</td>
                <td class="status-implemented">✓ Implémenté</td>
            </tr>
            <tr>
                <td>BrowserChartComponent</td>
                <td>Graphiques Chart.js optimisés</td>
                <td class="status-implemented">✓ Implémenté</td>
            </tr>
        </table>

        <h4>3.2.3 Backend - FastAPI</h4>
        <p>Le backend CloudCapacity utilise FastAPI avec une architecture modulaire organisée en routers spécialisés :
        </p>

        <table class="table-data">
            <tr>
                <th>Router</th>
                <th>Responsabilité</th>
                <th>Endpoints principaux</th>
            </tr>
            <tr>
                <td>server_info</td>
                <td>Gestion des informations serveurs</td>
                <td>/get-servers, /return-and-sync-hostnames-and-metrics</td>
            </tr>
            <tr>
                <td>server_usage</td>
                <td>Métriques d'utilisation</td>
                <td>/server-usage-per-month, /all-servers-usage-per-selected-month</td>
            </tr>
            <tr>
                <td>server_comparison</td>
                <td>Comparaison d'usage</td>
                <td>/compare-server-usage</td>
            </tr>
            <tr>
                <td>threshold</td>
                <td>Gestion des seuils</td>
                <td>/servers-usage-threshold</td>
            </tr>
            <tr>
                <td>reporting_capacity</td>
                <td>Génération de rapports</td>
                <td>/generate-capacity-report</td>
            </tr>
        </table>

        <h4>3.2.4 Intégration Zabbix</h4>
        <p>L'intégration avec Zabbix constitue le cœur du système de collecte de données :</p>
        <ul>
            <li><strong>PyZabbix</strong> : Bibliothèque Python pour l'interaction avec l'API Zabbix</li>
            <li><strong>Authentification sécurisée</strong> : Gestion des sessions et des identifiants</li>
            <li><strong>Collecte de métriques</strong> : CPU, mémoire, swap avec données historiques</li>
            <li><strong>Traitement parallèle</strong> : ThreadPoolExecutor pour optimiser les performances</li>
            <li><strong>Gestion d'erreurs</strong> : Robustesse face aux indisponibilités temporaires</li>
        </ul>

        <h4>3.2.5 Modèles de données SQLModel</h4>
        <table class="table-data">
            <tr>
                <th>Modèle</th>
                <th>Description</th>
                <th>Champs principaux</th>
            </tr>
            <tr>
                <td>Server</td>
                <td>Informations serveur statiques</td>
                <td>hostname, server_type, total_cpus, total_ram, total_swap</td>
            </tr>
            <tr>
                <td>UsageDataPoint</td>
                <td>Point de données d'utilisation</td>
                <td>timestamp, cpu_usage, memory_usage, swap_usage</td>
            </tr>
            <tr>
                <td>ZabbixData</td>
                <td>Données Zabbix structurées</td>
                <td>cpu_usage[], memory_usage[], swap_utilization[]</td>
            </tr>
            <tr>
                <td>ComparisonDataPoint</td>
                <td>Données de comparaison</td>
                <td>hostname, comparison_type, difference, difference_percent</td>
            </tr>
        </table>

        <h4>3.2.3 Conteneurisation - Docker et Kubernetes</h4>
        <p>La solution utilise Docker pour la conteneurisation des différents composants, offrant :</p>
        <ul>
            <li>Isolation des environnements d'exécution</li>
            <li>Portabilité entre les différents environnements (développement, test, production)</li>
            <li>Gestion simplifiée des dépendances</li>
        </ul>
        <p>Kubernetes est utilisé pour l'orchestration des conteneurs, avec les avantages suivants :</p>
        <ul>
            <li>Scaling automatique en fonction de la charge</li>
            <li>Auto-réparation en cas de défaillance d'un conteneur</li>
            <li>Déploiement sans interruption de service (zero downtime)</li>
            <li>Gestion avancée du réseau et du stockage</li>
        </ul>
        <p>Le déploiement est effectué sur OpenShift, la plateforme d'orchestration de conteneurs de Red Hat basée sur
            Kubernetes, qui offre des fonctionnalités supplémentaires de sécurité et de gestion.</p>

        <h4>3.2.4 Base de données et stockage</h4>
        <p>La solution utilise une base de données Oracle pour stocker les informations sur les serveurs et leurs
            métriques. Cette base de données offre les caractéristiques suivantes :</p>
        <ul>
            <li>Support pour les requêtes complexes et les fonctions d'agrégation</li>
            <li>Haute disponibilité et réplication</li>
            <li>Support pour les transactions ACID</li>
            <li>Intégration avec l'écosystème Oracle existant de l'entreprise</li>
        </ul>
        <p>Les principaux modèles de données incluent :</p>
        <ul>
            <li><strong>Server</strong> : Représente un serveur avec ses métriques statiques (hostname, type de serveur,
                nombre total de CPUs, RAM totale, swap total)</li>
            <li><strong>UsageDataPoint</strong> : Représente un point de données pour les métriques d'utilisation</li>
            <li><strong>MonthlyData</strong> : Contient les données d'utilisation pour un mois (CPU, mémoire, swap)</li>
            <li><strong>ThresholdType</strong> : Énumération pour les différents types de seuils (CPU, mémoire, swap)
            </li>
        </ul>
        <p>Les données historiques de métriques sont récupérées directement depuis Zabbix via son API, puis transformées
            et stockées temporairement pour l'analyse et la visualisation. Cette approche permet d'éviter la duplication
            des données tout en offrant des performances optimales pour les requêtes fréquentes.</p>

        <h4>3.2.5 Intégrations API</h4>
        <p>CloudCapacity s'intègre principalement avec l'API Zabbix pour la collecte des métriques de performance :</p>
        <ul>
            <li><strong>API Zabbix</strong> : Utilisée pour récupérer les métriques de performance des serveurs (CPU,
                mémoire, swap) en temps réel et historiques</li>
            <li><strong>Authentification Zabbix</strong> : Gestion des identifiants et des sessions pour l'accès
                sécurisé à l'API</li>
            <li><strong>Transformation des données</strong> : Conversion des données brutes de Zabbix en formats
                exploitables pour l'analyse et la visualisation</li>
        </ul>
        <p>L'intégration avec Zabbix est réalisée via la bibliothèque PyZabbix, qui offre une interface Python pour
            interagir avec l'API Zabbix. Cette intégration permet de récupérer les données suivantes :</p>
        <ul>
            <li>Liste des serveurs et leurs caractéristiques (hostname, type, OS, etc.)</li>
            <li>Métriques statiques (nombre de CPUs, RAM totale, swap total)</li>
            <li>Métriques d'utilisation (CPU, mémoire, swap) sur différentes périodes</li>
            <li>Tendances historiques pour l'analyse prédictive</li>
        </ul>
        <p>L'architecture est conçue pour permettre l'ajout futur d'autres intégrations API, comme Jira pour la gestion
            des incidents ou des systèmes de notification pour les alertes.</p>

        <h3>3.3 Sécurité et gestion des accès</h3>
        <p>La sécurité de CloudCapacity repose sur plusieurs mécanismes :</p>
        <ul>
            <li><strong>Communication sécurisée</strong> : Utilisation de HTTPS pour toutes les communications entre le
                frontend et le backend</li>
            <li><strong>CORS (Cross-Origin Resource Sharing)</strong> : Configuration appropriée pour permettre les
                requêtes cross-origin tout en maintenant la sécurité</li>
            <li><strong>Gestion des identifiants</strong> : Stockage sécurisé des identifiants pour l'accès aux systèmes
                externes comme Zabbix</li>
            <li><strong>Validation des données</strong> : Validation stricte des entrées utilisateur pour prévenir les
                injections et autres attaques</li>
            <li><strong>Journalisation</strong> : Enregistrement des actions système pour le débogage et l'audit</li>
        </ul>
        <p>Le backend utilise FastAPI avec des middlewares de sécurité pour gérer les aspects suivants :</p>
        <ul>
            <li>Validation des requêtes entrantes via Pydantic</li>
            <li>Gestion des erreurs et des exceptions de manière sécurisée</li>
            <li>Configuration CORS pour contrôler l'accès aux ressources API</li>
        </ul>
        <p>Le frontend implémente des pratiques de sécurité Angular, notamment :</p>
        <ul>
            <li>Protection contre les attaques XSS (Cross-Site Scripting)</li>
            <li>Sanitization des données affichées dans l'interface utilisateur</li>
            <li>Gestion sécurisée des formulaires et des entrées utilisateur</li>
        </ul>

        <h3>3.4 Performances et scalabilité</h3>
        <p>Pour garantir les performances et la scalabilité de la solution, plusieurs mécanismes sont mis en place :</p>
        <ul>
            <li><strong>API asynchrone</strong> : Utilisation des fonctionnalités asynchrones de FastAPI pour gérer
                efficacement les requêtes concurrentes</li>
            <li><strong>Pool de connexions</strong> : Configuration d'un pool de connexions à la base de données Oracle
                pour optimiser les performances</li>
            <li><strong>Optimisation des requêtes</strong> : Indexation appropriée des bases de données et optimisation
                des requêtes Zabbix</li>
            <li><strong>Traitement parallèle</strong> : Utilisation de ThreadPoolExecutor pour le traitement parallèle
                des requêtes de données</li>
            <li><strong>Lazy loading</strong> : Chargement à la demande des données dans l'interface utilisateur Angular
            </li>
        </ul>
        <p>Le backend est conçu pour gérer efficacement les charges importantes :</p>
        <ul>
            <li>Configuration du pool de connexions à la base de données avec une taille optimale</li>
            <li>Utilisation de requêtes asynchrones pour éviter le blocage du serveur</li>
            <li>Mise en cache des résultats de requêtes fréquentes</li>
            <li>Optimisation des requêtes à l'API Zabbix pour minimiser le temps de réponse</li>
        </ul>
        <p>Le frontend Angular est optimisé pour les performances :</p>
        <ul>
            <li>Utilisation de composants autonomes (standalone) pour réduire la taille du bundle</li>
            <li>Implémentation du change detection strategy OnPush pour minimiser les rendus inutiles</li>
            <li>Utilisation de BrowserChartComponent pour le rendu efficace des graphiques</li>
            <li>Mise en cache des données côté client pour réduire les appels API</li>
        </ul>

        <h3>3.5 Déploiement et CI/CD</h3>
        <p>Le processus de déploiement de CloudCapacity s'appuie sur une approche DevOps avec :</p>
        <ul>
            <li><strong>Docker</strong> : Conteneurisation des composants frontend et backend pour assurer la
                portabilité et la cohérence entre les environnements</li>
            <li><strong>Environnements multiples</strong> : Développement, test, préproduction et production</li>
            <li><strong>Tests automatisés</strong> : Tests unitaires avec Jasmine et Karma pour le frontend, tests API
                avec Tavern pour le backend</li>
            <li><strong>Scripts de déploiement</strong> : Automatisation du processus de déploiement avec des scripts
                dédiés</li>
            <li><strong>Surveillance</strong> : Mise en place de mécanismes de surveillance pour détecter les problèmes
                en production</li>
        </ul>
        <p>Le déploiement du frontend Angular utilise les fonctionnalités suivantes :</p>
        <ul>
            <li>Build optimisé avec Angular CLI pour la production</li>
            <li>Server-Side Rendering (SSR) pour améliorer les performances et le SEO</li>
            <li>Serveur Express pour servir l'application Angular</li>
            <li>Scripts d'analyse et de capture d'écran pour vérifier la qualité de l'interface utilisateur</li>
        </ul>
        <p>Le déploiement du backend FastAPI utilise :</p>
        <ul>
            <li>Serveur ASGI Uvicorn pour des performances optimales</li>
            <li>Configuration SSL pour les communications sécurisées</li>
            <li>Gestion des dépendances via requirements.txt</li>
            <li>Scripts d'initialisation pour la configuration de la base de données</li>
        </ul>
    </div>

    <!-- Chapitre 4 -->
    <div class="page latex-section">
        <h2>Chapitre 4 : Réalisation et implémentation</h2>

        <h3>4.1 Environnement de développement</h3>
        <p>Le développement de CloudCapacity s'appuie sur un environnement technique moderne et robuste :</p>

        <table class="table-data">
            <tr>
                <th>Composant</th>
                <th>Version</th>
                <th>Utilisation</th>
            </tr>
            <tr>
                <td>Node.js</td>
                <td>v20.16.0+</td>
                <td>Runtime JavaScript pour Angular</td>
            </tr>
            <tr>
                <td>Angular CLI</td>
                <td>v19.2.8+</td>
                <td>Outils de développement Angular</td>
            </tr>
            <tr>
                <td>Python</td>
                <td>3.12+</td>
                <td>Runtime pour FastAPI backend</td>
            </tr>
            <tr>
                <td>FastAPI</td>
                <td>Latest</td>
                <td>Framework API REST</td>
            </tr>
            <tr>
                <td>Oracle Database</td>
                <td>19c</td>
                <td>Base de données principale</td>
            </tr>
            <tr>
                <td>Zabbix</td>
                <td>6.x</td>
                <td>Système de monitoring source</td>
            </tr>
        </table>

        <h3>4.2 État d'avancement des fonctionnalités</h3>
        <p>À mi-parcours du projet, l'état d'avancement des fonctionnalités est le suivant :</p>

        <table class="table-data">
            <tr>
                <th>Fonctionnalité</th>
                <th>État</th>
                <th>Pourcentage</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>Dashboard principal</td>
                <td class="status-implemented">✓ Implémenté</td>
                <td>100%</td>
                <td>Vue d'ensemble avec métriques et alertes</td>
            </tr>
            <tr>
                <td>Liste des serveurs</td>
                <td class="status-implemented">✓ Implémenté</td>
                <td>100%</td>
                <td>Filtrage, tri, pagination</td>
            </tr>
            <tr>
                <td>Détails serveur</td>
                <td class="status-implemented">✓ Implémenté</td>
                <td>100%</td>
                <td>Métriques détaillées avec graphiques</td>
            </tr>
            <tr>
                <td>Comparaison d'usage</td>
                <td class="status-implemented">✓ Implémenté</td>
                <td>100%</td>
                <td>Comparaison serveurs/périodes</td>
            </tr>
            <tr>
                <td>Système d'alertes</td>
                <td class="status-implemented">✓ Implémenté</td>
                <td>100%</td>
                <td>Alertes de seuil configurables</td>
            </tr>
            <tr>
                <td>API Zabbix</td>
                <td class="status-implemented">✓ Implémenté</td>
                <td>100%</td>
                <td>Intégration complète</td>
            </tr>
            <tr>
                <td>Reporting PDF</td>
                <td class="status-in-progress">⚠ En cours</td>
                <td>80%</td>
                <td>Génération de rapports</td>
            </tr>
            <tr>
                <td>Machine Learning</td>
                <td class="status-planned">○ Planifié</td>
                <td>0%</td>
                <td>Prédiction de surcharges</td>
            </tr>
        </table>

        <h3>4.3 Fonctionnalités implémentées</h3>

        <h4>4.3.1 Dashboard principal</h4>
        <p>Le dashboard constitue le point d'entrée principal de l'application CloudCapacity :</p>
        <ul class="feature-list">
            <li>Vue d'ensemble des métriques système avec cartes de résumé</li>
            <li>Affichage des alertes récentes avec statuts colorés</li>
            <li>Top des serveurs par utilisation CPU</li>
            <li>Navigation intuitive vers les autres sections</li>
            <li>Actualisation automatique des données</li>
        </ul>

        <div class="alert alert-info">
            <strong>Composant :</strong> DashboardComponent - Implémente SummaryCardsComponent et ModernTableComponent
            pour l'affichage des données
        </div>

        <h4>4.3.2 Gestion des serveurs</h4>
        <p>Le module de gestion des serveurs offre une interface complète pour la supervision :</p>
        <ul class="feature-list">
            <li>Liste paginée avec filtrage multi-critères (type, statut, client, environnement)</li>
            <li>Recherche textuelle en temps réel</li>
            <li>Tri par colonnes (hostname, CPUs, mémoire, statut)</li>
            <li>Détails serveur avec métriques historiques</li>
            <li>Visualisation graphique des métriques CPU, mémoire, swap</li>
            <li>Sélection de période (mois/année) pour l'analyse historique</li>
        </ul>

        <div class="alert alert-info">
            <strong>Composants :</strong> ServerListComponent, ServerDetailComponent avec BrowserChartComponent pour les
            graphiques
        </div>

        <h4>4.3.3 Comparaison d'usage</h4>
        <p>Le système de comparaison d'usage constitue une fonctionnalité avancée permettant l'analyse comparative :</p>
        <ul class="feature-list">
            <li>Comparaison entre deux serveurs pour le même mois</li>
            <li>Comparaison temporelle d'un serveur entre deux mois</li>
            <li>Graphiques côte à côte et superposés</li>
            <li>Tableaux de comparaison avec calculs de différences</li>
            <li>Statistiques détaillées (moyennes, pics, tendances)</li>
            <li>Export des données de comparaison</li>
        </ul>

        <div class="alert alert-info">
            <strong>Composant :</strong> UsageComparisonComponent avec gestion avancée des graphiques Chart.js
        </div>

        <h4>4.3.4 Système d'alertes</h4>
        <p>Le module d'alertes permet la surveillance proactive des seuils de performance :</p>
        <ul class="feature-list">
            <li>Configuration de seuils par type de métrique (CPU, mémoire, swap)</li>
            <li>Génération automatique d'alertes en cas de dépassement</li>
            <li>Affichage des alertes avec niveaux de criticité</li>
            <li>Historique des alertes avec horodatage</li>
            <li>Intégration avec le dashboard pour visibilité immédiate</li>
        </ul>

        <div class="alert alert-info">
            <strong>Composant :</strong> ThresholdAlertsComponent avec backend endpoint /servers-usage-threshold
        </div>

        <h3>4.4 Architecture technique implémentée</h3>

        <h4>4.4.1 Backend FastAPI</h4>
        <p>L'architecture backend est organisée en modules spécialisés :</p>
        <table class="table-data">
            <tr>
                <th>Module</th>
                <th>Fichier</th>
                <th>Responsabilité</th>
            </tr>
            <tr>
                <td>Server Info</td>
                <td>server_info.py</td>
                <td>Gestion des informations serveurs et synchronisation Zabbix</td>
            </tr>
            <tr>
                <td>Server Usage</td>
                <td>server_usage.py</td>
                <td>Collecte et traitement des métriques d'utilisation</td>
            </tr>
            <tr>
                <td>Comparison</td>
                <td>server_comparison.py</td>
                <td>Logique de comparaison entre serveurs/périodes</td>
            </tr>
            <tr>
                <td>Threshold</td>
                <td>threshold.py</td>
                <td>Gestion des seuils et génération d'alertes</td>
            </tr>
            <tr>
                <td>Reporting</td>
                <td>reporting_capacity.py</td>
                <td>Génération de rapports PDF/Excel</td>
            </tr>
        </table>

        <div class="diagram-container">
            <div id="sequence-rapport" class="mermaid">
                <!-- Le diagramme sera chargé depuis scripts/diagram-loader.js -->
            </div>
            <div class="figure-caption">Figure 4.4 : Diagramme de séquence - Génération de rapport</div>
        </div>

        <div class="diagram-container">
            <div id="sequence-prediction" class="mermaid">
                <!-- Le diagramme sera chargé depuis scripts/diagram-loader.js -->
            </div>
            <div class="figure-caption">Figure 4.5 : Diagramme de séquence - Analyse prédictive</div>
        </div>

        <h3>4.4 Schémas d'architecture</h3>
        <p>Le schéma suivant présente l'architecture technique détaillée de CloudCapacity, mettant en évidence les
            différents composants et leurs interactions.</p>

        <div class="diagram-container">
            <div id="architecture-detaillee" class="mermaid">
                <!-- Le diagramme sera chargé depuis scripts/diagram-loader.js -->
            </div>
            <div class="figure-caption">Figure 4.6 : Schéma d'architecture technique détaillée</div>
        </div>

        <h3>4.5 Modèle de données</h3>
        <p>Le modèle de données suivant présente les principales tables de la base de données et leurs relations.</p>

        <div class="diagram-container">
            <div id="modele-donnees-detaille" class="mermaid">
                <!-- Le diagramme sera chargé depuis scripts/diagram-loader.js -->
            </div>
            <div class="figure-caption">Figure 4.7 : Modèle de données et relations</div>
        </div>
    </div>

    <!-- Chapitre 5 -->
    <div class="page latex-section">
        <h2>Chapitre 5 : Tests et validation</h2>

        <h3>5.1 Tests réalisés</h3>
        <p>La validation de CloudCapacity s'appuie sur une stratégie de tests multi-niveaux couvrant les aspects
            fonctionnels, techniques et d'intégration :</p>

        <h4>5.1.1 Tests fonctionnels</h4>
        <table class="table-data">
            <tr>
                <th>Fonctionnalité</th>
                <th>Test effectué</th>
                <th>Résultat</th>
            </tr>
            <tr>
                <td>Connexion API Zabbix</td>
                <td>Authentification et récupération de données</td>
                <td class="status-implemented">✓ Validé</td>
            </tr>
            <tr>
                <td>Affichage dashboard</td>
                <td>Chargement des métriques et alertes</td>
                <td class="status-implemented">✓ Validé</td>
            </tr>
            <tr>
                <td>Filtrage serveurs</td>
                <td>Filtres multiples et recherche textuelle</td>
                <td class="status-implemented">✓ Validé</td>
            </tr>
            <tr>
                <td>Graphiques métriques</td>
                <td>Visualisation CPU, mémoire, swap</td>
                <td class="status-implemented">✓ Validé</td>
            </tr>
            <tr>
                <td>Comparaison d'usage</td>
                <td>Comparaison serveurs et périodes</td>
                <td class="status-implemented">✓ Validé</td>
            </tr>
            <tr>
                <td>Alertes de seuil</td>
                <td>Génération et affichage d'alertes</td>
                <td class="status-implemented">✓ Validé</td>
            </tr>
        </table>

        <h4>5.1.2 Tests d'intégration</h4>
        <p>Les tests d'intégration valident la communication entre les composants :</p>
        <ul class="feature-list">
            <li>Communication Frontend Angular ↔ Backend FastAPI</li>
            <li>Intégration Backend ↔ API Zabbix</li>
            <li>Persistance des données en base Oracle</li>
            <li>Gestion des erreurs et timeouts</li>
            <li>Performance des requêtes complexes</li>
        </ul>

        <h3>5.2 Validation des performances</h3>
        <p>Les tests de performance démontrent la robustesse de la solution :</p>

        <table class="table-data">
            <tr>
                <th>Métrique</th>
                <th>Valeur mesurée</th>
                <th>Objectif</th>
                <th>Statut</th>
            </tr>
            <tr>
                <td>Temps de chargement dashboard</td>
                <td>
                    < 2 secondes</td>
                <td>
                    < 3 secondes</td>
                <td class="status-implemented">✓ Atteint</td>
            </tr>
            <tr>
                <td>Temps de réponse API</td>
                <td>
                    < 1.5 secondes</td>
                <td>
                    < 2 secondes</td>
                <td class="status-implemented">✓ Atteint</td>
            </tr>
            <tr>
                <td>Chargement liste serveurs</td>
                <td>
                    < 1 seconde</td>
                <td>
                    < 2 secondes</td>
                <td class="status-implemented">✓ Atteint</td>
            </tr>
            <tr>
                <td>Génération graphiques</td>
                <td>
                    < 0.5 secondes</td>
                <td>
                    < 1 seconde</td>
                <td class="status-implemented">✓ Atteint</td>
            </tr>
        </table>

        <h3>5.3 Optimisations réalisées</h3>
        <p>Plusieurs optimisations ont été mises en place pour améliorer les performances :</p>
        <ul class="feature-list">
            <li>Mise en cache des données côté frontend pour réduire les appels API</li>
            <li>Utilisation de ThreadPoolExecutor pour le traitement parallèle des requêtes Zabbix</li>
            <li>Optimisation des requêtes avec pagination et filtrage côté serveur</li>
            <li>Composants Angular autonomes pour réduire la taille du bundle</li>
            <li>Lazy loading des données pour améliorer la réactivité</li>
        </ul>
    </div>

    <!-- Chapitre 6 -->
    <div class="page latex-section">
        <h2>Bilan et perspectives</h2>

        <h3>6.1 État d'avancement</h3>
        <p>À mi-parcours du projet CloudCapacity, l'avancement est très satisfaisant avec un taux de réalisation
            de 85% des fonctionnalités principales :</p>

        <div class="performance-metric">
            <div class="metric-title">Fonctionnalités Core : 100% complétées</div>
            <p>Dashboard, gestion des serveurs, visualisation des métriques, comparaison d'usage, système d'alertes</p>
        </div>

        <div class="performance-metric">
            <div class="metric-title">Intégration Zabbix : 100% opérationnelle</div>
            <p>Collecte de données en temps réel, traitement des métriques historiques, gestion des erreurs</p>
        </div>

        <div class="performance-metric">
            <div class="metric-title">Architecture technique : 100% stable</div>
            <p>Backend FastAPI modulaire, frontend Angular 19 moderne, base de données Oracle intégrée</p>
        </div>

        <h3>6.2 Difficultés rencontrées</h3>
        <p>Le développement a rencontré quelques défis techniques qui ont été surmontés :</p>
        <ul>
            <li><strong>Intégration API Zabbix</strong> : Gestion des timeouts et optimisation des requêtes volumineuses
            </li>
            <li><strong>Performance des graphiques</strong> : Optimisation du rendu Chart.js pour de gros volumes de
                données</li>
            <li><strong>Thème UI cohérent</strong> : Migration complète vers le Modern Dark Dashboard et suppression des
                anciens thèmes</li>
            <li><strong>Comparaison complexe</strong> : Implémentation de la logique de comparaison serveurs/périodes
                avec calculs statistiques</li>
        </ul>

        <h3>6.3 Perspectives et améliorations futures</h3>
        <p>La suite du développement de CloudCapacity se concentrera sur les axes suivants :</p>

        <h4>6.3.1 Fonctionnalités à court terme (3 mois)</h4>
        <table class="table-data">
            <tr>
                <th>Fonctionnalité</th>
                <th>Priorité</th>
                <th>Effort estimé</th>
            </tr>
            <tr>
                <td>Finalisation du reporting PDF/Excel</td>
                <td>Haute</td>
                <td>2 semaines</td>
            </tr>
            <tr>
                <td>Notifications par email</td>
                <td>Haute</td>
                <td>1 semaine</td>
            </tr>
            <tr>
                <td>Historique des alertes</td>
                <td>Moyenne</td>
                <td>1 semaine</td>
            </tr>
            <tr>
                <td>Export des données de comparaison</td>
                <td>Moyenne</td>
                <td>1 semaine</td>
            </tr>
        </table>

        <h4>6.3.2 Fonctionnalités à moyen terme (6 mois)</h4>
        <ul class="feature-list">
            <li>Intégration avec Jira pour la création automatique de tickets</li>
            <li>Algorithmes de machine learning pour la prédiction de surcharges</li>
            <li>API REST publique pour intégration avec d'autres outils</li>
            <li>Tableau de bord personnalisable par utilisateur</li>
            <li>Gestion des rôles et permissions avancée</li>
        </ul>

        <h4>6.3.3 Évolutions à long terme</h4>
        <ul class="feature-list">
            <li>Extension vers le monitoring cloud (AWS, Azure)</li>
            <li>Intégration avec des outils de provisioning automatique</li>
            <li>Analyse prédictive avancée avec IA</li>
            <li>Module de recommandations d'optimisation</li>
        </ul>
    </div>

    <!-- Conclusion -->
    <div class="page latex-section">
        <h2>Conclusion générale</h2>
        <p>Ce rapport de mi-parcours démontre que le projet CloudCapacity a atteint ses objectifs intermédiaires
            avec succès. La plateforme développée répond déjà aux besoins principaux de monitoring et d'analyse
            de la capacité des serveurs chez Sopra HR Software.</p>

        <p>Les réalisations accomplies incluent :</p>
        <ul>
            <li><strong>Architecture technique robuste</strong> : Stack moderne Angular 19 + FastAPI avec intégration
                Zabbix complète</li>
            <li><strong>Interface utilisateur moderne</strong> : Thème Modern Dark Dashboard cohérent et ergonomique
            </li>
            <li><strong>Fonctionnalités core opérationnelles</strong> : Dashboard, gestion serveurs, comparaison
                d'usage, alertes</li>
            <li><strong>Performances optimisées</strong> : Temps de réponse sous les objectifs fixés</li>
            <li><strong>Intégration réussie</strong> : Communication fluide avec l'API Zabbix et base Oracle</li>
        </ul>

        <p>Le projet CloudCapacity s'inscrit dans une démarche d'amélioration continue de la gestion d'infrastructure,
            offrant une visibilité accrue sur l'utilisation des ressources et permettant une prise de décision éclairée.
            La suite du développement se concentrera sur l'enrichissement fonctionnel et l'intégration d'algorithmes
            prédictifs pour anticiper les besoins futurs.</p>

        <p>Cette réalisation constitue une base solide pour la transformation numérique des processus de monitoring
            chez Sopra HR Software, démontrant la valeur ajoutée d'une approche moderne et automatisée de la gestion
            de capacité.</p>
    </div>

    <!-- Bibliographie -->
    <div class="page latex-section">
        <h2>Bibliographie</h2>
        <ul>
            <li>[1] Documentation Sopra HR Software, "Infrastructure et monitoring des serveurs", 2025.</li>
            <li>[2] Documentation technique Zabbix, "Zabbix API Reference", <a
                    href="https://www.zabbix.com/documentation/current/en/manual/api">https://www.zabbix.com/documentation/current/en/manual/api</a>,
                consulté en décembre 2025.</li>
            <li>[3] Documentation technique FastAPI, "FastAPI Documentation", <a
                    href="https://fastapi.tiangolo.com/">https://fastapi.tiangolo.com/</a>, consulté en décembre 2025.
            </li>
            <li>[4] Documentation Angular, "Angular v19 Documentation", <a
                    href="https://angular.io/docs">https://angular.io/docs</a>, consulté en décembre 2025.</li>
            <li>[5] PyZabbix Documentation, "Python Zabbix API", <a
                    href="https://github.com/lukecyca/pyzabbix">https://github.com/lukecyca/pyzabbix</a>, consulté en
                décembre 2025.</li>
            <li>[6] Chart.js Documentation, "Chart.js Documentation", <a
                    href="https://www.chartjs.org/docs/">https://www.chartjs.org/docs/</a>, consulté en décembre 2025.
            </li>
            <li>[7] SQLModel Documentation, "SQLModel Documentation", <a
                    href="https://sqlmodel.tiangolo.com/">https://sqlmodel.tiangolo.com/</a>, consulté en décembre 2025.
            </li>
            <li>[8] Angular Material, "Angular Material Components", <a
                    href="https://material.angular.io/">https://material.angular.io/</a>, consulté en décembre 2025.
            </li>
            <li>[9] Oracle Database Documentation, "Oracle Database 19c", <a
                    href="https://docs.oracle.com/en/database/oracle/oracle-database/19/">https://docs.oracle.com/en/database/oracle/oracle-database/19/</a>,
                consulté en décembre 2025.</li>
            <li>[10] Standards académiques tunisiens, "Guide de rédaction des rapports PFE", Ministère de l'Enseignement
                Supérieur, 2025.</li>
        </ul>
    </div>

    <!-- Annexes -->
    <div class="page latex-section">
        <h2>Annexes</h2>
        <h3>Annexe 1 : Glossaire technique</h3>
        <table class="table-data">
            <tr>
                <th>Terme</th>
                <th>Définition</th>
            </tr>
            <tr>
                <td>API (Application Programming Interface)</td>
                <td>Interface permettant à différents logiciels de communiquer entre eux.</td>
            </tr>
            <tr>
                <td>CMDB (Configuration Management Database)</td>
                <td>Base de données contenant les informations relatives aux composants d'un système informatique.</td>
            </tr>
            <tr>
                <td>Conteneurisation</td>
                <td>Méthode de virtualisation au niveau du système d'exploitation permettant d'exécuter des applications
                    dans des environnements isolés appelés conteneurs.</td>
            </tr>
            <tr>
                <td>Docker</td>
                <td>Plateforme de conteneurisation permettant de créer, déployer et exécuter des applications dans des
                    conteneurs.</td>
            </tr>
            <tr>
                <td>FastAPI</td>
                <td>Framework Python moderne pour le développement d'API RESTful.</td>
            </tr>
            <tr>
                <td>Kubernetes</td>
                <td>Système open-source d'orchestration de conteneurs.</td>
            </tr>
            <tr>
                <td>Machine Learning</td>
                <td>Sous-domaine de l'intelligence artificielle qui utilise des algorithmes statistiques pour permettre
                    aux systèmes informatiques d'apprendre à partir de données.</td>
            </tr>
            <tr>
                <td>OpenShift</td>
                <td>Plateforme d'application conteneurisée de Red Hat basée sur Kubernetes.</td>
            </tr>
            <tr>
                <td>Zabbix</td>
                <td>Logiciel de surveillance réseau open-source.</td>
            </tr>
        </table>

        <h3>Annexe 2 : Exemples de maquettes d'interface utilisateur</h3>
        <p>Les maquettes détaillées de l'interface utilisateur seraient normalement incluses ici, avec des captures
            d'écran des principaux écrans de l'application.</p>

        <h3>Annexe 3 : Endpoints API implémentés</h3>
        <div class="code">
            <div class="code-title">Endpoints principaux de CloudCapacity</div>
            <pre>
// Récupération de la liste des serveurs
GET /capacity/get-servers
Response: Server[]

// Synchronisation des données Zabbix
POST /capacity/return-and-sync-hostnames-and-metrics
Response: SyncResult

// Métriques d'usage d'un serveur pour un mois
POST /capacity/server-usage-per-month
Body: { hostname: string, year: number, month: number }
Response: ZabbixData

// Comparaison d'usage entre serveurs ou périodes
POST /capacity/compare-server-usage
Body: ComparisonRequest
Response: ComparisonDataPoint[]

// Alertes de seuil
GET /capacity/servers-usage-threshold
Response: ThresholdAlert[]

// Génération de rapport de capacité
POST /capacity/generate-capacity-report
Body: ReportRequest
Response: ReportData
            </pre>
        </div>

        <h3>Annexe 4 : Structure des composants Angular</h3>
        <div class="code">
            <div class="code-title">Architecture des composants principaux</div>
            <pre>
src/app/
├── components/
│   ├── dashboard/
│   │   ├── dashboard.component.ts
│   │   └── summary-cards.component.ts
│   ├── server-list/
│   │   └── server-list.component.ts
│   ├── server-detail/
│   │   └── server-detail.component.ts
│   ├── usage-comparison/
│   │   └── usage-comparison.component.ts
│   ├── threshold-alerts/
│   │   └── threshold-alerts.component.ts
│   └── shared/
│       ├── modern-table.component.ts
│       ├── browser-chart.component.ts
│       └── navbar.component.ts
├── services/
│   ├── capacity.service.ts
│   ├── server.service.ts
│   └── chart.service.ts
└── models/
    ├── server.model.ts
    ├── zabbix-data.model.ts
    └── comparison.model.ts
            </pre>
        </div>
    </div>

    <!-- Le script de chargement des diagrammes est maintenant dans scripts/diagram-loader.js -->
</body>

</html>