# Script de Validation - Diagrammes Mermaid
# Vérifie que tous les diagrammes sont correctement configurés et fonctionnels

param(
    [string]$HtmlFile = "rapport_latex.html",
    [string]$JsFile = "scripts/diagram-loader.js"
)

Write-Host "=== Validation des Diagrammes Mermaid ===" -ForegroundColor Cyan
Write-Host ""

$errors = @()
$warnings = @()
$success = @()

# Liste des diagrammes attendus
$expectedDiagrams = @(
    "architecture-generale",
    "cas-utilisation", 
    "sequence-collecte",
    "modele-donnees",
    "sequence-rapport",
    "sequence-prediction",
    "architecture-detaillee",
    "modele-donnees-detaille"
)

# 1. Vérifier l'existence des fichiers
Write-Host "🔍 Vérification des fichiers..." -ForegroundColor Yellow

if (-not (Test-Path $HtmlFile)) {
    $errors += "❌ Fichier HTML non trouvé: $HtmlFile"
} else {
    $success += "✅ Fichier HTML trouvé: $HtmlFile"
}

if (-not (Test-Path $JsFile)) {
    $errors += "❌ Fichier JavaScript non trouvé: $JsFile"
} else {
    $success += "✅ Fichier JavaScript trouvé: $JsFile"
}

# 2. Vérifier les IDs des diagrammes dans le HTML
Write-Host "🔍 Vérification des IDs de diagrammes dans le HTML..." -ForegroundColor Yellow

$htmlContent = Get-Content $HtmlFile -Raw
$foundDiagramIds = @()

foreach ($diagramId in $expectedDiagrams) {
    if ($htmlContent -match "id=`"$diagramId`"") {
        $foundDiagramIds += $diagramId
        $success += "✅ ID trouvé dans HTML: $diagramId"
    } else {
        $errors += "❌ ID manquant dans HTML: $diagramId"
    }
}

# 3. Vérifier les définitions des diagrammes dans le JavaScript
Write-Host "🔍 Vérification des définitions dans le JavaScript..." -ForegroundColor Yellow

$jsContent = Get-Content $JsFile -Raw
$foundDiagramDefs = @()

foreach ($diagramId in $expectedDiagrams) {
    if ($jsContent -match "'$diagramId':\s*``") {
        $foundDiagramDefs += $diagramId
        $success += "✅ Définition trouvée dans JS: $diagramId"
    } else {
        $errors += "❌ Définition manquante dans JS: $diagramId"
    }
}

# 4. Vérifier la syntaxe Mermaid de base
Write-Host "🔍 Vérification de la syntaxe Mermaid..." -ForegroundColor Yellow

$mermaidPatterns = @{
    "flowchart" = "flowchart (TD|TB|LR|RL)"
    "sequenceDiagram" = "sequenceDiagram"
    "erDiagram" = "erDiagram"
    "graph" = "graph (TD|TB|LR|RL)"
}

$syntaxErrors = 0
foreach ($pattern in $mermaidPatterns.Keys) {
    $regex = $mermaidPatterns[$pattern]
    $matches = [regex]::Matches($jsContent, $regex, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($matches.Count -gt 0) {
        $success += "✅ Syntaxe $pattern trouvée ($($matches.Count) occurrence(s))"
    }
}

# 5. Vérifier les liens CDN Mermaid
Write-Host "🔍 Vérification des liens CDN Mermaid..." -ForegroundColor Yellow

if ($htmlContent -match "mermaid@\d+\.\d+\.\d+") {
    $success += "✅ Lien CDN Mermaid trouvé"
} else {
    $errors += "❌ Lien CDN Mermaid manquant ou incorrect"
}

# 6. Vérifier la fonction de chargement des diagrammes
Write-Host "🔍 Vérification de la fonction de chargement..." -ForegroundColor Yellow

if ($jsContent -match "function loadDiagram") {
    $success += "✅ Fonction loadDiagram trouvée"
} else {
    $errors += "❌ Fonction loadDiagram manquante"
}

if ($jsContent -match "Object\.keys\(diagramsContent\)\.forEach") {
    $success += "✅ Boucle de chargement des diagrammes trouvée"
} else {
    $errors += "❌ Boucle de chargement des diagrammes manquante"
}

# 7. Vérifier la configuration Mermaid
Write-Host "🔍 Vérification de la configuration Mermaid..." -ForegroundColor Yellow

if ($jsContent -match "mermaid\.initialize") {
    $success += "✅ Configuration Mermaid trouvée"
} else {
    $warnings += "⚠️ Configuration Mermaid manquante"
}

# 8. Analyser les types de diagrammes
Write-Host "🔍 Analyse des types de diagrammes..." -ForegroundColor Yellow

$diagramTypes = @{
    "Architecture" = @("architecture-generale", "architecture-detaillee")
    "Cas d'utilisation" = @("cas-utilisation")
    "Séquence" = @("sequence-collecte", "sequence-rapport", "sequence-prediction")
    "Modèle de données" = @("modele-donnees", "modele-donnees-detaille")
}

foreach ($type in $diagramTypes.Keys) {
    $diagrams = $diagramTypes[$type]
    $foundCount = 0
    foreach ($diagram in $diagrams) {
        if ($diagram -in $foundDiagramIds -and $diagram -in $foundDiagramDefs) {
            $foundCount++
        }
    }
    if ($foundCount -eq $diagrams.Count) {
        $success += "✅ Type $type complet ($foundCount/$($diagrams.Count))"
    } else {
        $warnings += "⚠️ Type $type incomplet ($foundCount/$($diagrams.Count))"
    }
}

# Affichage des résultats
Write-Host ""
Write-Host "📊 RÉSULTATS DE LA VALIDATION" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

if ($success.Count -gt 0) {
    Write-Host ""
    Write-Host "✅ SUCCÈS ($($success.Count)):" -ForegroundColor Green
    foreach ($item in $success) {
        Write-Host "  $item" -ForegroundColor Green
    }
}

if ($warnings.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️ AVERTISSEMENTS ($($warnings.Count)):" -ForegroundColor Yellow
    foreach ($item in $warnings) {
        Write-Host "  $item" -ForegroundColor Yellow
    }
}

if ($errors.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ ERREURS ($($errors.Count)):" -ForegroundColor Red
    foreach ($item in $errors) {
        Write-Host "  $item" -ForegroundColor Red
    }
}

# Statistiques
Write-Host ""
Write-Host "📈 STATISTIQUES:" -ForegroundColor Cyan
Write-Host "  Diagrammes attendus: $($expectedDiagrams.Count)" -ForegroundColor White
Write-Host "  IDs trouvés dans HTML: $($foundDiagramIds.Count)" -ForegroundColor White
Write-Host "  Définitions trouvées dans JS: $($foundDiagramDefs.Count)" -ForegroundColor White
Write-Host "  Taux de complétude: $([math]::Round(($foundDiagramDefs.Count / $expectedDiagrams.Count) * 100, 1))%" -ForegroundColor White

# Conclusion
Write-Host ""
if ($errors.Count -eq 0) {
    Write-Host "🎉 VALIDATION RÉUSSIE - Tous les diagrammes sont correctement configurés !" -ForegroundColor Green
    Write-Host "   Les diagrammes Mermaid devraient s'afficher correctement dans le rapport." -ForegroundColor Green
    exit 0
} else {
    Write-Host "💥 VALIDATION ÉCHOUÉE - Des problèmes ont été détectés." -ForegroundColor Red
    Write-Host "   Veuillez corriger les erreurs avant de continuer." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Fin de la Validation ===" -ForegroundColor Cyan
