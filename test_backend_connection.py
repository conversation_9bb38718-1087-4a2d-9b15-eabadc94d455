#!/usr/bin/env python3
"""
Test script to verify backend connectivity and endpoints
"""

import requests
import json
import sys
from urllib3.exceptions import InsecureRequestWarning

# Disable SSL warnings for testing
requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

def test_endpoint(url, description):
    """Test a single endpoint and return results."""
    print(f"\n🔍 Testing: {description}")
    print(f"URL: {url}")
    
    try:
        # Try with SSL verification first
        response = requests.get(url, timeout=10, verify=True)
        print(f"✅ Status: {response.status_code}")
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"📄 Response: {json.dumps(data, indent=2)[:200]}...")
            except:
                print(f"📄 Response: {response.text[:200]}...")
        return True
    except requests.exceptions.SSLError:
        print("⚠️  SSL verification failed, trying without verification...")
        try:
            response = requests.get(url, timeout=10, verify=False)
            print(f"✅ Status: {response.status_code} (SSL verification disabled)")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"📄 Response: {json.dumps(data, indent=2)[:200]}...")
                except:
                    print(f"📄 Response: {response.text[:200]}...")
            return True
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("🚀 CloudCapacity Backend Connection Test")
    print("=" * 60)
    
    # Test URLs
    test_urls = [
        # Production URL (what frontend expects)
        ("https://becloudcapacity.soprahronline.sopra/", "Production Root"),
        ("https://becloudcapacity.soprahronline.sopra/health", "Production Health Check"),
        ("https://becloudcapacity.soprahronline.sopra/capacity/get-servers", "Production Get Servers"),
        
        # Alternative URLs to test
        ("https://becloudcapacity.soprahronline.sopra:9099/", "Production Root (Port 9099)"),
        ("https://becloudcapacity.soprahronline.sopra:9099/health", "Production Health (Port 9099)"),
        ("https://becloudcapacity.soprahronline.sopra:9099/capacity/get-servers", "Production Servers (Port 9099)"),
        
        # HTTP fallback
        ("http://becloudcapacity.soprahronline.sopra/", "HTTP Root"),
        ("http://becloudcapacity.soprahronline.sopra/health", "HTTP Health Check"),
        ("http://becloudcapacity.soprahronline.sopra:9099/", "HTTP Root (Port 9099)"),
        ("http://becloudcapacity.soprahronline.sopra:9099/health", "HTTP Health (Port 9099)"),
    ]
    
    successful_tests = 0
    total_tests = len(test_urls)
    
    for url, description in test_urls:
        if test_endpoint(url, description):
            successful_tests += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {successful_tests}/{total_tests} successful")
    
    if successful_tests == 0:
        print("❌ No endpoints are accessible. Possible issues:")
        print("   1. Backend container is not running")
        print("   2. Backend is not exposed on the expected port")
        print("   3. Network routing issues")
        print("   4. SSL certificate problems")
        print("   5. Firewall blocking connections")
    elif successful_tests < total_tests:
        print("⚠️  Some endpoints are accessible. Check the working URLs above.")
    else:
        print("✅ All endpoints are accessible!")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
