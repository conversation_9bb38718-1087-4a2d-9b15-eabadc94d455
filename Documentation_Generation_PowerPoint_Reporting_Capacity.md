# Documentation - Génération de PowerPoint dans le Composant Reporting-Capacity

## Vue d'ensemble

Le composant `reporting-capacity` génère des rapports PowerPoint (.pptx) avec les données de configuration des serveurs sélectionnés.

## Bibliothèques Utilisées

### Frontend (Angular)
- **Angular Material** - Interface utilisateur (dialogues, boutons, icônes)
- **RxJS** - Gestion des requêtes HTTP asynchrones
- **File-saver** - Téléchargement automatique des fichiers
- **html2canvas** - Capture d'écran pour formats PNG/PDF
- **jsPDF** - Génération PDF alternative

### Backend (Python/FastAPI)
- **python-pptx** - Génération des fichiers PowerPoint
- **FastAPI** - API REST et gestion des endpoints
- **SQLAlchemy** - Accès aux données des serveurs
- **Pydantic** - Validation des modèles de données

## Fonctionnement

### 1. Sélection et Configuration
- L'utilisateur sélectionne les serveurs dans l'interface
- Clique sur "Export Report" pour ouvrir le dialogue
- Configure le titre, date et format (PNG/PDF/PPTX)

### 2. Génération Frontend
- La méthode `exportAsPPTX()` prépare les données
- Appel API vers `/generate-capacity-report-pptx`
- Affichage d'une barre de progression

### 3. Traitement Backend
- Récupération des données serveurs depuis la base
- Génération PowerPoint avec `python-pptx` :
  - **Diapositive 1** : Page de couverture avec titre et informations système
  - **Diapositive 2** : Tableau des serveurs (Hostname, Environment, Threads, RAM, Swap)
- Stylisation professionnelle (couleurs, polices, alignement)

### 4. Téléchargement
- Retour du fichier .pptx en flux binaire
- Téléchargement automatique avec nom formaté (nombre de serveurs + horodatage)

## Gestion des Erreurs

- **Frontend** : Vérification sélection serveurs, gestion erreurs API, messages traduits
- **Backend** : Validation données, logging détaillé, erreurs HTTP appropriées

## Fichiers Principaux

### Frontend
- `reporting-capacity.component.ts` - Composant principal
- `reporting-capacity-export-dialog.component.ts` - Interface d'export
- `api.service.ts` - Communication API

### Backend
- `reporting_capacity.py` - Génération PowerPoint
- `models.py` - Modèles de données
- `server_info.py` - Récupération données serveurs

Cette solution offre une génération automatisée et professionnelle de rapports PowerPoint à partir des données de capacité des serveurs.
