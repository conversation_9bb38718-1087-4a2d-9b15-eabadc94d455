%========= Chapitre 5: Tests et validation =========%
%                                                   %
% Chapter 5: Testing and validation                 %
%===================================================%

\chapter{Tests et validation}

\section{Stratégie de tests}

La validation de \projectTitle\ s'appuie sur une stratégie de tests multi-niveaux garantissant la qualité et la fiabilité de la solution :

\begin{itemize}
    \item \textbf{Tests unitaires} : Validation des fonctions individuelles et des composants
    \item \textbf{Tests d'intégration} : Vérification des interactions entre modules
    \item \textbf{Tests fonctionnels} : Validation des exigences métier
    \item \textbf{Tests de performance} : Évaluation des temps de réponse et de la scalabilité
    \item \textbf{Tests d'acceptation} : Validation par les utilisateurs finaux
\end{itemize}

\section{Tests backend}

\subsection{Tests unitaires FastAPI}

Les tests unitaires du backend couvrent les principales fonctionnalités :

\begin{table}[H]
\centering
\begin{tabular}{|p{4cm}|p{3cm}|p{6cm}|}
\hline
\textbf{Module testé} & \textbf{Couverture} & \textbf{Cas de test} \\
\hline
Module d'information & 95\% & Récupération serveurs, synchronisation Zabbix \\
\hline
Module d'utilisation & 90\% & Calcul métriques, agrégation données \\
\hline
Module de comparaison & 85\% & Comparaisons serveurs, validation périodes \\
\hline
Module de seuils & 92\% & Gestion seuils, génération alertes \\
\hline
\end{tabular}
\caption{Couverture des tests unitaires backend}
\label{tab:tests-unitaires-backend}
\end{table}

\subsection{Tests d'intégration API}

Les tests d'intégration valident les interactions avec les systèmes externes :

\begin{itemize}
    \item \textbf{Intégration Zabbix} : Tests de connexion, authentification et récupération de données
    \item \textbf{Base de données Oracle} : Tests CRUD et requêtes complexes
    \item \textbf{API endpoints} : Validation des réponses HTTP et formats JSON
    \item \textbf{Gestion d'erreurs} : Tests de robustesse face aux pannes
\end{itemize}

\section{Tests frontend}

\subsection{Tests unitaires Angular}

Les composants Angular sont testés avec Jasmine et Karma :

\begin{table}[H]
\centering
\footnotesize % Réduction de la taille de la police pour ce tableau uniquement
\renewcommand{\arraystretch}{1.3}
\begin{tabular}{|p{4cm}|p{3cm}|p{6cm}|}
\hline
\textbf{Composant} & \textbf{Couverture} & \textbf{Tests réalisés} \\
\hline
DashboardComponent & 88\% & Affichage des métriques, navigation \\
\hline
ServerListComponent & 92\% & Filtrage, tri, pagination \\
\hline
ServerDetailComponent & 85\% & Graphiques, sélection de la période \\
\hline
UsageComparison & 80\% & Comparaisons, validation des données \\
\hline
\end{tabular}
\caption{Couverture des tests unitaires frontend}
\label{tab:tests-unitaires-frontend}
\end{table}


\subsection{Tests end-to-end}

Les tests end-to-end avec Cypress valident les parcours utilisateur complets :

\begin{itemize}
    \item Navigation entre les différentes sections
    \item Filtrage et recherche dans les listes
    \item Affichage des détails serveur
    \item Configuration des alertes
    \item Génération de comparaisons
\end{itemize}

\section{Tests de performance}

\subsection{Métriques de performance}

Les tests de performance évaluent les aspects critiques de la solution :

\begin{table}[H]
\centering
\begin{tabular}{|p{4cm}|p{3cm}|p{3cm}|p{3cm}|}
\hline
\textbf{Métrique} & \textbf{Objectif} & \textbf{Résultat} & \textbf{Statut} \\
\hline
Temps de chargement initial & < 3s & 2.1s & ✓ \\
\hline
Réponse API serveurs & < 1s & 0.8s & ✓ \\
\hline
Génération graphiques & < 2s & 1.5s & ✓ \\
\hline
Collecte métriques Zabbix & < 5s & 3.2s & ✓ \\
\hline
\end{tabular}
\caption{Résultats des tests de performance}
\label{tab:tests-performance}
\end{table}

\subsection{Tests de charge}

Les tests de charge simulent l'utilisation simultanée par plusieurs utilisateurs :

\begin{itemize}
    \item \textbf{10 utilisateurs simultanés} : Temps de réponse stable < 2s
    \item \textbf{25 utilisateurs simultanés} : Légère dégradation acceptable < 3s
    \item \textbf{50 utilisateurs simultanés} : Limite recommandée atteinte
\end{itemize}

\section{Validation fonctionnelle}

\subsection{Tests d'acceptation utilisateur}

Les tests d'acceptation ont été réalisés avec les équipes DevOps de \companyName :

\begin{table}[H]
\centering
\begin{tabular}{|p{5cm}|p{3cm}|p{5cm}|}
\hline
\textbf{Fonctionnalité testée} & \textbf{Résultat} & \textbf{Commentaires} \\
\hline
Dashboard principal & ✓ Validé & Interface intuitive et informative \\
\hline
Filtrage serveurs & ✓ Validé & Critères pertinents et performants \\
\hline
Visualisation métriques & ✓ Validé & Graphiques clairs et détaillés \\
\hline
Système d'alertes & ✓ Validé & Configuration flexible et efficace \\
\hline
Comparaison d'usage & ✓ Validé & Outil puissant pour l'analyse \\
\hline
\end{tabular}
\caption{Résultats des tests d'acceptation}
\label{tab:tests-acceptation}
\end{table}

\subsection{Retours utilisateurs}

Les retours des utilisateurs finaux sont globalement positifs :

\begin{itemize}
    \item \textbf{Points forts} : Interface moderne, fonctionnalités complètes, performance satisfaisante
    \item \textbf{Améliorations suggérées} : Export Excel des comparaisons, notifications par email
    \item \textbf{Satisfaction globale} : 8.5/10
\end{itemize}

\section{Validation technique}

\subsection{Conformité aux exigences}

La validation technique confirme la conformité aux exigences initiales :

\begin{table}[H]
\centering
\begin{tabular}{|p{4cm}|p{3cm}|p{6cm}|}
\hline
\textbf{Exigence} & \textbf{Statut} & \textbf{Validation} \\
\hline
Intégration Zabbix & ✓ Conforme & API fonctionnelle et robuste \\
\hline
Performance < 2s & ✓ Conforme & Temps de réponse respectés \\
\hline
Interface responsive & ✓ Conforme & Compatible mobile et desktop \\
\hline
Sécurité HTTPS & ✓ Conforme & Chiffrement et authentification \\
\hline
Scalabilité 50+ serveurs & ✓ Conforme & Tests réalisés avec succès \\
\hline
\end{tabular}
\caption{Validation des exigences techniques}
\label{tab:validation-exigences}
\end{table}

\clearpage
