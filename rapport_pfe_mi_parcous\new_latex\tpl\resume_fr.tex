%========= Résumé en Français =========%
%                                      %
% French abstract page                 %
%======================================%

\chapter*{Résumé}
\addcontentsline{toc}{chapter}{Résumé}
\thispagestyle{empty}

\textbf{Contexte :} Dans un environnement informatique en constante évolution, la gestion optimale des ressources serveurs constitue un enjeu majeur pour les entreprises. \companyName, éditeur de solutions RH, fait face à des défis de gestion et d'analyse de la capacité de son infrastructure on-premise composée de plus de 400 serveurs.

\textbf{Problématique :} L'absence d'une solution centralisée de gestion de capacité entraîne des difficultés dans la planification des ressources, la détection proactive des surcharges et la génération de rapports de capacité pour les équipes techniques et managériales.

\textbf{Objectif :} Ce projet de fin d'études vise à concevoir et développer \projectTitle, une plateforme web moderne permettant d'automatiser la gestion, l'analyse et le reporting de la capacité des serveurs, tout en intégrant des fonctionnalités d'alertes intelligentes et de comparaisons.

\textbf{Méthodologie :} L'approche adoptée suit une méthodologie Agile Hybrid adaptée au développement solo avec des objectifs hebdomadaires et un suivi via Microsoft Teams. Elle combine une analyse approfondie des besoins métier, une conception architecturale basée sur des technologies modernes (Angular 19, FastAPI, Oracle), une intégration native avec l'infrastructure existante (Zabbix & MAIA CMDB), et un déploiement automatisé via pipeline GitLab CI/CD avec containerisation Docker sur la plateforme OpenShift d'entreprise.

\textbf{Résultats :} Ce rapport de mi-parcours présente un taux d'avancement de 88\% avec l'implémentation complète du frontend Angular 19, du backend FastAPI modulaire, et des intégrations Zabbix et MAIA CMDB. Les milestones actuels ont livré un dashboard interactif, un système de comparaison d'usage sophistiqué, et un module de reporting. Les milestones futurs incluront le déploiement OpenShift en production et l'intégration d'intelligence artificielle avec CatBoost pour la prédiction de capacité.

\textbf{Impact :} La plateforme démontre déjà sa capacité à centraliser la gestion des ressources, automatiser le reporting de capacité et améliorer la prise de décision concernant l'évolution de l'infrastructure, répondant ainsi aux objectifs initiaux du projet.

\textbf{Mots-clés :} Gestion de capacité, Capacité serveurs, Angular 19, FastAPI, Zabbix, Dashboard, Alertes, Reporting, Infrastructure, DevOps.

\clearpage
