%========= Chapitre 4: Réalisation et implémentation =========%
%                                                             %
% Chapter 4: Implementation and development                   %
%=============================================================%

\chapter{Réalisation et implémentation}

\section{Environnement de développement}

Le développement de \projectTitle\ s'appuie sur un environnement technique moderne et robuste :

\begin{table}[H]
\centering
\begin{tabular}{|p{3cm}|p{3cm}|p{7cm}|}
\hline
\textbf{Composant} & \textbf{Version} & \textbf{Utilisation} \\
\hline
Environnement JavaScript & v20.16.0+ & Runtime pour le frontend Angular \\
\hline
Angular CLI & v19.2.8+ & Outils de développement Angular \\
\hline
Python & 3.12+ & Runtime pour FastAPI backend \\
\hline
FastAPI & Latest & Framework API REST \\
\hline
Oracle Database & 19c & Base de données principale \\
\hline
Zabbix & 6.x & Système de collecte de métriques source \\
\hline
\end{tabular}
\caption{Environnement de développement}
\label{tab:environnement-dev}
\end{table}

\section{Implémentation du backend}

\subsection{API REST avec FastAPI}

L'architecture backend est organisée en modules spécialisés offrant une séparation claire des responsabilités :

\begin{table}[H]
\centering
\begin{tabular}{|p{3cm}|p{4cm}|p{6cm}|}
\hline
\textbf{Module} & \textbf{Fichier} & \textbf{Responsabilité} \\
\hline
Server Info & Module d'information & Gestion des informations serveurs et synchronisation Zabbix \\
\hline
Server Usage & Module d'utilisation & Collecte et traitement des métriques d'utilisation \\
\hline
Comparison & Module de comparaison & Logique de comparaison entre serveurs/périodes \\
\hline
Threshold & Module de seuils & Gestion des seuils et génération d'alertes \\
\hline
Reporting & Module de reporting & Génération de rapports PDF/Excel \\
\hline
Client Configuration & Module de configuration & Synchronisation MAIA CMDB et gestion clients \\
\hline
\end{tabular}
\caption{Modules backend implémentés}
\label{tab:modules-backend}
\end{table}

\subsection{Intégration API Zabbix}

L'intégration avec l'API Zabbix permet la collecte automatique des métriques de performance des serveurs. Cette intégration suit un processus de synchronisation bidirectionnelle pour maintenir la cohérence des données entre Zabbix et la base de données Oracle locale.

\subsection{Intégration MAIA CMDB}

L'intégration avec MAIA CMDB (Configuration Management Database) constitue un pilier essentiel de \projectTitle\ pour la gestion des configurations clients et l'organisation des serveurs par environnement.

\subsubsection{Architecture de l'intégration MAIA}

L'intégration MAIA CMDB suit une architecture de synchronisation via proxy SOCKS :

\begin{itemize}
    \item \textbf{Connexion sécurisée} : Accès à la base MAIA via proxy SOCKS (10.4.2.110:8443)
    \item \textbf{Base de données source} : MAIA MySQL (10.4.2.16) contenant la table clients\_conf
    \item \textbf{Synchronisation automatique} : Transfert des configurations vers Oracle local
    \item \textbf{Gestion des duplicatas} : Évitement des doublons par clés uniques
\end{itemize}

\subsubsection{Données synchronisées depuis MAIA}

Les informations clients synchronisées incluent :

\begin{itemize}
    \item \textbf{Identification client} : custconflabel, clientname, progiciel
    \item \textbf{Environnement technique} : platform, envtype, dataprivacy
    \item \textbf{Inventaire serveurs} : webserver, treatmentserver, dbserver1/2
    \item \textbf{Serveurs applicatifs} : qserver1 à qserver6 pour la montée en charge
    \item \textbf{Statut} : active pour filtrer les configurations en production
\end{itemize}

L'intégration avec Zabbix utilise la bibliothèque PyZabbix pour :

\begin{itemize}
    \item Authentification sécurisée avec gestion des sessions
    \item Récupération des listes de serveurs avec leurs caractéristiques
    \item Collecte des métriques historiques (CPU, mémoire, swap)
    \item Traitement parallèle des requêtes pour optimiser les performances
    \item Gestion robuste des erreurs et timeouts
\end{itemize}

\section{Implémentation du frontend}

\subsection{Interface utilisateur Angular}

Le frontend Angular 19 implémente une architecture basée sur des composants autonomes (standalone) avec :

\begin{itemize}
    \item Thème Modern Dark Dashboard cohérent
    \item Composants réutilisables et modulaires
    \item Gestion d'état locale avec services Angular
    \item Intégration de bibliothèques de visualisation
    \item Responsive design avec Angular Material
\end{itemize}

\subsection{Composants développés}

Les composants principaux implémentés incluent :

\begin{itemize}
    \item \textbf{DashboardComponent} : Vue d'ensemble avec cartes de résumé et alertes récentes
    \item \textbf{ServerListComponent} : Liste paginée avec filtrage multi-critères et recherche
    \item \textbf{ServerDetailComponent} : Détails serveur avec graphiques de métriques historiques
    \item \textbf{UsageComparisonComponent} : Comparaison avancée entre serveurs et périodes
    \item \textbf{ThresholdAlertsComponent} : Configuration et gestion des alertes de seuil
    \item \textbf{ModernTableComponent} : Composant de tableau réutilisable avec tri et filtrage
    \item \textbf{Composant de graphiques} : Composant de visualisation optimisé pour les performances
\end{itemize}

\section{Fonctionnalités implémentées}

\subsection{Dashboard et métriques}

Le dashboard constitue le point d'entrée principal de l'application \projectTitle\ et offre :

\begin{itemize}
    \item Vue d'ensemble des métriques système avec cartes de résumé
    \item Affichage des alertes récentes avec statuts colorés
    \item Top des serveurs par utilisation CPU
    \item Navigation intuitive vers les autres sections
    \item Actualisation automatique des données
\end{itemize}

\subsection{Gestion des serveurs}

Le module de gestion des serveurs offre une interface complète pour la supervision :

\begin{itemize}
    \item Liste paginée avec filtrage multi-critères (type, statut, client, environnement)
    \item Recherche textuelle en temps réel
    \item Tri par colonnes (hostname, CPUs, mémoire, statut)
    \item Détails serveur avec métriques historiques
    \item Visualisation graphique des métriques CPU, mémoire, swap
    \item Sélection de période (mois/année) pour l'analyse historique
\end{itemize}

\subsection{Système d'alertes}

Le module d'alertes permet la surveillance proactive des seuils de performance :

\begin{itemize}
    \item Configuration de seuils par type de métrique (CPU, mémoire, swap)
    \item Génération automatique d'alertes en cas de dépassement
    \item Affichage des alertes avec niveaux de criticité
    \item Historique des alertes avec horodatage
    \item Intégration avec le dashboard pour visibilité immédiate
\end{itemize}

\section{État d'avancement des fonctionnalités}

À mi-parcours du projet, l'état d'avancement des fonctionnalités est le suivant :

\begin{table}[H]
\centering
\begin{tabular}{|p{4cm}|p{3cm}|p{2cm}|p{4cm}|}
\hline
\textbf{Fonctionnalité} & \textbf{État} & \textbf{\%} & \textbf{Description} \\
\hline
Dashboard principal & ✓ Implémenté & 100\% & Vue d'ensemble avec métriques et alertes \\
\hline
Liste des serveurs & ✓ Implémenté & 100\% & Filtrage, tri, pagination \\
\hline
Détails serveur & ✓ Implémenté & 100\% & Métriques détaillées avec graphiques \\
\hline
Comparaison d'usage & ✓ Implémenté & 100\% & Comparaison serveurs/périodes \\
\hline
Système d'alertes & ✓ Implémenté & 100\% & Alertes de seuil configurables \\
\hline
API Zabbix & ✓ Implémenté & 100\% & Intégration complète \\
\hline
MAIA CMDB & ✓ Implémenté & 100\% & Synchronisation configurations clients \\
\hline
Reporting PDF & ⚠ En cours & 80\% & Génération de rapports \\
\hline
Machine Learning & ○ Planifié & 0\% & Intégration CatBoost pour prédiction \\
\hline
Déploiement OpenShift & ⚠ En cours & 70\% & Containerisation et CI/CD \\
\hline
\end{tabular}
\caption{État d'avancement des fonctionnalités}
\label{tab:avancement-fonctionnalites}
\end{table}

Le taux d'avancement global du projet atteint 88\% avec l'implémentation complète des fonctionnalités core, de l'intégration Zabbix et de l'intégration MAIA CMDB.

\section{Déploiement et Infrastructure}

\subsection{Containerisation Docker}

La solution \projectTitle\ est packagée dans des conteneurs Docker pour assurer la portabilité et la scalabilité :

\begin{itemize}
    \item \textbf{Frontend} : Image basée sur Nginx avec build Angular optimisé
    \item \textbf{Backend} : Image Python avec FastAPI et dépendances Oracle
    \item \textbf{Multi-stage builds} : Optimisation de la taille des images
    \item \textbf{Health checks} : Surveillance de l'état des conteneurs
    \item \textbf{Configuration externalisée} : Variables d'environnement pour la flexibilité
\end{itemize}

\subsection{Pipeline CI/CD GitLab}

L'automatisation du déploiement s'appuie sur un pipeline GitLab CI/CD sophistiqué organisé en plusieurs stages :

\subsubsection{Architecture du pipeline}

Le pipeline GitLab CI/CD est structuré en 6 stages principaux :

\begin{itemize}
    \item \textbf{Stage 1 - Validation} : Linting du code, vérification des standards de codage
    \item \textbf{Stage 2 - Tests} : Tests unitaires frontend (Jest) et backend (pytest)
    \item \textbf{Stage 3 - Build} : Construction des images Docker optimisées
    \item \textbf{Stage 4 - Security} : Scan de sécurité des images et dépendances
    \item \textbf{Stage 5 - Deploy} : Déploiement sur OpenShift selon l'environnement
    \item \textbf{Stage 6 - Verify} : Tests post-déploiement et validation fonctionnelle
\end{itemize}

\subsubsection{Configuration des environnements}

Le pipeline gère trois environnements distincts :

\begin{itemize}
    \item \textbf{Développement} : Déploiement automatique sur chaque commit sur la branche develop
    \item \textbf{Staging} : Déploiement manuel pour validation métier avant production
    \item \textbf{Production} : Déploiement contrôlé avec approbation manuelle et stratégie blue-green
\end{itemize}

\subsubsection{Stratégies de déploiement}

Le pipeline implémente des stratégies de déploiement avancées :

\begin{itemize}
    \item \textbf{Blue-Green Deployment} : Déploiement sans interruption de service
    \item \textbf{Health Checks} : Vérification automatique de l'état des services
    \item \textbf{Rollback automatique} : Retour à la version précédente en cas d'échec des health checks
    \item \textbf{Monitoring continu} : Surveillance des métriques pendant et après le déploiement
    \item \textbf{Notifications} : Alertes automatiques vers les équipes en cas de problème
\end{itemize}

\subsubsection{Intégration OpenShift}

Le déploiement sur OpenShift utilise les outils natifs de la plateforme :

\begin{itemize}
    \item \textbf{OpenShift CLI (oc)} : Commandes de déploiement et de gestion
    \item \textbf{Templates OpenShift} : Définition des ressources Kubernetes
    \item \textbf{ConfigMaps et Secrets} : Gestion sécurisée de la configuration
    \item \textbf{Persistent Volume Claims} : Gestion du stockage persistant
    \item \textbf{Service Mesh} : Gestion du trafic et de la sécurité inter-services
\end{itemize}

\subsection{Roadmap de finalisation}

La finalisation du projet \projectTitle\ suit une roadmap structurée en milestones avec objectifs hebdomadaires :

\subsubsection{Phase 1 - Déploiement en production (Milestones M7-M8)}

La première phase se concentre sur la mise en production de la solution :

\textbf{Milestone M7 - DevOps Pipeline (4 semaines) :}
\begin{itemize}
    \item \textbf{Semaine 1} : Finalisation de la containerisation Docker et optimisation des images
    \item \textbf{Semaine 2} : Configuration du pipeline GitLab CI/CD avec tests automatisés
    \item \textbf{Semaine 3} : Intégration des health checks et monitoring
    \item \textbf{Semaine 4} : Tests d'intégration et validation du pipeline complet
\end{itemize}

\textbf{Milestone M8 - Production Deployment (4 semaines) :}
\begin{itemize}
    \item \textbf{Semaine 1} : Configuration OpenShift et setup des environnements
    \item \textbf{Semaine 2} : Déploiement en staging et tests de charge
    \item \textbf{Semaine 3} : Migration vers l'environnement de production
    \item \textbf{Semaine 4} : Formation des équipes et documentation opérationnelle
\end{itemize}

\subsubsection{Phase 2 - Intelligence artificielle (Milestones M9-M10)}

La seconde phase introduira les capacités d'analyse prédictive :

\begin{itemize}
\textbf{Milestone M9 - AI Integration (4 semaines) :}
\begin{itemize}
    \item \textbf{Semaine 1} : Collecte et préparation des données historiques pour l'entraînement
    \item \textbf{Semaine 2} : Implémentation de l'algorithme CatBoost pour la prédiction de capacité
    \item \textbf{Semaine 3} : Développement des API de prédiction et interfaces utilisateur
    \item \textbf{Semaine 4} : Tests et validation des modèles prédictifs
\end{itemize}

\textbf{Milestone M10 - Project Finalization (4 semaines) :}
\begin{itemize}
    \item \textbf{Semaine 1} : Tests de performance et optimisation globale
    \item \textbf{Semaine 2} : Documentation technique et guides utilisateur
    \item \textbf{Semaine 3} : Formation finale et transfert de connaissances
    \item \textbf{Semaine 4} : Livraison finale et clôture du projet
\end{itemize}

\subsubsection{Fonctionnalités d'intelligence artificielle}

L'intégration CatBoost apportera les capacités suivantes :

\begin{itemize}
    \item \textbf{Algorithme CatBoost} : Modèle de gradient boosting pour la prédiction de capacité
    \item \textbf{Analyse des tendances} : Identification des patterns d'utilisation
    \item \textbf{Prédiction de surcharges} : Anticipation des pics de charge
    \item \textbf{Recommandations automatiques} : Suggestions d'optimisation et de provisioning
    \item \textbf{Détection d'anomalies} : Identification proactive des comportements inhabituels
\end{itemize}

\clearpage
