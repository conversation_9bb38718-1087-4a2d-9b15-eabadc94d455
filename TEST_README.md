# 🧪 CloudCapacity Test Suite

Complete test suite for CloudCapacity application covering backend API endpoints and frontend components with comprehensive export functionality testing.

## 🚀 Quick Start

### Run All Tests
```bash
# Backend Tests
cd be_cloudcapacity && python run_tests.py --all --coverage

# Frontend Tests  
cd fe_cloudcapacity && node run-tests.js --all --coverage
```

## 📁 Test Files Overview

### Backend Tests (`be_cloudcapacity/tests/`)
- **`test_reporting_capacity.py`** - Capacity reporting and PPTX export tests
- **`test_server_info.py`** - Server CRUD operations and database tests
- **`conftest.py`** - Test configuration and fixtures
- **`run_tests.py`** - Test runner script

### Frontend Tests (`fe_cloudcapacity/src/app/`)
- **`reporting-capacity.component.spec.ts`** - Main component tests
- **`reporting-capacity-export-dialog.component.spec.ts`** - Export dialog tests
- **`resource-usage/export-dialog.component.spec.ts`** - Resource usage export tests
- **`testing/test-utils.ts`** - Test utilities and helpers
- **`run-tests.js`** - Test runner script

## 🎯 Test Coverage

### Backend Coverage
- ✅ **API Endpoints**: All capacity and server endpoints
- ✅ **PPTX Generation**: PowerPoint export functionality
- ✅ **Database Operations**: CRUD with SQLAlchemy
- ✅ **Error Handling**: 400, 404, 500 scenarios
- ✅ **Data Validation**: Pydantic model testing

### Frontend Coverage
- ✅ **Component Logic**: Initialization and data handling
- ✅ **Export Functionality**: PNG, PDF, PPTX exports
- ✅ **User Interactions**: Clicks, selections, form inputs
- ✅ **Service Integration**: API service mocking
- ✅ **Dialog Management**: Export dialog lifecycle

## 📊 Export Functionality Tests

### Backend Export Tests
```bash
# PPTX generation
python run_tests.py --test tests/test_reporting_capacity.py::TestReportingCapacityEndpoints::test_generate_pptx_report_success

# Capacity report generation
python run_tests.py --test tests/test_reporting_capacity.py::TestReportingCapacityEndpoints::test_generate_capacity_report_success
```

### Frontend Export Tests
```bash
# Export dialog functionality
node run-tests.js --test "**/reporting-capacity-export-dialog*.spec.ts"

# Multi-format export testing
node run-tests.js --test "**/export*.spec.ts"
```

## 🔧 Test Features

### Comprehensive Mocking
- **Backend**: Database sessions, external APIs
- **Frontend**: Services, HTTP clients, Canvas operations
- **Export Libraries**: html2canvas, jsPDF, python-pptx

### Error Scenario Testing
- **Network failures**
- **Invalid data inputs**
- **Missing dependencies**
- **Export generation failures**

### Performance Testing
- **Large dataset handling**
- **Multi-server exports**
- **Memory usage validation**

## 📈 Coverage Reports

### View Coverage
```bash
# Backend Coverage
cd be_cloudcapacity
python run_tests.py --coverage
# Open: htmlcov/index.html

# Frontend Coverage
cd fe_cloudcapacity
node run-tests.js --coverage
# Open: coverage/index.html
```

### Coverage Targets
- **Backend**: >85% overall, >90% API endpoints
- **Frontend**: >85% components, >90% services

## 🛠️ Test Utilities

### Backend Utilities (`conftest.py`)
- **Database fixtures**: Test database setup
- **Mock data factories**: Server and report data
- **Session management**: Test isolation

### Frontend Utilities (`test-utils.ts`)
- **Component helpers**: Element selection and interaction
- **Mock factories**: Server, usage data, charts
- **Service mocks**: API, translation, dialog services
- **DOM utilities**: Canvas, blob, download mocking

## 🚨 Troubleshooting

### Common Issues
```bash
# Backend: Virtual environment not activated
cd be_cloudcapacity && .\venv\Scripts\activate

# Frontend: Node modules issues
cd fe_cloudcapacity && npm install

# Clean test artifacts
python run_tests.py --clean  # Backend
node run-tests.js --clean    # Frontend
```

### Debug Individual Tests
```bash
# Backend verbose output
python run_tests.py --test tests/test_reporting_capacity.py --verbose

# Frontend specific test
node run-tests.js --test "**/reporting-capacity.component.spec.ts"
```

## 📋 Test Commands Reference

### Backend Commands
| Command | Description |
|---------|-------------|
| `--all` | Run all tests with linting |
| `--unit` | Unit tests only |
| `--coverage` | Generate coverage report |
| `--lint` | Code linting (flake8, black) |
| `--clean` | Clean test artifacts |

### Frontend Commands
| Command | Description |
|---------|-------------|
| `--all` | Run lint + unit + build tests |
| `--unit` | Unit tests only |
| `--coverage` | Generate coverage report |
| `--lint` | ESLint checking |
| `--build` | Production build test |

## 📚 Documentation

- **[TEST_EXECUTION_GUIDE.md](TEST_EXECUTION_GUIDE.md)** - Detailed execution instructions
- **[TESTING.md](TESTING.md)** - Comprehensive testing documentation
- **Individual test files** - Specific test case documentation

## 🎯 Next Steps

1. **Run the tests** using the commands above
2. **Check coverage reports** to identify gaps
3. **Add new tests** for additional features
4. **Integrate with CI/CD** for automated testing

---

**Ready to test?** Start with the [TEST_EXECUTION_GUIDE.md](TEST_EXECUTION_GUIDE.md) for step-by-step instructions!
