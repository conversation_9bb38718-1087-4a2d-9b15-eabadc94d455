{"name": "fe-cloudcapacity-scripts", "version": "1.0.0", "description": "UI/UX Quality Improvement Scripts for Cloud Capacity Frontend", "scripts": {"screenshots": "node screenshot.js", "analyze": "node analyzer.js", "element-analyze": "node element-analyzer.js", "purge": "node purge.js", "cycle": "node run-ui-ux-cycle.js", "cycle:fix": "node run-ui-ux-cycle.js --fix"}, "dependencies": {"chalk": "^4.1.2", "pixelmatch": "^5.3.0", "pngjs": "^6.0.0", "puppeteer": "^19.7.2"}}