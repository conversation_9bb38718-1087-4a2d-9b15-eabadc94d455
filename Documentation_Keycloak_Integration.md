# Documentation - Intégration Keycloak

## Vue d'ensemble

L'application CloudCapacity utilise Keycloak pour l'authentification et l'autorisation des utilisateurs. Cette documentation explique comment Keycloak est intégré, configuré et utilisé dans l'application.

## Bibliothèques Utilisées

### Frontend (Angular)
- **keycloak-angular** - Bibliothèque d'intégration Keycloak pour Angular
- **keycloak-js** - Client JavaScript Keycloak (dépendance de keycloak-angular)
- **Angular Router** - Gestion des routes protégées
- **Angular HTTP Client** - Intercepteurs pour les tokens

### Backend (FastAPI)
- **Aucune bibliothèque Keycloak** - Le backend n'utilise pas d'authentification
- **CORS** - Configuration pour accepter les requêtes cross-origin
- **FastAPI** - API REST sans authentification requise

## Configuration Keycloak

### Paramètres d'Environnement
**Développement** (`environment.ts`) :
- **URL Keycloak** : `https://keycloak-route-enterprise-search.apps.okd-poc.soprahronline.sopra`
- **Realm** : `testme`
- **Client ID** : `testangular`

**Production** (`environment.prod.ts`) :
- **URL Keycloak** : Même serveur que développement
- **Configuration** : Identique pour cohérence

### Initialisation Keycloak
**Fonction `initializeKeycloak()`** :
1. **Configuration de base** avec URL, realm et client ID
2. **Options d'initialisation** :
   - `onLoad: 'check-sso'` - Vérification SSO sans forcer la connexion
   - `checkLoginIframe: false` - Désactivation iframe pour performance
   - `pkceMethod: 'S256'` - Sécurité PKCE pour OAuth2
3. **Gestion des erreurs** - Continuation en mode test si échec
4. **Chargement du profil utilisateur** si authentifié

## Architecture d'Authentification

### Service d'Authentification (`AuthService`)
**Fonctionnalités principales** :
- **Mode de test** - Fonctionnement sans Keycloak pour développement
- **Gestion des sessions** - Vérification de l'état d'authentification
- **Profil utilisateur** - Récupération et stockage des informations
- **Rôles et permissions** - Gestion des autorisations utilisateur

**Méthodes clés** :
- `isAuthenticated()` - Vérification de l'authentification
- `login()` - Déclenchement de la connexion Keycloak
- `logout()` - Déconnexion et nettoyage de session
- `getUserProfile()` - Récupération des données utilisateur
- `getUserRoles()` - Obtention des rôles assignés

### Garde d'Authentification (`AuthGuard`)
**Hérite de** : `KeycloakAuthGuard`

**Logique de protection** :
1. **Mode test** - Accès autorisé automatiquement
2. **Vérification d'authentification** - Redirection vers landing si non connecté
3. **Contrôle des rôles** - Vérification des permissions requises
4. **Autorisation d'accès** - Validation finale pour la route

### Intercepteur HTTP (`keycloakInterceptor`)
**Fonctionnalité** : Ajout automatique du token Bearer aux requêtes HTTP

**Traitement** :
1. **Exclusion d'URLs** - Pas de token pour `/assets/`
2. **Récupération du token** - Depuis le service Keycloak
3. **Ajout de l'en-tête** - `Authorization: Bearer <token>`
4. **Transmission** - Envoi de la requête modifiée

## Flux d'Authentification

### 1. Démarrage de l'Application
- Initialisation de Keycloak au lancement
- Vérification du statut SSO automatique
- Chargement du profil si utilisateur connecté
- Redirection vers dashboard si authentifié

### 2. Page de Connexion (Landing)
- **Vérification préalable** - Redirection si déjà connecté
- **Bouton Keycloak** - Déclenchement de l'authentification
- **Mode test** - Option de développement sans Keycloak
- **Gestion d'erreurs** - Messages en cas d'échec

### 3. Protection des Routes
- **Garde sur toutes les routes** - Sauf landing et publiques
- **Vérification des rôles** - Selon les exigences de la route
- **Redirection automatique** - Vers landing si non autorisé

### 4. Gestion des Tokens
- **Récupération automatique** - Via l'intercepteur HTTP
- **Rafraîchissement** - Gestion automatique par keycloak-angular
- **Expiration** - Redirection vers connexion si token expiré

## Mode Test (Développement)

### Activation du Mode Test
- **Méthode** : `setTestingMode(true)` dans AuthService
- **Persistance** : Stockage dans localStorage
- **Utilisateur fictif** : Profil de test prédéfini

### Utilisateur de Test
```
{
  username: 'test-user',
  email: '<EMAIL>',
  firstName: 'Test',
  lastName: 'User',
  roles: ['user', 'admin']
}
```

### Avantages du Mode Test
- **Développement local** - Pas besoin de connexion Keycloak
- **Tests automatisés** - Environnement contrôlé
- **Débogage** - Isolation des problèmes d'authentification

## Configuration Backend

### Absence d'Authentification Backend
- **CORS ouvert** - Accepte toutes les origines (`allow_origins=["*"]`)
- **Pas de validation de token** - Endpoints publics
- **Sécurité** - Repose entièrement sur le frontend

### Justification de l'Architecture
- **Simplicité** - Réduction de la complexité backend
- **Performance** - Pas de validation de token à chaque requête
- **Flexibilité** - Facilite les tests et le développement

## Gestion des Erreurs

### Erreurs d'Initialisation
- **Connexion Keycloak échouée** - Continuation en mode test
- **Configuration invalide** - Logging et fallback
- **Réseau indisponible** - Mode dégradé automatique

### Erreurs d'Authentification
- **Token expiré** - Redirection vers connexion
- **Permissions insuffisantes** - Message d'erreur approprié
- **Session invalide** - Nettoyage et reconnexion

### Logging et Débogage
- **Console détaillée** - Informations de débogage
- **États d'authentification** - Suivi des transitions
- **Profil utilisateur** - Vérification des données chargées

## Sécurité

### Bonnes Pratiques Implémentées
- **PKCE** - Protection contre les attaques d'interception
- **HTTPS** - Communication sécurisée avec Keycloak
- **Token Bearer** - Transmission sécurisée des autorisations
- **Validation côté client** - Vérification des permissions

### Considérations de Sécurité
- **Backend ouvert** - Tous les endpoints sont publics
- **Confiance frontend** - Sécurité repose sur l'authentification client
- **Réseau interne** - Supposé être dans un environnement sécurisé

## Configuration des Routes

### Routes Protégées
- **Dashboard** - Accès authentifié requis
- **Server List** - Authentification nécessaire
- **Resource Usage** - Protection par garde
- **Reporting** - Accès contrôlé

### Routes Publiques
- **Landing** - Page de connexion accessible
- **Assets** - Ressources statiques libres

## Maintenance et Évolution

### Mise à Jour Keycloak
- **Compatibilité** - Vérification avec keycloak-angular
- **Configuration** - Adaptation des paramètres si nécessaire
- **Tests** - Validation du flux d'authentification

### Ajout de Nouveaux Rôles
- **Configuration Keycloak** - Création des rôles côté serveur
- **Mise à jour des gardes** - Ajout des vérifications nécessaires
- **Interface utilisateur** - Adaptation selon les permissions

Cette architecture d'authentification offre une intégration robuste avec Keycloak tout en maintenant la flexibilité pour le développement et les tests, avec un système de fallback en mode test pour assurer la continuité du développement.
