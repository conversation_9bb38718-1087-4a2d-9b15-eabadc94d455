{"name": "fe-cloudcapacity-ui-testing", "version": "1.0.0", "description": "UI Testing and Analysis Scripts for Cloud Capacity Frontend", "private": true, "scripts": {"screenshot": "node screenshot.js", "screenshot:full": "node screenshot.js --mode=full", "screenshot:components": "node screenshot.js --mode=components", "screenshot:elements": "node screenshot.js --mode=elements", "analyze": "node screenshot-analyzer.js", "analyze:fix": "node screenshot-analyzer.js --fix", "analyze:loop": "node screenshot-analyzer.js --fix --loop", "test-elements": "node element-analyzer.js", "test-elements:fix": "node element-analyzer.js --fix", "test-elements:loop": "node element-analyzer.js --fix --loop", "purge": "node purge.js", "purge:force": "node purge.js --force", "purge:keep-recent": "node purge.js --keep=3", "full-test": "npm run screenshot && npm run analyze:fix && npm run test-elements:fix"}, "dependencies": {"fs-extra": "^11.3.0", "inquirer": "^8.2.6", "path": "^0.12.7", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "puppeteer": "^20.9.0", "yargs": "^17.7.2"}, "engines": {"node": ">=16.0.0"}}