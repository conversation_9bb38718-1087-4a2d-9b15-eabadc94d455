%========= Chapitre 2: Étude de l'existant et analyse des besoins =========%
%                                                                          %
% Chapter 2: Existing system analysis and requirements                    %
%==========================================================================%

\chapter{Étude de l'existant et analyse des besoins}

\section{Analyse de l'infrastructure existante}

\subsection{Architecture serveurs on-premise}

L'infrastructure on-premise de \companyName\ est composée de plus de 400 serveurs physiques et virtuels répartis dans différents datacenters. Ces serveurs hébergent diverses applications et services critiques pour l'entreprise et ses clients, incluant :

\begin{itemize}
    \item Serveurs d'applications métier RH
    \item Serveurs de bases de données Oracle
    \item Serveurs web et de présentation
    \item Serveurs de traitement batch
    \item Serveurs de développement et de test
\end{itemize}

Cette infrastructure hétérogène nécessite un suivi de capacité constant pour garantir les performances et la disponibilité des services.

\subsection{Outils de collecte de métriques actuels (Zabbix)}

La collecte de métriques de capacité de ces serveurs est actuellement assurée par Zabbix, qui collecte en temps réel les métriques de performance telles que :

\begin{itemize}
    \item Utilisation CPU (moyenne, pics)
    \item Utilisation mémoire et swap
    \item Espace disque disponible
    \item Bande passante réseau
    \item Temps de réponse des applications
    \item Latence réseau
\end{itemize}

Les informations relatives aux serveurs et à leurs configurations sont stockées dans une base CMDB nommée Maia, qui centralise les données de configuration de l'infrastructure.

\section{Analyse critique et limites}

L'analyse de l'existant révèle plusieurs limitations dans les processus actuels de gestion de la capacité :

\begin{itemize}
    \item \textbf{Processus manuels chronophages} : La consultation des métriques Zabbix et l'extraction de données vers des fichiers Excel nécessitent des interventions manuelles régulières
    \item \textbf{Absence de centralisation} : Les données de performance sont dispersées entre différents outils sans vue d'ensemble cohérente
    \item \textbf{Reporting manuel} : La création de rapports mensuels de capacité est entièrement manuelle et sujette aux erreurs
    \item \textbf{Réactivité limitée} : Le traitement des incidents de capacité se fait de manière réactive plutôt que proactive
    \item \textbf{Manque de prédictibilité} : Absence d'outils permettant d'anticiper les besoins futurs en ressources
    \item \textbf{Difficultés de corrélation} : Complexité pour corréler les tendances de consommation avec les incidents
\end{itemize}

\section{Spécification des besoins}

\subsection{Besoins fonctionnels}

L'analyse des besoins métier a permis d'identifier les exigences fonctionnelles suivantes :

\begin{table}[H]
\centering
\begin{tabular}{|p{4cm}|p{8cm}|p{2cm}|}
\hline
\textbf{Besoin} & \textbf{Description} & \textbf{Priorité} \\
\hline
Collecte automatisée & Récupération automatique des métriques depuis Zabbix & Haute \\
\hline
Dashboard interactif & Interface de visualisation des métriques en temps réel & Haute \\
\hline
Comparaison d'usage & Outils de comparaison entre serveurs et périodes & Haute \\
\hline
Système d'alertes & Alertes configurables basées sur des seuils & Haute \\
\hline
Reporting automatique & Génération automatique de rapports PDF/Excel & Moyenne \\
\hline
Analyse prédictive & Prédiction des surcharges futures & Moyenne \\
\hline
Intégration Jira & Création automatique de tickets d'incidents & Faible \\
\hline
\end{tabular}
\caption{Besoins fonctionnels identifiés}
\label{tab:besoins-fonctionnels}
\end{table}

\subsection{Besoins non fonctionnels}

Les exigences non fonctionnelles incluent :

\begin{table}[H]
\centering
\begin{tabular}{|p{4cm}|p{8cm}|p{2cm}|}
\hline
\textbf{Critère} & \textbf{Exigence} & \textbf{Mesure} \\
\hline
Performance & Temps de réponse rapide pour l'interface utilisateur & < 2 secondes \\
\hline
Scalabilité & Support de l'ajout de nouveaux serveurs & 100+ serveurs \\
\hline
Disponibilité & Haute disponibilité du système & 99.9\% \\
\hline
Sécurité & Protection des données sensibles & Chiffrement HTTPS \\
\hline
Maintenabilité & Code modulaire et documenté & Standards de développement \\
\hline
Intégration & Compatibilité avec l'infrastructure existante & API Zabbix \\
\hline
\end{tabular}
\caption{Besoins non fonctionnels}
\label{tab:besoins-non-fonctionnels}
\end{table}

\clearpage
