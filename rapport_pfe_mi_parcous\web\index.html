<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport PFE CloudCapacity - Index</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1a1d29 0%, #252837 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .content {
            padding: 30px;
        }

        .card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #6366f1;
        }

        .card h3 {
            margin-top: 0;
            color: #1a1d29;
        }

        .btn {
            display: inline-block;
            background: #6366f1;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 10px 10px 0;
            transition: background 0.3s;
        }

        .btn:hover {
            background: #5855eb;
        }

        .btn-secondary {
            background: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status-complete {
            background: #d4edda;
            color: #155724;
        }

        .status-progress {
            background: #fff3cd;
            color: #856404;
        }

        .file-list {
            list-style: none;
            padding: 0;
        }

        .file-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .file-list li:last-child {
            border-bottom: none;
        }

        .file-icon {
            margin-right: 10px;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Rapport PFE CloudCapacity</h1>
            <p>Plateforme Web de Monitoring et d'Analyse de la Capacité des Serveurs</p>
            <p><strong>MAHMOUD Mohamed Amine</strong> - Année Universitaire 2024-2025</p>
        </div>

        <div class="content">
            <div class="card">
                <h3>📄 Document Principal</h3>
                <p>Rapport complet de mi-parcours du projet CloudCapacity conforme aux standards académiques tunisiens.
                </p>
                <a href="rapport_latex.html" class="btn">Ouvrir le Rapport</a>
                <span class="status status-complete">✓ Complété</span>
            </div>

            <div class="card">
                <h3>📊 État d'Avancement</h3>
                <p><strong>Taux de réalisation global : 85%</strong></p>
                <ul>
                    <li>✅ Architecture technique : 100%</li>
                    <li>✅ Fonctionnalités core : 100%</li>
                    <li>✅ Intégration Zabbix : 100%</li>
                    <li>⚠️ Reporting avancé : 80%</li>
                    <li>📋 Machine Learning : 0% (planifié)</li>
                </ul>
            </div>

            <div class="card">
                <h3>🗂️ Structure des Fichiers</h3>
                <ul class="file-list">
                    <li><span class="file-icon">📄</span> rapport_latex.html - Document principal</li>
                    <li><span class="file-icon">🎨</span> styles/rapport-styles.css - Feuilles de style</li>
                    <li><span class="file-icon">📊</span> diagrams/ - Diagrammes Mermaid (4 fichiers)</li>
                    <li><span class="file-icon">🔧</span> scripts/diagram-loader.js - Script de chargement</li>
                    <li><span class="file-icon">📖</span> README.md - Documentation</li>
                </ul>
            </div>

            <div class="card">
                <h3>🛠️ Technologies Utilisées</h3>
                <p><strong>Frontend :</strong> Angular 19, Angular Material, Chart.js, TypeScript</p>
                <p><strong>Backend :</strong> FastAPI, SQLModel, PyZabbix, Oracle Database</p>
                <p><strong>Outils :</strong> Mermaid, SCSS, Git</p>
            </div>

            <div class="card">
                <h3>📋 Actions Rapides</h3>
                <a href="rapport_latex.html" class="btn">Lire le Rapport</a>
                <a href="README.md" class="btn btn-secondary">Documentation</a>
                <a href="diagrams/" class="btn btn-secondary">Voir les Diagrammes</a>
            </div>

            <div class="card">
                <h3>👥 Équipe Projet</h3>
                <p><strong>Étudiant :</strong> MAHMOUD Mohamed Amine</p>
                <p><strong>Encadrant Académique :</strong> M. MEZGHANNI Dhafer (Université Sésame)</p>
                <p><strong>Encadrants Professionnels :</strong> M. DELIL Yassine (Manager DevOps), M. MAHMOUD Hatem
                    (Senior Technical Lead) - Sopra HR Software</p>
                <p><strong>Période :</strong> Rapport de Mi-Parcours - Décembre 2024</p>
            </div>
        </div>
    </div>
</body>

</html>