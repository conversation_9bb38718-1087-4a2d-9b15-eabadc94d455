#!/usr/bin/env python3
"""
<PERSON><PERSON>t to verify that all required images are present for LaTeX compilation
"""

import os
from pathlib import Path

def verify_images():
    """Verify that all required images exist"""
    print("=== Image Verification for LaTeX Document ===\n")
    
    script_dir = Path(__file__).parent
    images_dir = script_dir / 'images'
    
    required_images = [
        'architecture-generale.png',
        'cas-utilisation.png',
        'sequence-collecte-metriques.png',
        'modele-donnees.png'
    ]
    
    print(f"Checking images directory: {images_dir}")
    
    if not images_dir.exists():
        print("✗ Images directory does not exist!")
        print("Please run the diagram conversion script first:")
        print("  python convert_diagrams.py")
        print("  OR")
        print("  convert_diagrams.bat (Windows)")
        print("  OR")
        print("  ./convert_diagrams.sh (Unix/Linux/macOS)")
        return False
    
    missing_images = []
    present_images = []
    
    for image in required_images:
        image_path = images_dir / image
        if image_path.exists():
            file_size = image_path.stat().st_size
            print(f"✓ {image} ({file_size:,} bytes)")
            present_images.append(image)
        else:
            print(f"✗ {image} - MISSING")
            missing_images.append(image)
    
    print(f"\n=== Summary ===")
    print(f"Present: {len(present_images)}/{len(required_images)} images")
    
    if missing_images:
        print(f"Missing images: {', '.join(missing_images)}")
        print("\nTo generate missing images, run:")
        print("  python convert_diagrams.py")
        return False
    else:
        print("✓ All required images are present!")
        print("\nYour LaTeX document is ready to compile.")
        print("\nTo compile the document:")
        print("  pdflatex rapport_cloudcapacity.tex")
        print("  biber rapport_cloudcapacity")
        print("  pdflatex rapport_cloudcapacity.tex")
        print("  pdflatex rapport_cloudcapacity.tex")
        print("\nOr use latexmk:")
        print("  latexmk -pdf -biber rapport_cloudcapacity.tex")
        return True

if __name__ == "__main__":
    success = verify_images()
    exit(0 if success else 1)
