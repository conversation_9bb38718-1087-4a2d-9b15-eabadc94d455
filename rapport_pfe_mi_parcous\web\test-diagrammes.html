<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Diagrammes Mermaid - CloudCapacity</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
        }
        .mermaid {
            margin: 20px 0;
            text-align: center;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Test des Diagrammes Mermaid - Rapport PFE CloudCapacity</h1>
    
    <div class="test-container">
        <h2 class="test-title">1. Architecture Générale</h2>
        <div id="test-architecture" class="mermaid">
            graph TD
                A[Client Web - Angular 19] --> B[API Gateway]
                B --> C[Backend FastAPI]
                C --> D[Base de données Oracle]
                C --> E[API Zabbix]
                
                style A fill:#f9f,stroke:#333,stroke-width:2px
                style B fill:#bbf,stroke:#333,stroke-width:2px
                style C fill:#dfd,stroke:#333,stroke-width:2px
                style D fill:#fdd,stroke:#333,stroke-width:2px
                style E fill:#ddd,stroke:#333,stroke-width:2px
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">2. Cas d'Utilisation</h2>
        <div id="test-cas-utilisation" class="mermaid">
            graph TD
                subgraph "Acteurs"
                    Admin[Administrateur Système]
                    User[Utilisateur]
                end
                
                subgraph "Fonctionnalités"
                    UC1[Consulter dashboard]
                    UC2[Gérer serveurs]
                    UC3[Comparer usage]
                end
                
                Admin --> UC1
                Admin --> UC2
                Admin --> UC3
                User --> UC1
                User --> UC3
                
                style Admin fill:#ffcccc
                style User fill:#ccffcc
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">3. Séquence de Collecte</h2>
        <div id="test-sequence" class="mermaid">
            sequenceDiagram
                participant F as Frontend
                participant B as Backend
                participant Z as Zabbix
                
                F->>B: GET /capacity/get-servers
                B->>Z: Connexion API
                Z-->>B: Données métriques
                B-->>F: Serveurs + métriques
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">4. Modèle de Données</h2>
        <div id="test-modele" class="mermaid">
            erDiagram
                Server {
                    int id PK
                    string hostname
                    string server_type
                    int total_cpus
                }
                
                UsageDataPoint {
                    int id PK
                    string hostname FK
                    datetime timestamp
                    float cpu_usage
                }
                
                Server ||--o{ UsageDataPoint : "génère"
        </div>
    </div>

    <div id="test-results">
        <h2>Résultats des Tests</h2>
        <div id="status-container"></div>
    </div>

    <script>
        // Configuration Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#0066cc',
                primaryTextColor: '#333',
                primaryBorderColor: '#0066cc',
                lineColor: '#666',
                secondaryColor: '#f9f9f9',
                tertiaryColor: '#fff'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true,
                mirrorActors: true
            },
            er: {
                useMaxWidth: true
            }
        });

        // Test de fonctionnement
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const statusContainer = document.getElementById('status-container');
                const diagramElements = document.querySelectorAll('.mermaid');
                let successCount = 0;
                let totalCount = diagramElements.length;

                diagramElements.forEach((element, index) => {
                    const svg = element.querySelector('svg');
                    if (svg && svg.children.length > 0) {
                        successCount++;
                    }
                });

                if (successCount === totalCount) {
                    statusContainer.innerHTML = `
                        <div class="status status-success">
                            ✅ Tous les diagrammes se chargent correctement (${successCount}/${totalCount})
                            <br>Les diagrammes Mermaid fonctionnent parfaitement dans le rapport PFE.
                        </div>
                    `;
                } else {
                    statusContainer.innerHTML = `
                        <div class="status status-error">
                            ❌ Problème détecté : ${successCount}/${totalCount} diagrammes chargés
                            <br>Vérifiez la configuration Mermaid et la syntaxe des diagrammes.
                        </div>
                    `;
                }
            }, 2000);
        });
    </script>
</body>
</html>
