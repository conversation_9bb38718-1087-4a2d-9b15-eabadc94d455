{"name": "cloud-capacity", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:docker": "ng build --configuration=docker", "build:debug": "ng build --configuration=debug", "build:production": "ng build --configuration=production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.0", "@angular/cdk": "^19.2.0", "@angular/common": "^19.2.0", "@angular/compiler": "^19.2.0", "@angular/core": "^19.2.0", "@angular/forms": "^19.2.0", "@angular/material": "^19.2.0", "@angular/platform-browser": "^19.2.0", "@angular/platform-browser-dynamic": "^19.2.0", "@angular/router": "^19.2.0", "@angular/service-worker": "^19.2.10", "@types/xlsx": "^0.0.35", "chart.js": "^4.4.9", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "keycloak-angular": "^15.3.0", "keycloak-js": "^24.0.5", "rxjs": "~7.8.0", "three": "^0.176.0", "tslib": "^2.3.0", "web-vitals": "^5.0.0", "xlsx": "^0.18.5", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.0", "@angular/cli": "^19.2.0", "@angular/compiler-cli": "^19.2.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "~5.1.0", "@types/three": "^0.176.0", "axios": "^1.9.0", "chalk": "^5.4.1", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.2"}}