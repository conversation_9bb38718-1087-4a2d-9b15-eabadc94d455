# CloudCapacity Project Architecture Document

## Table of Contents
- [1. System Overview](#1-system-overview)
- [2. Backend Architecture](#2-backend-architecture)
  - [2.1 Technology Stack](#21-technology-stack)
  - [2.2 Core Components](#22-core-components)
  - [2.3 Key Data Models](#23-key-data-models)
  - [2.4 API Endpoints](#24-api-endpoints)
- [3. Frontend Architecture](#3-frontend-architecture)
  - [3.1 Technology Stack](#31-technology-stack)
  - [3.2 Core Components](#32-core-components)
  - [3.3 Services](#33-services)
- [4. Interaction Design](#4-interaction-design)
  - [4.1 Frontend-Backend Communication](#41-frontend-backend-communication)
  - [4.2 Authentication Flow](#42-authentication-flow)
- [5. Features and Use Cases](#5-features-and-use-cases)
  - [5.1 Dashboard](#51-dashboard)
  - [5.2 Server List](#52-server-list)
  - [5.3 Server Details](#53-server-details)
  - [5.4 Usage Comparison](#54-usage-comparison)
  - [5.5 Threshold Alerts](#55-threshold-alerts)
  - [5.6 Threshold Settings](#56-threshold-settings)
- [6. Deployment Architecture](#6-deployment-architecture)
  - [6.1 Backend Deployment](#61-backend-deployment)
  - [6.2 Frontend Deployment](#62-frontend-deployment)
- [7. Future Enhancements](#7-future-enhancements)
  - [7.1 Backend Enhancements](#71-backend-enhancements)
  - [7.2 Frontend Enhancements](#72-frontend-enhancements)

## 1. System Overview

The CloudCapacity project is a comprehensive solution designed for monitoring and analyzing server capacity metrics. It consists of two main components working together:

1. **Backend (be_cloudcapacity)**: A FastAPI-based API that integrates with Zabbix to retrieve server metrics and stores them in an Oracle database.

2. **Frontend (fe_cloudcapacity)**: An Angular 19 application with a distinctive modern dark dashboard theme that provides an intuitive user interface for visualizing and analyzing server metrics.

## 2. Backend Architecture

### 2.1 Technology Stack

- **Framework**: FastAPI (Python 3.11+)
- **Database**: SQLModel (SQLAlchemy ORM)
- **Database Type**: Oracle
- **API Integration**: Zabbix API for server metrics
- **Authentication**: Basic authentication (username/password)
- **Deployment**: Docker support
- **Testing**: Tavern for API testing

### 2.2 Core Components

1. **API Layer (FastAPI)**
   - Provides RESTful endpoints for retrieving server metrics
   - Handles authentication and request validation
   - Implements CORS for cross-origin requests
   - Exposes endpoints under the `/capacity` prefix

2. **Database Layer (SQLModel/SQLAlchemy)**
   - Defines data models for servers and metrics
   - Handles database connections and transactions
   - Provides ORM functionality for database operations
   - Manages database connections and transactions

3. **Zabbix Integration**
   - Connects to Zabbix API using provided credentials
   - Retrieves server information and metrics
   - Handles authentication with Zabbix
   - Processes and transforms Zabbix data for storage and retrieval

### 2.3 Key Data Models

- **Server**: Represents a physical or virtual server with static metrics (hostname, server type, total CPUs, total RAM, total swap)
- **UsageDataPoint**: Represents a single data point for server usage metrics (CPU, memory, swap usage)
- **MonthlyData**: Contains aggregated usage data for a specific month (CPU, memory, swap)
- **ThresholdType**: Enumeration for different types of thresholds (CPU, memory, swap)

### 2.4 API Endpoints

1. **Server Management**
   - `POST /capacity/return-and-sync-hostnames-and-metrics`: Synchronizes server information from Zabbix to the local database

2. **Server Usage Data**
   - `POST /capacity/server-usage-per-month`: Retrieves usage data for a specific server by month
   - `POST /capacity/all-servers-usage-per-selected-month`: Retrieves usage data for all servers for a specific month

3. **Server Comparison**
   - `POST /capacity/server-usage-multi-comparison`: Compares server usage between different time periods or servers

4. **Threshold Management**
   - `GET /capacity/thresholds`: Retrieves threshold settings
   - `POST /capacity/update-threshold`: Updates threshold settings

5. **Server Monitoring**
   - `GET /capacity/monthly-servers-usage-averages`: Retrieves average usage data for all servers for a specific month

## 3. Frontend Architecture

### 3.1 Technology Stack

- **Framework**: Angular 19 (standalone components)
- **UI Components**: Angular Material with custom Modern Dark Dashboard theme
- **Styling**: SCSS with Modern Dark Dashboard styling
- **Charts**: Chart.js with ng2-charts integration
- **Authentication**: Keycloak integration for production authentication
- **Node.js Version**: v20.16.0 or later
- **npm Version**: v10.9.0 or later
- **Angular CLI Version**: v19.2.8 or later
- **Internationalization**: Supports English and French languages

### 3.2 Core Components

1. **Dashboard Component**
   - Provides an overview of server metrics
   - Displays total number of servers, average CPU usage, memory usage, and swap usage
   - Shows number of servers exceeding thresholds
   - Uses modern dark dashboard layout for metrics display
   - Includes circular progress indicators for usage metrics
   - Shows server distribution by type (VM vs Physical)
   - Displays server status indicators with color coding
   - Includes month/year selector for historical data viewing
   - Allows filtering servers by type
   - Offers data export functionality

2. **Server List Component**
   - Displays all servers with their metrics
   - Shows hostname, server type, CPU usage, memory usage, swap usage, total CPUs, total RAM, and total swap
   - Provides filtering by hostname, server type, and status
   - Enables sorting by various metrics
   - Provides navigation to server details
   - Includes manual refresh button for immediate data updates
   - Shows last refresh timestamp

3. **Server Detail Component**
   - Displays detailed metrics for a specific server
   - Shows hostname, server type, CPU usage, memory usage, swap usage, total CPUs, total RAM, and total swap
   - Displays historical charts for CPU, memory, and swap usage
   - Provides navigation to usage comparison
   - Allows selecting different time periods for data viewing

4. **Usage Comparison Component**
   - Allows comparing servers or time periods
   - Offers both time-based and server-based comparison
   - Provides options for selecting servers or time periods to compare
   - Displays charts for comparison visualization
   - Shows difference calculation between compared items
   - Includes summary statistics for the comparison
   - Shows percentage differences between compared items

5. **Threshold Alerts Component**
   - Displays servers exceeding thresholds
   - Shows alert details including type, value, and timestamp
   - Allows acknowledging and resolving alerts
   - Provides filtering and sorting of alerts
   - Shows distribution of alert types

6. **Threshold Settings Component**
   - Manages global and per-server threshold values
   - Provides slider controls for easy threshold adjustment
   - Includes visualization of threshold impacts
   - Offers bulk threshold settings for server groups

### 3.3 Services

1. **Data Service**
   - Abstract service defining the interface for data retrieval
   - Implemented by ApiService for production use

2. **API Service**
   - Communicates with the backend API
   - Handles HTTP requests and responses
   - Implements error handling and retry logic
   - Provides real-time data from Zabbix monitoring system

4. **Authentication Service**
   - Handles user authentication via Keycloak
   - Provides methods for login, logout, and user profile retrieval
   - Includes token management and refresh logic

5. **Translation Service**
   - Handles internationalization
   - Provides translation for UI text
   - Supports French locale
   - Manages language switching functionality

6. **Chart Animation Service**
   - Controls animations for data visualization components
   - Provides consistent animation behavior across charts

## 4. Interaction Design

### 4.1 Frontend-Backend Communication

The frontend communicates with the backend through RESTful API calls. The main interaction points include:

#### Server List and Dashboard

1. **Fetching Server List**
   - Frontend calls `POST /capacity/return-and-sync-hostnames-and-metrics` to get all servers
   - Backend connects to Zabbix, retrieves server information, stores it in the database
   - Backend returns the list of servers with their static metrics
   - Frontend displays the server list and calculates summary statistics

2. **Dashboard Metrics**
   - Frontend calls `GET /capacity/monthly-servers-usage-averages` with year and month parameters
   - Backend retrieves and aggregates server metrics for the specified period
   - Frontend displays the metrics in dashboard charts and statistics cards

#### Server Details

1. **Server Usage Data**
   - When a user selects a server, frontend calls `POST /capacity/server-usage-per-month` with server hostname, year, and month
   - Backend retrieves detailed usage data for the specified server and time period
   - Frontend displays the detailed metrics in line charts and statistics

#### Usage Comparison

1. **Comparing Servers or Time Periods**
   - Frontend calls `POST /capacity/server-usage-multi-comparison` with comparison parameters
   - Backend retrieves and processes comparative data
   - Frontend displays comparison charts showing differences between servers or time periods

#### Threshold Management

1. **Retrieving Thresholds**
   - Frontend calls `GET /capacity/thresholds`
   - Backend returns current threshold values
   - Frontend displays thresholds in the settings UI

2. **Updating Thresholds**
   - When user changes threshold values, frontend calls `POST /capacity/update-threshold`
   - Backend updates threshold settings
   - Frontend displays success message and updates UI

### 4.2 Authentication Flow

1. **Login Process**
   - User accesses the application
   - Frontend redirects to Keycloak login page
   - User enters credentials
   - Upon successful authentication, Keycloak returns access and refresh tokens
   - Frontend stores tokens and redirects to the dashboard

2. **Authenticated Requests**
   - Frontend includes the access token in the Authorization header of API requests
   - Backend validates the token
   - If valid, backend processes the request and returns data
   - If invalid, backend returns an authentication error

3. **Token Refresh**
   - When access token expires, frontend uses refresh token to obtain a new one
   - If refresh token is invalid or expired, user is redirected to login

4. **Logout**
   - User clicks logout
   - Frontend calls Keycloak logout endpoint
   - Keycloak invalidates the session
   - Frontend redirects to the login page

## 5. Features and Use Cases

### 5.1 Dashboard

**Features:**

- System performance overview with metrics and alerts
- Server distribution by type (physical vs. virtual)
- Status indicators for servers
- Historical data visualization
- Resource usage trends over time
- Alert statistics and quick access to servers in alert state
- Time period selection for historical data viewing

**Use Cases:**

- Monitoring overall system health at a glance
- Identifying trends in resource consumption
- Quickly spotting problematic servers
- Getting a high-level view of the infrastructure
- Generating reports for capacity planning
- Analyzing resource distribution across the infrastructure

### 5.2 Server List

**Features:**

- Comprehensive server inventory with detailed metrics
- Filterable and sortable server inventory
- Multi-criteria filtering (by name, type, status)
- Detailed metrics for each server
- Navigation to server details
- Manual refresh button
- Status indicators for server health

**Use Cases:**

- Finding specific servers by name or characteristics
- Sorting servers by resource usage to identify high consumers
- Getting a comprehensive view of all servers
- Refreshing data to get the latest metrics
- Monitoring server statuses across the infrastructure

### 5.3 Server Details

**Features:**

- In-depth metrics for individual servers
- Historical data visualization with line charts
- Performance trends
- Time period selection for trend analysis
- Resource utilization statistics (CPU, memory, swap)
- Navigation to comparative analysis

**Use Cases:**

- Analyzing a specific server's performance
- Tracking resource usage over time
- Investigating performance issues
- Planning capacity upgrades
- Monitoring the impact of changes

### 5.4 Usage Comparison

**Features:**

- Server-to-server comparison
- Time period comparison for the same server
- Visual representation of differences
- Percentage change calculations
- Multiple comparison types
- Summary statistics

**Use Cases:**

- Comparing performance between similar servers
- Analyzing changes in resource usage over time
- Evaluating the impact of software or hardware changes
- Capacity planning based on comparative analysis
- Identifying performance anomalies

### 5.5 Threshold Alerts

**Features:**

- List of servers exceeding resource thresholds
- Alert categorization by type (CPU, memory, swap)
- Alert management workflow (acknowledge, resolve)
- Historical alert tracking
- Alert statistics and distribution charts
- Filtering and sorting alerts

**Use Cases:**

- Identifying servers with resource constraints
- Prioritizing server maintenance
- Managing and tracking alerts
- Setting appropriate threshold levels
- Proactive capacity management
- Analyzing alert patterns for proactive management

### 5.6 Threshold Settings

**Features:**

- Global threshold configuration
- Per-server threshold customization
- Visual threshold adjustment controls
- Impact visualization for threshold changes
- Bulk threshold management

**Use Cases:**

- Setting appropriate alerting levels
- Customizing thresholds for specific server types
- Fine-tuning monitoring sensitivity
- Implementing organizational policies for resource management
- Adapting thresholds based on historical patterns

## 6. Deployment Architecture

### 6.1 Backend Deployment

- Containerized deployment using Docker
- Oracle database for persistent storage
- Environment-specific configuration through environment variables
- Logging for operational monitoring
- Health checks for container orchestration

### 6.2 Frontend Deployment

- Static file hosting for production builds
- Configuration management for different environments
- CORS configuration for API communication
- Caching strategies for improved performance

## 7. Future Enhancements

### 7.1 Backend Enhancements

- Additional data sources beyond Zabbix
- Advanced analytics for resource prediction
- Multiple database support
- Enhanced security with OAuth/OpenID Connect
- API rate limiting and throttling

### 7.2 Frontend Enhancements

- Mobile-responsive design improvements
- Offline capabilities with Progressive Web App features
- Real-time updates using WebSockets
- Advanced filtering and search capabilities
- Customizable dashboards and reports

---

This document provides a comprehensive overview of the CloudCapacity project architecture, detailing both the backend and frontend components, their interactions, and the features they provide. The system is designed to offer robust server monitoring and capacity planning capabilities through an intuitive and visually appealing interface.
