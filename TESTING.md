# CloudCapacity Testing Guide

This document provides comprehensive information about testing the CloudCapacity application, including both backend and frontend components.

## 📋 Table of Contents

- [Overview](#overview)
- [Backend Testing](#backend-testing)
- [Frontend Testing](#frontend-testing)
- [Test Coverage](#test-coverage)
- [Running Tests](#running-tests)
- [Test Structure](#test-structure)
- [Best Practices](#best-practices)

## 🔍 Overview

The CloudCapacity application includes comprehensive test suites for both backend (Python/FastAPI) and frontend (Angular) components, covering:

- **Unit Tests**: Individual component/function testing
- **Integration Tests**: API endpoint and service integration testing
- **Export Functionality Tests**: Comprehensive testing of PNG, PDF, and PPTX export features
- **UI Component Tests**: Angular component and dialog testing
- **Service Tests**: API service and data service testing

## 🐍 Backend Testing

### Test Structure

```
be_cloudcapacity/
├── tests/
│   ├── conftest.py                    # Test configuration and fixtures
│   ├── requirements-test.txt          # Test dependencies
│   ├── test_reporting_capacity.py     # Reporting capacity endpoint tests
│   └── test_server_info.py           # Server info endpoint tests
└── run_tests.py                      # Test runner script
```

### Test Categories

#### 1. **Reporting Capacity Tests** (`test_reporting_capacity.py`)
- ✅ **Capacity Report Generation**: JSON report creation
- ✅ **PPTX Export**: PowerPoint presentation generation
- ✅ **Server Configuration Retrieval**: Database queries
- ✅ **Data Validation**: Request/response model validation
- ✅ **Error Handling**: Invalid data and missing servers

#### 2. **Server Info Tests** (`test_server_info.py`)
- ✅ **CRUD Operations**: Create, Read, Update, Delete servers
- ✅ **Database Queries**: Server filtering and searching
- ✅ **Model Validation**: Pydantic model testing
- ✅ **Error Scenarios**: Duplicate hostnames, invalid data

### Running Backend Tests

```bash
# Navigate to backend directory
cd be_cloudcapacity

# Install test dependencies
python run_tests.py --install-deps

# Run all tests
python run_tests.py --all

# Run unit tests only
python run_tests.py --unit

# Run with coverage
python run_tests.py --unit --coverage

# Run specific test
python run_tests.py --test tests/test_reporting_capacity.py

# Run linting
python run_tests.py --lint

# Generate coverage report
python run_tests.py --report

# Clean test artifacts
python run_tests.py --clean
```

### Backend Test Dependencies

- **pytest**: Testing framework
- **pytest-asyncio**: Async testing support
- **pytest-cov**: Coverage reporting
- **httpx**: HTTP client testing
- **factory-boy**: Test data factories
- **python-pptx**: PowerPoint testing

## 🅰️ Frontend Testing

### Test Structure

```
fe_cloudcapacity/
├── src/
│   ├── app/
│   │   ├── components/
│   │   │   ├── reporting-capacity/
│   │   │   │   ├── reporting-capacity.component.spec.ts
│   │   │   │   └── reporting-capacity-export-dialog.component.spec.ts
│   │   │   └── resource-usage/
│   │   │       └── export-dialog.component.spec.ts
│   │   └── testing/
│   │       └── test-utils.ts              # Test utilities and helpers
│   ├── test-setup.ts                      # Global test setup
│   └── karma.conf.js                      # Karma configuration
└── run-tests.js                          # Test runner script
```

### Test Categories

#### 1. **Reporting Capacity Component Tests**
- ✅ **Component Initialization**: Data loading and setup
- ✅ **Server Selection**: Multi-select functionality
- ✅ **Filtering and Search**: Server filtering logic
- ✅ **Export Dialog**: Dialog opening and data passing
- ✅ **Error Handling**: API error scenarios

#### 2. **Export Dialog Tests**
- ✅ **Format Selection**: PNG, PDF, PPTX format options
- ✅ **Export Functionality**: Client-side and server-side exports
- ✅ **Progress Tracking**: Export progress indicators
- ✅ **Canvas Generation**: Chart rendering and export
- ✅ **File Download**: Blob creation and download triggers

#### 3. **Resource Usage Export Tests**
- ✅ **Multi-Server Export**: Multiple server selection
- ✅ **Chart Arrangements**: Different layout options
- ✅ **Data Fetching**: Server usage data retrieval
- ✅ **PDF Generation**: Multi-page PDF creation
- ✅ **Grouping Methods**: By server vs by metric

### Running Frontend Tests

```bash
# Navigate to frontend directory
cd fe_cloudcapacity

# Install dependencies (if needed)
npm install

# Run all tests (default)
node run-tests.js

# Run unit tests only
node run-tests.js --unit

# Run with coverage
node run-tests.js --unit --coverage

# Run specific test
node run-tests.js --test "**/reporting-capacity*.spec.ts"

# Run linting
node run-tests.js --lint

# Run build test
node run-tests.js --build

# Run all tests (lint + unit + build)
node run-tests.js --all

# Clean test artifacts
node run-tests.js --clean

# Generate coverage report
node run-tests.js --coverage
```

### Frontend Test Dependencies

- **Jasmine**: Testing framework
- **Karma**: Test runner
- **Angular Testing Utilities**: Component testing
- **NoopAnimationsModule**: Animation testing support

## 📊 Test Coverage

### Backend Coverage Targets
- **Overall Coverage**: > 85%
- **API Endpoints**: > 90%
- **Business Logic**: > 95%
- **Error Handling**: > 80%

### Frontend Coverage Targets
- **Components**: > 85%
- **Services**: > 90%
- **Export Functionality**: > 95%
- **User Interactions**: > 80%

### Coverage Reports

**Backend Coverage**:
```bash
python run_tests.py --coverage
# View: htmlcov/index.html
```

**Frontend Coverage**:
```bash
node run-tests.js --coverage
# View: coverage/index.html
```

## 🏃‍♂️ Running Tests

### Quick Test Commands

**Run All Tests (Backend + Frontend)**:
```bash
# Backend
cd be_cloudcapacity && python run_tests.py --all

# Frontend  
cd fe_cloudcapacity && node run-tests.js --all
```

**Export Functionality Tests**:
```bash
# Backend export tests
python run_tests.py --test tests/test_reporting_capacity.py::TestReportingCapacityEndpoints::test_generate_pptx_report_success

# Frontend export tests
node run-tests.js --test "**/export*.spec.ts"
```

**Coverage Reports**:
```bash
# Backend with coverage
python run_tests.py --coverage --report

# Frontend with coverage
node run-tests.js --coverage
```

## 🏗️ Test Structure

### Backend Test Structure
```python
class TestReportingCapacityEndpoints:
    def test_generate_capacity_report_success(self):
        # Test successful report generation
        
    def test_generate_pptx_report_success(self):
        # Test PPTX export functionality
        
    def test_server_configurations_filtered(self):
        # Test server filtering
```

### Frontend Test Structure
```typescript
describe('ReportingCapacityComponent', () => {
  beforeEach(() => {
    // Setup test environment
  });

  it('should create', () => {
    // Test component creation
  });

  it('should export PNG format successfully', async () => {
    // Test PNG export functionality
  });
});
```

## ✅ Best Practices

### Backend Testing
1. **Use Fixtures**: Leverage pytest fixtures for test data
2. **Mock External Dependencies**: Mock database and API calls
3. **Test Error Scenarios**: Include negative test cases
4. **Validate Data Models**: Test Pydantic model validation
5. **Clean Test Data**: Use separate test database

### Frontend Testing
1. **Mock Services**: Use jasmine spies for service mocking
2. **Test User Interactions**: Simulate clicks and form inputs
3. **Async Testing**: Properly handle async operations
4. **Component Isolation**: Test components in isolation
5. **Accessibility Testing**: Include accessibility checks

### Export Testing
1. **Mock Canvas Operations**: Mock html2canvas and jsPDF
2. **Test File Downloads**: Verify blob creation and download triggers
3. **Validate Export Data**: Ensure correct data in exported files
4. **Test Format Options**: Cover all export formats (PNG, PDF, PPTX)
5. **Error Handling**: Test export failure scenarios

## 🚀 Continuous Integration

### GitHub Actions Example
```yaml
name: Tests
on: [push, pull_request]
jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Backend Tests
        run: |
          cd be_cloudcapacity
          python run_tests.py --all --coverage
  
  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run Frontend Tests
        run: |
          cd fe_cloudcapacity
          node run-tests.js --all --coverage
```

## 📝 Test Maintenance

### Adding New Tests
1. **Backend**: Add test methods to existing test classes or create new test files
2. **Frontend**: Create `.spec.ts` files alongside components
3. **Update Documentation**: Update this guide when adding new test categories

### Test Data Management
- **Backend**: Use factories and fixtures in `conftest.py`
- **Frontend**: Use test utilities in `test-utils.ts`
- **Keep Test Data Minimal**: Only include necessary data for tests

---

For questions about testing or to report issues with tests, please refer to the project documentation or create an issue in the repository.
