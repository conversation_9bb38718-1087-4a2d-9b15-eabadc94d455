#!/usr/bin/env python3
"""
Script to convert Mermaid diagrams to PNG images for LaTeX document
Requires: mermaid-cli (mmdc) to be installed
Install with: npm install -g @mermaid-js/mermaid-cli
"""

import os
import subprocess
import sys
from pathlib import Path


def check_mermaid_cli():
    """Check if mermaid-cli is installed"""
    try:
        # Try different ways to call mmdc on Windows
        commands_to_try = ["mmdc", "mmdc.cmd", "npx mmdc"]

        for cmd in commands_to_try:
            try:
                result = subprocess.run(
                    cmd.split() + ["--version"],
                    capture_output=True,
                    text=True,
                    shell=True,
                )
                if result.returncode == 0:
                    print(f"✓ Mermaid CLI found: {result.stdout.strip()}")
                    return True
            except:
                continue

        print("✗ Mermaid CLI not found")
        return False
    except Exception as e:
        print(f"✗ Error checking Mermaid CLI: {str(e)}")
        return False


def install_mermaid_cli():
    """Install mermaid-cli using npm"""
    print("Installing Mermaid CLI...")
    try:
        result = subprocess.run(
            ["npm", "install", "-g", "@mermaid-js/mermaid-cli"],
            capture_output=True,
            text=True,
        )
        if result.returncode == 0:
            print("✓ Mermaid CLI installed successfully")
            return True
        else:
            print(f"✗ Failed to install Mermaid CLI: {result.stderr}")
            return False
    except FileNotFoundError:
        print("✗ npm not found. Please install Node.js and npm first.")
        return False


def convert_mermaid_to_png(input_file, output_file, width=1200, height=800):
    """Convert a single Mermaid file to PNG"""
    try:
        cmd = [
            "mmdc",
            "-i",
            str(input_file),
            "-o",
            str(output_file),
            "-w",
            str(width),
            "-H",
            str(height),
            "--backgroundColor",
            "white",
            "--theme",
            "default",
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, shell=True)

        if result.returncode == 0:
            print(f"✓ Converted {input_file.name} -> {output_file.name}")
            return True
        else:
            print(f"✗ Failed to convert {input_file.name}: {result.stderr}")
            return False

    except Exception as e:
        print(f"✗ Error converting {input_file.name}: {str(e)}")
        return False


def main():
    """Main conversion process"""
    print("=== Mermaid to PNG Converter for LaTeX ===\n")

    # Check if mermaid-cli is available
    if not check_mermaid_cli():
        print("\nMermaid CLI is required for conversion.")
        print("Would you like to install it? (y/n): ", end="")
        response = input().lower().strip()

        if response == "y" or response == "yes":
            if not install_mermaid_cli():
                print("Installation failed. Please install manually:")
                print("npm install -g @mermaid-js/mermaid-cli")
                sys.exit(1)
        else:
            print("Please install Mermaid CLI manually:")
            print("npm install -g @mermaid-js/mermaid-cli")
            sys.exit(1)

    # Define paths
    script_dir = Path(__file__).parent
    diagrams_dir = script_dir.parent / "web" / "diagrams"
    output_dir = script_dir / "images"

    # Create output directory
    output_dir.mkdir(exist_ok=True)

    # Define diagram files and their output names
    diagrams = {
        "architecture-generale.mmd": "architecture-generale.png",
        "cas-utilisation.mmd": "cas-utilisation.png",
        "sequence-collecte-metriques.mmd": "sequence-collecte-metriques.png",
        "modele-donnees.mmd": "modele-donnees.png",
    }

    print(f"Converting diagrams from: {diagrams_dir}")
    print(f"Output directory: {output_dir}\n")

    success_count = 0
    total_count = len(diagrams)

    # Convert each diagram
    for mmd_file, png_file in diagrams.items():
        input_path = diagrams_dir / mmd_file
        output_path = output_dir / png_file

        if not input_path.exists():
            print(f"✗ Source file not found: {input_path}")
            continue

        # Optimized dimensions for academic document format (more vertical)
        if "sequence" in mmd_file:
            width, height = 1000, 1200  # Taller for sequence diagrams
        elif "modele-donnees" in mmd_file:
            width, height = 1200, 1400  # More square format for ERD
        elif "cas-utilisation" in mmd_file:
            width, height = 1000, 1400  # Much taller for use case diagram
        elif "architecture" in mmd_file:
            width, height = 1000, 1600  # Very tall for layered architecture
        else:
            width, height = 1000, 1200  # Default taller format

        if convert_mermaid_to_png(input_path, output_path, width, height):
            success_count += 1

    print(f"\n=== Conversion Summary ===")
    print(f"Successfully converted: {success_count}/{total_count} diagrams")

    if success_count == total_count:
        print("✓ All diagrams converted successfully!")
        print(f"\nImages saved in: {output_dir}")
        print("\nYou can now use these images in your LaTeX document:")
        for png_file in diagrams.values():
            print(f"  \\includegraphics[width=0.9\\textwidth]{{images/{png_file}}}")
    else:
        print("⚠ Some diagrams failed to convert. Check the errors above.")

    return success_count == total_count


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
