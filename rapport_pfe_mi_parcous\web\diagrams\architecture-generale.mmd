flowchart TB
    %% Couche Présentation
    subgraph Frontend["🌐 Couche Présentation - Angular 19"]
        direction TB
        A[Client Web Angular 19]
        A1[Dashboard<br/>Component]
        A2[Server List<br/>Component]
        A3[Server Detail<br/>Component]
        A4[Usage Comparison<br/>Component]
        A5[Threshold Alerts<br/>Component]

        A --- A1
        A --- A2
        A --- A3
        A --- A4
        A --- A5
    end

    %% Couche API
    subgraph Gateway["🔗 Couche API Gateway"]
        direction TB
        B[API Gateway<br/>Nginx Reverse Proxy]
        B1[Authentication &<br/>Authorization]
        B2[CORS & Security<br/>Headers]

        B --- B1
        B --- B2
    end

    %% Couche Métier
    subgraph Backend["⚙️ Couche Métier - FastAPI"]
        direction TB
        C[Backend FastAPI]
        C1[Server Info<br/>Router]
        C2[Server Usage<br/>Router]
        C3[Server Comparison<br/>Router]
        C4[Threshold<br/>Router]
        C5[Reporting<br/>Router]
        C6[Client Configuration<br/>Router]

        C --- C1
        C --- C2
        C --- C3
        C --- C4
        C --- C5
        C --- C6
    end

    %% Couche Données
    subgraph Storage["🗄️ Couche Stockage"]
        direction TB
        D[Base de données<br/>Oracle]
        D1[Table Serveurs]
        D2[Table Métriques]
        D3[Table Alertes]
        D4[Table Configurations]

        D --- D1
        D --- D2
        D --- D3
        D --- D4
    end

    %% Intégrations Externes
    subgraph External["🔌 Intégrations Externes"]
        direction TB
        E[API Zabbix]
        E1[Zabbix Monitoring<br/>Server]
        E2[Métriques Serveurs<br/>en Temps Réel]
        E3[MAIA CMDB]
        E4[Configurations<br/>Clients]
        E5[Inventaire<br/>Serveurs]

        E --- E1
        E --- E2
        E3 --- E4
        E3 --- E5
    end

    %% Module Reporting
    subgraph Reporting["📊 Module Reporting"]
        direction TB
        F[Service Reporting]
        F1[Génération PDF]
        F2[Export Excel]
        F3[Rapports Automatisés]

        F --- F1
        F --- F2
        F --- F3
    end

    %% Flux de données principaux
    Frontend --> Gateway
    Gateway --> Backend
    Backend --> Storage
    Backend --> External
    Backend --> Reporting

    %% Styles avec couleurs cohérentes
    style Frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    style Gateway fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    style Backend fill:#e8f5e8,stroke:#388e3c,stroke-width:3px
    style Storage fill:#ffebee,stroke:#d32f2f,stroke-width:3px
    style External fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    style Reporting fill:#fce4ec,stroke:#c2185b,stroke-width:3px
