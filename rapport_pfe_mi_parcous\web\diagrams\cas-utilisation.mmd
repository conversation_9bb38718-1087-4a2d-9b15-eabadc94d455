flowchart TB
    %% ========= ACTEURS (STICK FIGURES) =========
    AuthUser["👤<br/>Utilisateur<br/>Authentifié"]
    SysAdmin["👤<br/>Administrateur<br/>Système"]
    ZabbixSystem["🖥️<br/>Système<br/>Zabbix"]
    MaiaCMDB["🗄️<br/>MAIA<br/>CMDB"]

    %% ========= SYSTÈME CLOUDCAPACITY =========
    subgraph SystemBoundary["CloudCapacity - Plateforme de Gestion de Capacité Serveurs"]
        direction TB

        %% Use Cases (Ovals/Ellipses) - Actual Implementation
        UC1("S'authentifier<br/>via Keycloak")
        UC2("Consulter dashboard<br/>de capacité")
        UC3("Consulter liste<br/>des serveurs")
        UC4("Voir détails<br/>serveur")
        UC5("Filtrer serveurs<br/>par critères")
        UC6("Analyser usage<br/>serveur mensuel")
        UC7("Comparer usage<br/>entre serveurs")
        UC8("Comparer usage<br/>entre périodes")
        UC9("Visualiser métriques<br/>en graphiques")
        UC10("Générer rapport<br/>de capacité")
        UC11("Exporter rapport<br/>PowerPoint")
        UC12("Synchroniser serveurs<br/>depuis Zabbix")
        UC13("Synchroniser métriques<br/>depuis Zabbix")
        UC14("Synchroniser configurations<br/>clients MAIA")
        UC15("Consulter configurations<br/>clients actives")
        UC16("Associer serveurs<br/>aux clients")
        UC17("Gérer profil<br/>utilisateur")
        UC18("Configurer paramètres<br/>application")
    end

    %% ========= ASSOCIATIONS ACTEURS - USE CASES =========

    %% Utilisateur Authentifié associations (all frontend functionality)
    AuthUser --- UC1
    AuthUser --- UC2
    AuthUser --- UC3
    AuthUser --- UC4
    AuthUser --- UC5
    AuthUser --- UC6
    AuthUser --- UC7
    AuthUser --- UC8
    AuthUser --- UC9
    AuthUser --- UC10
    AuthUser --- UC11
    AuthUser --- UC17
    AuthUser --- UC18

    %% Administrateur Système associations (backend operations)
    SysAdmin --- UC12
    SysAdmin --- UC13
    SysAdmin --- UC14
    SysAdmin --- UC15
    SysAdmin --- UC16

    %% Système Zabbix associations (data source)
    ZabbixSystem --- UC12
    ZabbixSystem --- UC13

    %% MAIA CMDB associations (configuration source)
    MaiaCMDB --- UC14
    MaiaCMDB --- UC15
    MaiaCMDB --- UC16

    %% ========= RELATIONS INCLUDE/EXTEND =========

    %% Include relationships (dashed arrows with <<include>>) - Actual Dependencies
    UC2 -.->|"<<include>>"| UC1
    UC3 -.->|"<<include>>"| UC1
    UC4 -.->|"<<include>>"| UC1
    UC6 -.->|"<<include>>"| UC13
    UC7 -.->|"<<include>>"| UC13
    UC8 -.->|"<<include>>"| UC13
    UC10 -.->|"<<include>>"| UC6
    UC11 -.->|"<<include>>"| UC10
    UC15 -.->|"<<include>>"| UC14
    UC16 -.->|"<<include>>"| UC15

    %% Extend relationships (dashed arrows with <<extend>>) - Optional Features
    UC5 -.->|"<<extend>>"| UC3
    UC9 -.->|"<<extend>>"| UC6
    UC17 -.->|"<<extend>>"| UC1
    UC18 -.->|"<<extend>>"| UC1

    %% ========= STYLES =========

    %% Actor styles (stick figures)
    style AuthUser fill:#e8f4fd,stroke:#1976d2,stroke-width:2px
    style SysAdmin fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style ZabbixSystem fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style MaiaCMDB fill:#fce4ec,stroke:#c2185b,stroke-width:2px

    %% System boundary
    style SystemBoundary fill:#fafafa,stroke:#424242,stroke-width:3px

    %% Use case styles (ovals) - Color coded by functional area
    style UC1 fill:#f5f5f5,stroke:#616161,stroke-width:2px
    style UC2 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC3 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC4 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC5 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC6 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC7 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC8 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC9 fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style UC10 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style UC11 fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style UC12 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style UC13 fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    style UC14 fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style UC15 fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style UC16 fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    style UC17 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    style UC18 fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
