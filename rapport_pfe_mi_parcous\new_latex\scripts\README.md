# Scripts Directory

This directory contains utility scripts for the CloudCapacity LaTeX report project.

## Scripts Overview

### Diagram Conversion Scripts
- **`convert_diagrams.py`** - Python script to convert Mermaid diagrams to images
- **`convert_diagrams.sh`** - Shell script for Unix/Linux systems to convert diagrams
- **`convert_diagrams.bat`** - Batch script for Windows systems to convert diagrams

### Verification Scripts
- **`verify_images.py`** - Python script to verify that all required images exist and are accessible
- **`verify_improvements.py`** - Python script to verify document improvements and formatting

## Usage

### Converting Diagrams
Run the appropriate script for your operating system:
- **Windows**: `convert_diagrams.bat`
- **Unix/Linux/macOS**: `./convert_diagrams.sh`
- **Python (cross-platform)**: `python convert_diagrams.py`

### Verifying Images
```bash
python verify_images.py
```

### Verifying Improvements
```bash
python verify_improvements.py
```

## Notes
- Make sure you have the required dependencies installed before running the scripts
- For diagram conversion, ensure Mermaid CLI is installed
- Run scripts from the main LaTeX directory (parent of this scripts folder)
