sequenceDiagram
    participant User as Utilisateur
    participant Frontend as Frontend Angular
    participant Backend as Backend FastAPI
    participant <PERSON>abbix as API Zabbix
    participant MA<PERSON> as MAIA CMDB
    participant DB as Base Oracle
    participant JIRA as API JIRA

    Note over User,JIRA: Workflows complets CloudCapacity

    %% === 1. INITIALISATION ET SYNCHRONISATION ===
    rect rgb(240, 248, 255)
        Note over User,DB: 1. Synchronisation initiale des données
        User->>Frontend: Accès application
        Frontend->>Backend: GET /capacity/get-servers
        Backend->>DB: SELECT * FROM Server
        DB-->>Backend: Liste serveurs existants
        Backend-->>Frontend: ServerRead[] avec client info

        User->>Frontend: Clic "Refresh Servers"
        Frontend->>Backend: POST /capacity/return-and-sync-hostnames-and-metrics
        Backend->>Zabbix: host.get(filter: "frsops*")
        Zabbix-->>Backend: Liste hosts actifs
        Backend->>Zabbix: item.get(hostids, keys: CPU/RAM/Swap)
        Zabbix-->>Backend: Métriques statiques serveurs
        Backend->>DB: INSERT/UPDATE Server table
        DB-->>Backend: Confirmation stockage
        Backend-->>Frontend: Liste hostnames synchronisés
    end

    %% === 2. SYNCHRONISATION MAIA CMDB ===
    rect rgb(248, 255, 248)
        Note over User,DB: 2. Intégration configurations clients MAIA
        Backend->>MAIA: Connexion proxy SOCKS (**********:8443)
        MAIA-->>Backend: Session MySQL établie
        Backend->>MAIA: SELECT * FROM clients_conf WHERE active='1'
        MAIA-->>Backend: Configurations clients actives
        Backend->>DB: INSERT/UPDATE ClientConf table
        DB-->>Backend: Configurations synchronisées

        Backend->>Backend: sync_client_information()
        Note right of Backend: Mapping serveurs ↔ clients
        Backend->>DB: UPDATE Server SET client_name, client_config_label
        DB-->>Backend: Associations mises à jour
    end

    %% === 3. CONSULTATION MÉTRIQUES ===
    rect rgb(255, 248, 240)
        Note over User,DB: 3. Consultation métriques serveur
        User->>Frontend: Sélection serveur + mois
        Frontend->>Backend: POST /capacity/server-usage-per-month
        Note right of Frontend: {hostname, year, month}

        Backend->>Zabbix: Authentification API
        Zabbix-->>Backend: Session Zabbix active
        Backend->>Zabbix: get_host_id(hostname)
        Zabbix-->>Backend: host_id
        Backend->>Zabbix: get_item_ids(host_id, ["cpu", "memory", "swap"])
        Zabbix-->>Backend: item_ids mappés
        Backend->>Zabbix: trend.get(itemids, time_from, time_till)
        Note right of Backend: Données horaires complètes
        Zabbix-->>Backend: Raw trend data

        Backend->>Backend: process_trend_data()
        Note right of Backend: Calculs moyennes, transformation
        Backend-->>Frontend: ZabbixData{cpu_usage, memory_usage, swap_utilization}

        Frontend->>Frontend: Chart.js rendering
        Note right of Frontend: Graphiques interactifs
    end

    %% === 4. COMPARAISON SERVEURS/PÉRIODES ===
    rect rgb(255, 240, 255)
        Note over User,DB: 4. Comparaison usage serveurs
        User->>Frontend: Mode comparaison (serveurs ou périodes)
        Frontend->>Backend: POST /capacity/usage-comparison
        Note right of Frontend: UsageComparisonRequest{type, servers, months}

        alt Comparaison deux serveurs même mois
            Backend->>Zabbix: Récupération données serveur1
            Backend->>Zabbix: Récupération données serveur2
            Backend->>Backend: calculate_differences()
        else Comparaison un serveur deux mois
            Backend->>Zabbix: Récupération données mois1
            Backend->>Zabbix: Récupération données mois2
            Backend->>Backend: calculate_temporal_differences()
        end

        Backend->>Backend: generate_statistics()
        Note right of Backend: MetricStatistics, trends, peaks
        Backend-->>Frontend: EnhancedUsageComparison

        Frontend->>Frontend: Render comparison charts
        Note right of Frontend: Graphiques superposés + tableaux
    end

    %% === 5. GÉNÉRATION RAPPORTS ===
    rect rgb(240, 255, 240)
        Note over User,DB: 5. Génération et export rapports
        User->>Frontend: Demande rapport capacité
        Frontend->>Backend: POST /capacity/generate-capacity-report
        Note right of Frontend: {servers[], period, format}

        Backend->>DB: SELECT usage data aggregated
        DB-->>Backend: Données historiques
        Backend->>Backend: prepare_report_data()
        Note right of Backend: Calculs statistiques, tendances

        alt Export PowerPoint
            Backend->>Backend: generate_pptx()
            Note right of Backend: python-pptx library
            Backend-->>Frontend: PPTX file stream
        else Export JSON
            Backend-->>Frontend: JSON report data
        end

        Frontend->>Frontend: Download/Display report
    end

    %% === 6. INTÉGRATION JIRA (OPTIONNELLE) ===
    rect rgb(255, 255, 240)
        Note over User,JIRA: 6. Intégration tickets JIRA
        User->>Frontend: Consultation ticket JIRA
        Frontend->>Backend: POST /capacity/jira/get-issue
        Note right of Frontend: {issue_key: "PRODOPSNSS-123"}

        Backend->>JIRA: GET /rest/api/2/issue/{key}
        JIRA-->>Backend: Issue details + attachments + comments
        Backend->>Backend: format_jira_response()
        Backend-->>Frontend: JiraIssueResponse

        Frontend->>Frontend: Display ticket info
        Note right of Frontend: Intégration alertes ↔ tickets
    end

    %% === 7. GESTION CONFIGURATIONS CLIENTS ===
    rect rgb(248, 248, 255)
        Note over User,DB: 7. Gestion configurations clients
        User->>Frontend: Consultation clients actifs
        Frontend->>Backend: GET /capacity/clients-conf/active
        Backend->>DB: SELECT * FROM ClientConf WHERE active='1'
        DB-->>Backend: Configurations actives
        Backend-->>Frontend: ClientConfReadEnhanced[]

        User->>Frontend: Détails client avec serveurs
        Frontend->>Backend: GET /capacity/clients-conf/{id}/with-servers
        Backend->>DB: JOIN ClientConf + Server tables
        DB-->>Backend: Client + serveurs associés
        Backend-->>Frontend: ClientWithServersResponse

        Frontend->>Frontend: Display client dashboard
        Note right of Frontend: Vue organisée par client/environnement
    end
