#!/bin/bash
# Shell script to convert Mermaid diagrams to PNG for Unix/Linux/macOS
# Requires Node.js and npm to be installed

echo "=== Mermaid to PNG Converter for LaTeX (Unix/Linux/macOS) ==="
echo

# Check if Node.js is installed
if ! command -v node &>/dev/null; then
    echo "Error: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &>/dev/null; then
    echo "Error: npm is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js and npm are available"

# Check if mermaid-cli is installed
if ! command -v mmdc &>/dev/null; then
    echo "Mermaid CLI not found. Installing..."
    npm install -g @mermaid-js/mermaid-cli
    if [ $? -ne 0 ]; then
        echo "Failed to install Mermaid CLI"
        echo "You may need to run with sudo: sudo npm install -g @mermaid-js/mermaid-cli"
        exit 1
    fi
    echo "Mermaid CLI installed successfully"
else
    echo "Mermaid CLI is already installed"
fi

# Create images directory
mkdir -p images

echo
echo "Converting diagrams..."

# Function to convert diagram
convert_diagram() {
    local input_file="$1"
    local output_file="$2"
    local width="$3"
    local height="$4"

    if [ -f "$input_file" ]; then
        mmdc -i "$input_file" -o "$output_file" -w "$width" -H "$height" --backgroundColor white --theme default
        if [ $? -eq 0 ]; then
            echo "✓ Converted $(basename "$input_file")"
            return 0
        else
            echo "✗ Failed to convert $(basename "$input_file")"
            return 1
        fi
    else
        echo "✗ $(basename "$input_file") not found"
        return 1
    fi
}

# Convert diagrams with optimized dimensions for academic format
success_count=0
total_count=4

# Architecture diagram (optimized for vertical layout)
convert_diagram "../web/diagrams/architecture-generale.mmd" "images/architecture-generale.png" 1000 1600
[ $? -eq 0 ] && ((success_count++))

# Use case diagram (optimized for vertical layout)
convert_diagram "../web/diagrams/cas-utilisation.mmd" "images/cas-utilisation.png" 1000 1400
[ $? -eq 0 ] && ((success_count++))

# Sequence diagram (optimized for vertical layout)
convert_diagram "../web/diagrams/sequence-collecte-metriques.mmd" "images/sequence-collecte-metriques.png" 1000 1200
[ $? -eq 0 ] && ((success_count++))

# Data model diagram (optimized for square format)
convert_diagram "../web/diagrams/modele-donnees.mmd" "images/modele-donnees.png" 1200 1400
[ $? -eq 0 ] && ((success_count++))

echo
echo "=== Conversion Summary ==="
echo "Successfully converted: $success_count/$total_count diagrams"

if [ $success_count -eq $total_count ]; then
    echo "✓ All diagrams converted successfully!"
    echo
    echo "Images saved in the 'images' directory"
    echo "You can now compile your LaTeX document with the generated PNG images"
    echo
    echo "LaTeX usage examples:"
    echo "  \\includegraphics[width=0.9\\textwidth]{images/architecture-generale.png}"
    echo "  \\includegraphics[width=0.9\\textwidth]{images/cas-utilisation.png}"
    echo "  \\includegraphics[width=0.9\\textwidth]{images/sequence-collecte-metriques.png}"
    echo "  \\includegraphics[width=0.9\\textwidth]{images/modele-donnees.png}"
else
    echo "⚠ Some diagrams failed to convert. Check the errors above."
    exit 1
fi
