erDiagram
    Server {
        int id PK
        string hostname UK
        string server_type
        int total_cpus
        float total_ram_b
        float total_ram_mb
        float total_ram_gb
        float total_swap_b
        float total_swap_mb
        float total_swap_gb
        int status
        string timestamp_update
        string client_name
        string client_environment
        string client_config_label
        string server_role
        string platform
        string envtype
    }

    UsageDataPoint {
        int id PK
        string hostname FK
        datetime timestamp
        float cpu_usage
        float memory_usage
        float swap_usage
        string metric_type
    }

    Alert {
        int id PK
        string hostname FK
        string alert_type
        float threshold_value
        float current_value
        datetime timestamp
        string status
        string severity
        text description
    }

    Threshold {
        int id PK
        string threshold_type
        float warning_value
        float critical_value
        boolean enabled
        datetime created_at
        datetime updated_at
    }

    ClientConf {
        int id PK
        string custconflabel
        string clientname
        string progiciel
        string platform
        string envtype
        string webserver
        string webserverint
        string treatmentserver
        string dbserver1
        string dbserver2
        string qserver1
        string qserver2
        string qserver3
        string qserver4
        string qserver5
        string qserver6
        string dataprivacy
        string active
        string timestamp_update
    }

    MonthlyData {
        int id PK
        string hostname FK
        int year
        int month
        float avg_cpu_usage
        float max_cpu_usage
        float avg_memory_usage
        float max_memory_usage
        float avg_swap_usage
        float max_swap_usage
        datetime calculated_at
    }

    ZabbixServerInfo {
        string hostname PK
        string status
        string hostid
        string available
    }

    JiraIssue {
        string key PK
        string summary
        text description
        string status
        string priority
        string assignee
        string reporter
        datetime created
        datetime updated
        string project
        string issue_type
    }

    JiraAttachment {
        string id PK
        string issue_key FK
        string filename
        int size
        string content_type
        datetime created
        string author
        string thumbnail_url
        string content_url
    }

    JiraComment {
        string id PK
        string issue_key FK
        string author
        text body
        datetime created
        datetime updated
    }

    ServerDetailsForClient {
        string hostname PK
        string environment
        string threads
        string ram
        string swap
        string client_config_label FK
    }

    ThresholdTypeEnum {
        string cpu
        string memory
        string swap
    }

    ComparisonTypeEnum {
        string one_server_two_months
        string two_servers_same_month
    }

    EnhancedUsageDataPoint {
        int timestamp
        float value_avg
        float value_min
        float value_max
        string date
    }

    MetricStatistics {
        float average
        float minimum
        float maximum
        int peak_timestamp
        string peak_date
        string trend_direction
        float trend_percentage
    }

    UsageDifference {
        int timestamp
        float month1_value
        float month2_value
        float difference
        string date
    }

    ComprehensiveMonthlyData {
        string hostname
        string month
        json cpu_usage
        json memory_usage
        json swap_utilization
        json disk_io_read
        json disk_io_write
        json network_in
        json network_out
    }

    %% Relations principales
    Server ||--o{ UsageDataPoint : "génère"
    Server ||--o{ Alert : "déclenche"
    Server ||--o{ MonthlyData : "agrège"
    Server }o--|| ClientConf : "associé_via_client_config_label"
    Threshold ||--o{ Alert : "définit"

    %% Relations JIRA
    JiraIssue ||--o{ JiraAttachment : "contient"
    JiraIssue ||--o{ JiraComment : "contient"

    %% Relations clients et serveurs
    ClientConf ||--o{ ServerDetailsForClient : "contient"

    %% Relations Zabbix
    ZabbixServerInfo ||--o{ Server : "synchronise_vers"

    %% Relations de données étendues
    Server ||--o{ EnhancedUsageDataPoint : "génère_données_étendues"
    Server ||--o{ ComprehensiveMonthlyData : "agrège_données_complètes"

    %% Relations statistiques
    MetricStatistics ||--o{ UsageDifference : "calcule_différences"

    %% Notes détaillées:
    %% === TABLES ORACLE (Persistantes) ===
    %% Server: Table principale Oracle avec métadonnées serveurs et informations clients intégrées
    %% ClientConf: Table Oracle synchronisée depuis MAIA CMDB avec inventaire serveurs complet

    %% === MODÈLES API ZABBIX (Temporaires) ===
    %% UsageDataPoint: Points de données d'utilisation collectés depuis Zabbix via API
    %% MonthlyData: Données agrégées mensuelles calculées à partir de Zabbix
    %% ZabbixServerInfo: Informations serveurs récupérées directement depuis Zabbix API
    %% EnhancedUsageDataPoint: Points de données étendus avec min/max pour analyses avancées
    %% ComprehensiveMonthlyData: Données mensuelles complètes incluant disque et réseau

    %% === MODÈLES MÉTIER (API Responses) ===
    %% Alert: Alertes générées par dépassement de seuils (calculées dynamiquement)
    %% Threshold: Configuration des seuils d'alerte (stockés en configuration)
    %% MetricStatistics: Statistiques calculées pour analyses comparatives
    %% UsageDifference: Différences calculées entre périodes ou serveurs
    %% ServerDetailsForClient: Détails serveurs formatés pour réponses clients

    %% === INTÉGRATION JIRA (API Externes) ===
    %% JiraIssue: Tickets JIRA récupérés via API pour intégration alertes
    %% JiraAttachment: Pièces jointes des tickets JIRA
    %% JiraComment: Commentaires des tickets JIRA

    %% === ÉNUMÉRATIONS ===
    %% ThresholdTypeEnum: Types de seuils (cpu, memory, swap)
    %% ComparisonTypeEnum: Types de comparaisons (serveurs, périodes)
