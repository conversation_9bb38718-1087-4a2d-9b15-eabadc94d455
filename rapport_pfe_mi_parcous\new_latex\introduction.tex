%========= Introduction Générale =========%
%                                         %
% General introduction chapter            %
%=========================================%

\chapter*{Introduction générale}
\addcontentsline{toc}{chapter}{Introduction générale}

\section*{Contexte général}

À l'ère de la transformation numérique, la gestion optimale des infrastructures informatiques constitue un enjeu stratégique majeur pour les entreprises. Dans un environnement où la disponibilité des services et la performance des systèmes conditionnent directement la satisfaction client et la compétitivité, les organisations doivent relever des défis complexes liés à la gestion de capacité, à l'optimisation des ressources et à la prévention proactive des incidents.

Les infrastructures on-premise, bien qu'offrant un contrôle total sur les données et les processus, nécessitent une expertise technique approfondie et des outils de gestion de capacité sophistiqués pour garantir leur efficacité opérationnelle. La complexité croissante des architectures distribuées, combinée à l'augmentation des volumes de données à traiter, rend indispensable l'adoption de solutions automatisées de gestion de capacité et d'analyse prédictive.

\section*{Problématique}

\companyName, acteur de référence dans le domaine des solutions de gestion des ressources humaines, opère une infrastructure on-premise critique composée de plus de 400 serveurs supportant les applications métier de centaines de clients à travers le monde. Cette infrastructure, garante de la continuité de service et de la sécurité des données RH sensibles, nécessite un suivi constant et une gestion proactive des capacités.

Actuellement, le processus de gestion et d'analyse de la capacité des serveurs repose sur des approches largement manuelles, impliquant la consultation de multiples outils disparates, la génération manuelle de rapports et l'analyse ponctuelle des métriques. Cette situation engendre plusieurs problématiques majeures : temps de traitement élevé, risque d'erreurs humaines, difficultés de corrélation des données, et surtout, capacité limitée d'anticipation des surcharges et des besoins d'évolution de l'infrastructure.

\section*{Objectifs du projet}

Le projet \projectTitle\ s'inscrit dans une démarche d'innovation et d'amélioration continue visant à moderniser et automatiser la gestion de la capacité des serveurs chez \companyName. L'objectif principal consiste à concevoir et développer une plateforme web intégrée de gestion de capacité serveurs permettant de :

\begin{itemize}
    \item Centraliser la collecte et la visualisation des métriques de capacité serveurs (CPU, mémoire, swap)
    \item Automatiser la génération de rapports de capacité personnalisés et leur envoi aux équipes
    \item Implémenter un système d'alertes intelligentes basé sur des seuils configurables
    \item Fournir des outils de comparaison et d'analyse temporelle des usages entre serveurs et périodes
    \item Intégrer l'infrastructure de collecte de métriques Zabbix existante avec une interface moderne
    \item Préparer l'intégration future d'algorithmes d'intelligence artificielle (CatBoost) pour l'analyse prédictive
    \item Améliorer la prise de décision concernant le provisioning et l'évolution de l'infrastructure
\end{itemize}

\section*{Approche méthodologique}

La réalisation de ce projet s'appuie sur une méthodologie rigoureuse combinant analyse des besoins métier, conception architecturale moderne et développement itératif. L'approche adoptée privilégie l'intégration native avec l'écosystème technique existant, notamment l'infrastructure de collecte de métriques Zabbix et la base de données Oracle, tout en introduisant des technologies web modernes (Angular 19, FastAPI) pour garantir une expérience utilisateur optimale et une maintenabilité à long terme. Le déploiement est prévu sur l'infrastructure OpenShift avec une pipeline CI/CD GitLab.

\section*{Structure du rapport}

Ce rapport de mi-parcours présente l'avancement du projet \projectTitle\ selon une structure académique classique. Le premier chapitre expose l'étude de l'existant et l'analyse du contexte organisationnel et technique. Le deuxième chapitre détaille l'analyse des besoins et les spécifications fonctionnelles et non fonctionnelles. Le troisième chapitre présente la conception architecturale et les choix technologiques. Le quatrième chapitre décrit la réalisation et l'implémentation des fonctionnalités. Le cinquième chapitre expose les tests réalisés et la validation des développements. Enfin, le sixième chapitre dresse un bilan de l'avancement et présente les perspectives d'évolution du projet.

\clearpage
