# Remarques - Lionel

## Décommissionnement des serveurs
- **Décommissionnement par serveur** : Moins prioritaire
- **Décommissionnement par clients** : Plus important, avec granularité par client (voir CHMNG-4033 pour exemple)
- **À considérer** : Leurs serveurs de base de données et leurs dépendances

## Points de coordination
- **Réunion avec Manel** : Pour connaître les suggestions, l'usage actuel et les évolutions possibles
- **Coordination avec Hatem** : À planifier

## API Jira - Gestion des tokens
- **jira_api_users** : Pour récupérer un token de manière temporaire pour les tests
- **Cron job de renouvellement** :
  ```bash
  00 13 * * * . ~/.profile;cd /data/scripts/python/jiraclient/;python3 launch2_token.py RENEWTOKEN prod automate.supervision 1>>/data/scripts/python/jiraclient/logs/launch_RENEW.log 2>&1
  ```
- **Fonction spécifique** : `def renew_token_treatment(env, login):` pour renouveler le token automatiquement