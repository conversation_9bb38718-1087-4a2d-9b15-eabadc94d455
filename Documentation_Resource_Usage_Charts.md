# Documentation - Fonctionnement des Graphiques Resource-Usage

## Vue d'ensemble

Le composant `resource-usage` affiche des graphiques interactifs montrant l'utilisation des ressources serveurs (CPU, mémoire, swap) sur une période donnée. Cette documentation explique comment les données sont récupérées, traitées et visualisées.

## Bibliothèques Utilisées

### Frontend (Angular)
- **Chart.js** - Bibliothèque principale pour la création des graphiques
- **Angular Material** - Interface utilisateur (sélecteurs, boutons, dialogues)
- **RxJS** - Gestion des flux de données asynchrones
- **html2canvas** - Capture d'écran pour l'export
- **jsPDF** - Génération de fichiers PDF

### Backend (Python/FastAPI)
- **pyzabbix** - Connexion et requêtes vers l'API Zabbix
- **FastAPI** - Endpoints API REST
- **SQLAlchemy** - Accès aux données de la base
- **ThreadPoolExecutor** - Traitement parallèle des serveurs

## Flux de Fonctionnement

### 1. Sélection et Initialisation
- L'utilisateur sélectionne un serveur dans la liste déroulante
- Sélection de la période (mois/année) via les contrôles de date
- Le composant appelle `loadUsageData()` pour récupérer les données

### 2. Récupération des Données Backend
**Endpoint principal** : `POST /capacity/server-usage-per-month`

**Paramètres** :
- `host_name` - Nom d'hôte du serveur
- `year` - Année sélectionnée
- `month` - Mois sélectionné (1-12)

**Traitement backend** :
1. Connexion à l'API Zabbix avec authentification
2. Récupération de l'ID du host depuis Zabbix
3. Identification des métriques système :
   - `system.cpu.util[,idle]` - Utilisation CPU (converti depuis idle)
   - `vm.memory.utilization` - Utilisation mémoire
   - `system.swap.utilization` - Utilisation swap
4. Récupération des données de tendance (trend data) horaires
5. Formatage et retour des données structurées

### 3. Traitement des Données Frontend
**Méthode `updateChartData()`** :
- Traitement des données CPU avec conversion idle → usage
- Traitement des données mémoire et swap
- Formatage des labels temporels
- Préparation des datasets pour Chart.js

### 4. Création des Graphiques
**Trois graphiques distincts** :

#### Graphique CPU
- **Type** : Line ou Bar (commutable)
- **Couleur** : Rose/Magenta (`rgba(255, 182, 193, 1)`)
- **Données** : Pourcentage d'utilisation CPU
- **Échelle** : 0-100%

#### Graphique Mémoire
- **Type** : Line ou Bar (commutable)
- **Couleur** : Violet (`rgba(147, 112, 219, 1)`)
- **Données** : Pourcentage d'utilisation mémoire
- **Échelle** : 0-100%

#### Graphique Swap
- **Type** : Line ou Bar (commutable)
- **Couleur** : Bleu (`rgba(63, 81, 181, 1)`)
- **Données** : Pourcentage d'utilisation swap
- **Échelle** : 0-100%

## Configuration Chart.js

### Options Communes
- **Responsive** : Adaptation automatique à la taille du conteneur
- **Animations** : Transitions fluides lors des changements
- **Grille** : Lignes de grille semi-transparentes
- **Tooltips** : Informations détaillées au survol
- **Légendes** : Étiquettes colorées pour identification

### Stylisation Thème Sombre
- **Couleurs de texte** : Blanc/gris clair pour la lisibilité
- **Grille** : Lignes blanches semi-transparentes
- **Arrière-plan** : Transparent pour intégration au thème
- **Points** : Bordures blanches pour contraste

## Fonctionnalités Interactives

### Maximisation des Graphiques
- **Service** : `ChartMaximizationService`
- **Fonctionnalité** : Affichage plein écran des graphiques
- **Interface** : Dialogue modal responsive
- **Navigation** : Boutons de fermeture et contrôles

### Changement de Type de Graphique
- **Basculement** : Line ↔ Bar chart
- **Méthodes** : `toggleCpuChartType()`, `toggleMemoryChartType()`, `toggleSwapChartType()`
- **Persistance** : Maintien du type sélectionné pendant la session

### Export des Graphiques
- **Formats disponibles** : PNG, PDF, Excel
- **Service** : `ExportDialogComponent`
- **Capture** : html2canvas pour la génération d'images
- **Téléchargement** : Automatique avec noms de fichiers horodatés

## Optimisation des Performances

### Monitoring des Performances
- **Service** : `PerformanceMonitor`
- **Métriques** : Temps de traitement des données
- **Logging** : Utilisation mémoire et temps d'exécution
- **Optimisation** : Identification des goulots d'étranglement

### Gestion Mémoire
- **Destruction des graphiques** : Nettoyage automatique lors du changement
- **Réutilisation des canvas** : Évite les fuites mémoire
- **Debouncing** : Limitation des appels API répétés

## Structure des Données

### Format Zabbix (Backend)
```
{
  "cpu_usage": [
    {
      "timestamp": 1640995200,
      "value_avg": 45.67,
      "date": "2022-01-01 00:00:00"
    }
  ],
  "memory_usage": [...],
  "swap_utilization": [...]
}
```

### Format Chart.js (Frontend)
```
{
  "labels": ["01/01", "01/02", "01/03", ...],
  "datasets": [{
    "label": "CPU Usage (%)",
    "data": [45.67, 52.34, 38.91, ...],
    "borderColor": "rgba(255, 182, 193, 1)",
    "backgroundColor": "rgba(255, 182, 193, 0.3)"
  }]
}
```

## Gestion des Erreurs

### Frontend
- **Validation** : Vérification de la sélection serveur/période
- **États de chargement** : Indicateurs visuels pendant les requêtes
- **Messages d'erreur** : Notifications utilisateur en cas d'échec
- **Fallback** : Graphiques vides avec messages informatifs

### Backend
- **Connexion Zabbix** : Gestion des timeouts et erreurs réseau
- **Validation des données** : Vérification de la cohérence des métriques
- **Logging** : Enregistrement détaillé pour le débogage
- **Codes d'erreur HTTP** : Retours appropriés (404, 500)

## Endpoints Principaux

| Méthode | Endpoint | Description |
|---------|----------|-------------|
| POST | `/capacity/server-usage-per-month` | Données d'usage pour un serveur/mois |
| POST | `/capacity/all-servers-usage-per-selected-month` | Données agrégées tous serveurs |

## Personnalisation et Extensions

### Ajout de Nouvelles Métriques
- Extension des modèles de données backend
- Ajout des clés Zabbix correspondantes
- Création de nouveaux graphiques frontend
- Mise à jour des interfaces de données

### Personnalisation Visuelle
- Modification des couleurs dans les configurations Chart.js
- Adaptation des thèmes via les variables CSS
- Personnalisation des animations et transitions

Cette architecture offre une visualisation performante et interactive des données de ressources serveurs avec une synchronisation temps réel depuis Zabbix et des capacités d'export professionnelles.
