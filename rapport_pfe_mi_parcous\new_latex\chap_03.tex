%========= Chapitre 3: Conception et architecture =========%
%                                                           %
% Chapter 3: Design and architecture                       %
%===========================================================%

\chapter{Conception et architecture}

\section{Architecture générale du système}

\subsection{Vue d'ensemble}

L'architecture de \projectTitle\ est conçue pour être modulaire, distribuée et scalable, permettant une évolution progressive du système et une adaptation aux besoins futurs. Elle s'articule autour des composants suivants :

\begin{itemize}
    \item \textbf{Frontend Angular 19} : Interface utilisateur moderne avec composants autonomes
    \item \textbf{Backend FastAPI} : API RESTful modulaire pour la logique métier
    \item \textbf{Base de données Oracle} : Stockage des informations serveurs et configurations
    \item \textbf{Intégration Zabbix} : Collecte des métriques de performance en temps réel
    \item \textbf{Intégration MAIA CMDB} : Synchronisation des configurations clients et inventaire serveurs
    \item \textbf{Module de reporting} : Génération automatique de rapports PDF/Excel
\end{itemize}

\clearpage
\thispagestyle{empty}
\begin{figure}[p]
\centering
\vspace*{-1.5cm}
\includegraphics[width=0.95\textwidth,height=0.9\textheight,keepaspectratio]{images/architecture-generale.png}
\vspace{1cm}
\caption{Architecture générale de \projectTitle}
\label{fig:architecture-generale}
\end{figure}
\clearpage

\textbf{Description du diagramme :} Ce diagramme d'architecture présente l'organisation en couches de la plateforme \projectTitle. Il illustre une architecture moderne en 6 couches principales : la \textbf{Couche Présentation} (Angular 19) avec ses 5 composants principaux (Dashboard, Server List, Server Detail, Usage Comparison, Threshold Alerts), la \textbf{Couche API Gateway} (Nginx) gérant l'authentification et la sécurité, la \textbf{Couche Métier} (FastAPI) avec ses 6 routeurs spécialisés incluant le routeur de configuration clients, la \textbf{Couche Stockage} (Oracle) contenant les tables de données, les \textbf{Intégrations Externes} comprenant l'API Zabbix pour la collecte de métriques et MAIA CMDB pour la gestion des configurations clients, et le \textbf{Module Reporting} pour la génération de rapports. Les flux de données suivent une architecture RESTful avec séparation claire des responsabilités, garantissant la modularité et l'évolutivité du système tout en intégrant harmonieusement les systèmes externes existants.

\subsection{Composants principaux}

L'architecture repose sur les principes suivants :

\begin{itemize}
    \item \textbf{Séparation des responsabilités} : Les différentes couches (présentation, logique métier, accès aux données) sont clairement séparées
    \item \textbf{API-centric} : Toutes les interactions entre les composants se font via des API RESTful bien définies
    \item \textbf{Modularité} : Chaque composant peut évoluer indépendamment
    \item \textbf{Évolutivité} : L'architecture permet d'ajouter facilement de nouvelles fonctionnalités
\end{itemize}

\section{Modélisation UML}

\subsection{Diagrammes de cas d'utilisation}

Le diagramme de cas d'utilisation présente les principales interactions entre les acteurs du système et les fonctionnalités de gestion de capacité offertes par \projectTitle.

\clearpage
\thispagestyle{empty}
\begin{figure}[p]
\centering
\vspace*{-1.5cm}
\includegraphics[width=0.95\textwidth,height=0.9\textheight,keepaspectratio]{images/cas-utilisation.png}
\vspace{1cm}
\caption{Diagramme de cas d'utilisation - Gestion de capacité serveurs}
\label{fig:cas-utilisation}
\end{figure}
\clearpage

\textbf{Description du diagramme :} Ce diagramme UML de cas d'utilisation respecte la notation standard et reflète fidèlement l'implémentation réelle de \projectTitle. Il modélise les interactions entre 4 acteurs principaux et 18 cas d'utilisation basés sur l'analyse approfondie du code frontend et backend. Les \textbf{acteurs} sont positionnés à l'extérieur du système : l'\textbf{Utilisateur Authentifié} (accès à toutes les fonctionnalités frontend via Keycloak), l'\textbf{Administrateur Système} (opérations backend et synchronisation), le \textbf{Système Zabbix} (source de métriques), et \textbf{MAIA CMDB} (source de configurations clients). Cette représentation corrige les incohérences identifiées entre la conception initiale et l'implémentation effective, notamment l'absence de contrôle d'accès basé sur les rôles dans le backend et la nature unifiée de l'expérience utilisateur frontend. Les \textbf{relations d'inclusion} reflètent les dépendances techniques réelles du système, comme l'authentification Keycloak requise pour l'accès aux fonctionnalités et la synchronisation des données externes nécessaire pour les opérations de consultation.

Les acteurs du système représentent les différents types d'utilisateurs et systèmes externes qui interagissent réellement avec \projectTitle selon l'implémentation effective :

\begin{itemize}
    \item \textbf{Utilisateur Authentifié} : Tout utilisateur connecté via Keycloak ayant accès à l'ensemble des fonctionnalités frontend (dashboard, serveurs, comparaisons, rapports). Le système ne différencie pas les rôles au niveau fonctionnel
    \item \textbf{Administrateur Système} : Responsable des opérations backend, de la synchronisation des données Zabbix et MAIA CMDB, et de la maintenance de l'infrastructure
    \item \textbf{Système Zabbix} : Système externe automatisé fournissant les métriques de performance des serveurs (CPU, mémoire, swap) via API
    \item \textbf{MAIA CMDB} : Base de données de gestion de configuration contenant les informations clients, environnements et inventaire serveurs, synchronisée via proxy SOCKS
\end{itemize}

\subsection{Cas d'utilisation par domaine fonctionnel}

Les 18 cas d'utilisation de \projectTitle\ reflètent l'implémentation réelle du système et sont organisés selon les domaines fonctionnels effectifs :

\subsubsection{Authentification et Accès}
Gestion de l'accès au système :
\begin{itemize}
    \item \textbf{S'authentifier via Keycloak} : Authentification sécurisée requise pour l'accès aux fonctionnalités
\end{itemize}

\subsubsection{Consultation et Analyse de Capacité}
Fonctionnalités frontend d'analyse des données de capacité serveurs :
\begin{itemize}
    \item \textbf{Consulter dashboard de capacité} : Vue d'ensemble des métriques avec indicateurs clés et graphiques
    \item \textbf{Consulter liste des serveurs} : Accès à l'inventaire complet avec filtrage et tri
    \item \textbf{Voir détails serveur} : Consultation des informations détaillées et métriques historiques
    \item \textbf{Filtrer serveurs par critères} : Recherche avancée par environnement, type, client
    \item \textbf{Analyser usage serveur mensuel} : Analyse détaillée de l'utilisation des ressources par mois
    \item \textbf{Comparer usage entre serveurs} : Comparaison des métriques entre différents serveurs
    \item \textbf{Comparer usage entre périodes} : Analyse temporelle des évolutions de capacité
    \item \textbf{Visualiser métriques en graphiques} : Représentation graphique des données avec Chart.js
\end{itemize}

\subsubsection{Reporting et Export}
Génération et export des rapports de capacité :
\begin{itemize}
    \item \textbf{Générer rapport de capacité} : Création de rapports JSON avec données agrégées
    \item \textbf{Exporter rapport PowerPoint} : Export des rapports au format PPTX pour présentation
\end{itemize}

\subsubsection{Gestion Utilisateur}
Fonctionnalités de gestion du profil utilisateur :
\begin{itemize}
    \item \textbf{Gérer profil utilisateur} : Consultation et modification des informations personnelles
    \item \textbf{Configurer paramètres application} : Personnalisation des préférences utilisateur
\end{itemize}

\subsubsection{Synchronisation Zabbix (Opérations Backend)}
Fonctionnalités de synchronisation avec le système Zabbix :
\begin{itemize}
    \item \textbf{Synchroniser serveurs depuis Zabbix} : Récupération et stockage des informations serveurs depuis l'API Zabbix
    \item \textbf{Synchroniser métriques depuis Zabbix} : Collecte des données de performance (CPU, mémoire, swap) depuis Zabbix
\end{itemize}

\subsubsection{Intégration MAIA CMDB (Opérations Backend)}
Fonctionnalités de synchronisation avec MAIA CMDB :
\begin{itemize}
    \item \textbf{Synchroniser configurations clients MAIA} : Synchronisation automatique des données de configuration depuis la base MAIA CMDB via proxy SOCKS
    \item \textbf{Consulter configurations clients actives} : Consultation des configurations clients actives avec leurs environnements et serveurs associés
    \item \textbf{Associer serveurs aux clients} : Association et mapping des serveurs aux configurations clients pour une gestion organisée par environnement
\end{itemize}

\subsection{Relations UML et notation standard}

Le diagramme respecte la notation UML standard pour les diagrammes de cas d'utilisation :

\begin{itemize}
    \item \textbf{Acteurs} : Représentés par des figures humaines (stick figures) positionnées à l'extérieur du système
    \item \textbf{Frontière système} : Rectangle délimitant clairement les fonctionnalités de \projectTitle
    \item \textbf{Cas d'utilisation} : Ellipses contenant les fonctionnalités accessibles aux acteurs
    \item \textbf{Associations} : Traits pleins reliant les acteurs aux cas d'utilisation qu'ils peuvent exécuter
    \item \textbf{Relations d'inclusion (<<include>>)} : Flèches pointillées indiquant les dépendances obligatoires
    \item \textbf{Relations d'extension (<<extend>>)} : Flèches pointillées représentant les extensions optionnelles
\end{itemize}

Cette représentation UML conforme aux standards facilite la compréhension des interactions dans le contexte de la gestion de capacité serveurs, en distinguant clairement les rôles des différents acteurs et leurs accès aux fonctionnalités de la plateforme.

\subsection{Diagrammes de séquence}

Le diagramme de séquence suivant illustre le processus de collecte de métriques depuis Zabbix.

\clearpage
\thispagestyle{empty}
\begin{figure}[p]
\centering
\vspace*{-1.5cm}
\includegraphics[width=0.95\textwidth,height=0.9\textheight,keepaspectratio]{images/sequence-collecte-metriques.png}
\vspace{1cm}
\caption{Diagramme de séquence - Collecte de métriques}
\label{fig:sequence-collecte}
\end{figure}
\clearpage

\textbf{Description du diagramme :} Ce diagramme de séquence UML détaille le workflow complet de collecte et d'affichage des métriques de capacité serveur. Il implique 4 participants : le \textbf{Frontend Angular}, le \textbf{Backend FastAPI}, l'\textbf{API Zabbix}, et la \textbf{Base de Données Oracle}. Le processus commence par la récupération de la liste des serveurs depuis la base de données, puis se poursuit par une requête de métriques mensuelles. Le backend établit une session authentifiée avec Zabbix, récupère l'ID du serveur, identifie les items de métriques (CPU, Memory, Swap), collecte les données de tendance horaires pour la période demandée, traite et transforme ces données brutes, puis les retourne au frontend. Le frontend met ensuite à jour les graphiques et affiche les métriques dans les composants de visualisation. Ce workflow illustre l'intégration native avec l'infrastructure Zabbix existante et la transformation des données pour l'analyse de capacité.

\subsection{Modèle de données}

Le modèle de données relationnel définit les principales entités du système et leurs relations.

\clearpage
\thispagestyle{empty}
\begin{figure}[p]
\centering
\vspace*{-1.5cm}
\includegraphics[width=0.95\textwidth,height=0.9\textheight,keepaspectratio]{images/modele-donnees.png}
\vspace{1cm}
\caption{Modèle de données relationnel}
\label{fig:modele-donnees}
\end{figure}
\clearpage

\textbf{Description du diagramme :} Ce diagramme entité-relation (ERD) présente le modèle de données Oracle de \projectTitle\ avec 6 entités principales et leurs relations. L'entité centrale \textbf{Server} contient les métadonnées statiques des serveurs (hostname, type, ressources CPU/RAM/Swap, client, environnement). L'entité \textbf{UsageDataPoint} stocke les points de données d'utilisation en temps réel collectés depuis Zabbix. L'entité \textbf{MonthlyData} contient les données agrégées mensuelles (moyennes et maximums) pour optimiser les rapports de capacité. L'entité \textbf{Alert} gère les alertes générées par dépassement de seuils avec leur statut et sévérité. L'entité \textbf{Threshold} définit les seuils configurables (warning/critical) pour les différents types de métriques. L'entité \textbf{ClientConf} stocke les configurations clients synchronisées depuis MAIA CMDB, incluant les informations détaillées sur les environnements clients (custconflabel, clientname, progiciel, platform, envtype) et l'inventaire des serveurs associés (webserver, treatmentserver, dbserver). Les relations illustrent les cardinalités : un serveur génère plusieurs points de données, déclenche plusieurs alertes, et produit des données mensuelles agrégées. Ce modèle supporte efficacement les fonctionnalités de gestion de capacité, d'analyse temporelle, de reporting automatisé, et d'organisation par configuration client via l'intégration MAIA CMDB.

\section{Choix technologiques}

\subsection{Frontend - Angular 19}

Le frontend de \projectTitle\ est développé avec Angular 19 en utilisant des composants autonomes (standalone), offrant une architecture moderne et modulaire :

\begin{itemize}
    \item \textbf{Thème Modern Dark Dashboard} : Interface sombre moderne avec palette de couleurs cohérente
    \item \textbf{Composants autonomes} : Architecture modulaire sans modules NgModule traditionnels
    \item \textbf{Graphiques intégrés} : Visualisation de données avec bibliothèques de graphiques et composants personnalisés
    \item \textbf{Angular Material} : Composants UI avec personnalisation du thème
    \item \textbf{TypeScript strict} : Typage fort pour une meilleure maintenabilité
    \item \textbf{SCSS modulaire} : Styles organisés avec variables et mixins centralisés
\end{itemize}

\subsection{Backend - FastAPI}

Le backend \projectTitle\ utilise FastAPI avec une architecture modulaire organisée en routers spécialisés :

\begin{table}[H]
\centering
\footnotesize % <-- Réduction de la taille de police
\renewcommand{\arraystretch}{1.3}
\begin{tabular}{|p{3cm}|p{5cm}|p{5cm}|}
\hline
\textbf{Router} & \textbf{Responsabilité} & \textbf{Endpoints principaux} \\
\hline
server\_info & Gestion des informations serveurs & /get-servers, \newline /sync-hostnames \\
\hline
server\_usage & Métriques d'utilisation & /server-usage-per-month \\
\hline
server\_comparison & Comparaison d'usage & /compare-server-usage \\
\hline
threshold & Gestion des seuils & /servers-usage-threshold \\
\hline
reporting\_capacity & Génération de rapports & /generate-capacity-report \\
\hline
\end{tabular}
\caption{Routers backend implémentés}
\label{tab:routers-backend}
\end{table}



\subsection{Base de données - Oracle}

La solution utilise une base de données Oracle pour stocker les informations sur les serveurs et leurs métriques, offrant :

\begin{itemize}
    \item Support pour les requêtes complexes et les fonctions d'agrégation
    \item Haute disponibilité et réplication
    \item Support pour les transactions ACID
    \item Intégration avec l'écosystème Oracle existant de l'entreprise
\end{itemize}

\subsection{Intégration Zabbix}

L'intégration avec Zabbix constitue le cœur du système de collecte de données :

\begin{itemize}
    \item \textbf{PyZabbix} : Bibliothèque Python pour l'interaction avec l'API Zabbix
    \item \textbf{Authentification sécurisée} : Gestion des sessions et des identifiants
    \item \textbf{Collecte de métriques} : CPU, mémoire, swap avec données historiques
    \item \textbf{Traitement parallèle} : ThreadPoolExecutor pour optimiser les performances
    \item \textbf{Gestion d'erreurs} : Robustesse face aux indisponibilités temporaires
\end{itemize}

\subsection{Intégration MAIA CMDB}

L'intégration avec MAIA CMDB enrichit \projectTitle\ avec la gestion des configurations clients :

\begin{itemize}
    \item \textbf{PyMySQL} : Bibliothèque Python pour l'accès à la base MySQL MAIA
    \item \textbf{Proxy SOCKS} : Connexion sécurisée via proxy pour accéder à MAIA CMDB
    \item \textbf{Synchronisation automatique} : Transfert des configurations clients vers Oracle
    \item \textbf{Gestion des environnements} : Organisation des serveurs par client et environnement
    \item \textbf{Inventaire centralisé} : Mapping des serveurs aux configurations clients
\end{itemize}

\subsection{Architecture de déploiement OpenShift}

La solution \projectTitle\ est conçue pour être déployée sur l'infrastructure OpenShift de \companyName\ selon une architecture cloud-native moderne :

\subsubsection{Containerisation Docker}

L'application est packagée en conteneurs Docker pour assurer la portabilité et la scalabilité :

\begin{itemize}
    \item \textbf{Frontend Angular 19} : Image Docker basée sur Nginx pour servir l'application statique
    \item \textbf{Backend FastAPI} : Image Docker Python avec toutes les dépendances et optimisations
    \item \textbf{Configuration externalisée} : Variables d'environnement et ConfigMaps pour la configuration
    \item \textbf{Multi-stage builds} : Optimisation de la taille des images avec builds en plusieurs étapes
    \item \textbf{Sécurité} : Images basées sur des distributions minimales et scannées pour les vulnérabilités
\end{itemize}

\subsubsection{Orchestration OpenShift}

Le déploiement sur OpenShift exploite les capacités Kubernetes d'entreprise :

\begin{itemize}
    \item \textbf{Namespaces} : Séparation des environnements (dev, staging, production)
    \item \textbf{Deployments} : Gestion des versions et rollbacks automatiques
    \item \textbf{Services} : Exposition des applications avec load balancing
    \item \textbf{Routes} : Accès externe sécurisé avec certificats SSL/TLS
    \item \textbf{Persistent Volumes} : Stockage persistant pour les données et logs
    \item \textbf{Resource Quotas} : Limitation et monitoring des ressources consommées
\end{itemize}

\subsubsection{Pipeline CI/CD GitLab}

L'automatisation du déploiement suit les meilleures pratiques DevOps :

\begin{itemize}
    \item \textbf{Build automatique} : Construction des images Docker à chaque commit
    \item \textbf{Tests automatisés} : Exécution des tests unitaires et d'intégration
    \item \textbf{Déploiement progressif} : Blue-green deployment pour minimiser les interruptions
    \item \textbf{Monitoring continu} : Surveillance des métriques de performance et d'erreurs
    \item \textbf{Rollback automatique} : Retour à la version précédente en cas de problème
\end{itemize}

\subsubsection{Observabilité et monitoring}

L'infrastructure de monitoring assure la visibilité complète du système :

\begin{itemize}
    \item \textbf{Métriques Prometheus} : Collecte des métriques applicatives et infrastructure
    \item \textbf{Dashboards Grafana} : Visualisation des performances et alertes
    \item \textbf{Logging ELK Stack} : Centralisation et analyse des logs applicatifs
    \item \textbf{Tracing distribué} : Suivi des requêtes à travers les microservices
    \item \textbf{Alerting intelligent} : Notifications proactives basées sur des seuils métier
\end{itemize}

\subsection{Roadmap Intelligence Artificielle}

L'architecture actuelle prépare l'intégration future d'algorithmes d'intelligence artificielle pour l'analyse prédictive :

\begin{itemize}
    \item \textbf{CatBoost} : Algorithme de gradient boosting pour la prédiction de capacité
    \item \textbf{Analyse prédictive} : Anticipation des surcharges et besoins en ressources
    \item \textbf{Recommandations automatiques} : Suggestions de provisioning et d'optimisation
    \item \textbf{Détection d'anomalies} : Identification proactive des comportements inhabituels
    \item \textbf{Planification capacitaire} : Aide à la décision pour l'évolution de l'infrastructure
\end{itemize}

\clearpage
