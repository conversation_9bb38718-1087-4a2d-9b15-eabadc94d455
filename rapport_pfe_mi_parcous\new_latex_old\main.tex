%========= Main LaTeX Document for CloudCapacity Report =========%
%                                                                 %
% CloudCapacity - Plateforme de Gestion de Capacité Serveurs    %
%                                                                 %
% Author: MAHMOUD Mohamed Amine                                   %
% Project: Projet de Fin d'Études                                %
% Company: Sopra HR Software                                     %
% Academic Year: 2024-2025                                       %
%=================================================================%

% !TEX encoding = UTF-8
\documentclass[12pt,a4paper]{report}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Packages nécessaires
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage[french]{babel}
% Font configuration for better readability
% Using Palatino font family - highly recommended for academic documents
\usepackage{mathpazo} % Palatino font with matching math fonts
\usepackage[scaled=0.95]{helvet} % Helvetica for sans-serif
\usepackage{courier} % Courier for monospace
\renewcommand{\familydefault}{\rmdefault}
\usepackage{graphicx}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{setspace}
\usepackage{titlesec}
\usepackage{titletoc}
\usepackage{array}
\usepackage{tabularx}
\usepackage{booktabs}
\usepackage{fancyhdr}
\usepackage{lipsum}
\usepackage{enumitem}
% \usepackage{listings} % Supprimé - pas de code dans le document
\usepackage{caption}
\usepackage{float}
\usepackage{geometry}
\usepackage{pifont}
\usepackage{tikz}
\usepackage[style=numeric,sorting=none,backend=biber]{biblatex}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Configuration de la page
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\geometry{top=2.5cm, bottom=2.5cm, left=2.5cm, right=2.5cm}
\setlength{\parindent}{0pt}
\setlength{\parskip}{6pt}
\onehalfspacing

% Configuration des images
\graphicspath{{./images/}}

% Configuration de la bibliographie
\addbibresource{references.bib}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Configuration globale du document
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\input{global_config}

% Configuration des liens hypertexte
\hypersetup{
    colorlinks=true,
    linkcolor=primaryBlue,
    filecolor=magenta,
    urlcolor=primaryBlue,
    citecolor=primaryBlue,
    pdftitle={\documentTitle},
    pdfauthor={\documentAuthor},
    pdfsubject={\documentSubject},
    pdfkeywords={\documentKeywords}
}

% Configuration des en-têtes et pieds de page
\pagestyle{fancy}
\fancyhf{}
\fancyhead[L]{\leftmark}
\fancyhead[R]{\thepage}
\fancyfoot[C]{Projet de Fin d'Études - \projectTitle}
\renewcommand{\footrulewidth}{0.4pt}

% Configuration des titres de chapitres
\titleformat{\chapter}[display]
{\normalfont\huge\bfseries\color{primaryBlue}}
{\chaptertitlename\ \thechapter}{20pt}{\Huge}
\titlespacing*{\chapter}{0pt}{50pt}{40pt}

% Configuration supprimée - pas de code dans le document

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Document principal
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Pages préliminaires
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\frontmatter

% Page de garde
\input{tpl/title_page}

% Dédicaces
\input{tpl/dedicaces}

% Remerciements
\input{tpl/remerciements}

% Résumé
\input{tpl/resume_fr}
% \input{tpl/resume_en} % English Abstract removed - French résumé only

% Liste des abréviations
\input{tpl/abbreviations}

% Tables des matières
\tableofcontents
\clearpage

% Liste des figures
\listoffigures
\clearpage

% Liste des tableaux
\listoftables
\clearpage

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Corps du document
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\mainmatter

% Introduction générale
\input{introduction}

% Chapitre 1: Cadre général du projet
\input{chap_01}

% Chapitre 2: Étude de l'existant et analyse des besoins
\input{chap_02}

% Chapitre 3: Conception et architecture
\input{chap_03}

% Chapitre 4: Réalisation et implémentation
\input{chap_04}

% Chapitre 5: Tests et validation
\input{chap_05}

% Chapitre 6: Bilan et perspectives
\input{chap_06}

% Conclusion générale
\input{conclusion}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Bibliographie
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\printbibliography[heading=bibintoc,title=Bibliographie]

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Annexes (si nécessaire)
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\appendix
% \input{annexes} % Décommentez si vous avez des annexes

\end{document}
