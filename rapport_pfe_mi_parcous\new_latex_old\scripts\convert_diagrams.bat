@echo off
REM Batch script to convert Mermaid diagrams to PNG for Windows
REM Requires Node.js and npm to be installed

echo === Mermaid to PNG Converter for LaTeX (Windows) ===
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js and npm are available

REM Check if mermaid-cli is installed
mmdc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Mermaid CLI not found. Installing...
    npm install -g @mermaid-js/mermaid-cli
    if %errorlevel% neq 0 (
        echo Failed to install Mermaid CLI
        pause
        exit /b 1
    )
    echo Mermaid CLI installed successfully
) else (
    echo Mermaid CLI is already installed
)

REM Create images directory
if not exist "images" mkdir images

echo.
echo Converting diagrams...

REM Convert architecture diagram (optimized for vertical layout)
if exist "..\web\diagrams\architecture-generale.mmd" (
    mmdc -i "..\web\diagrams\architecture-generale.mmd" -o "images\architecture-generale.png" -w 1000 -H 1600 --backgroundColor white --theme default
    if %errorlevel% equ 0 (
        echo ✓ Converted architecture-generale.mmd
    ) else (
        echo ✗ Failed to convert architecture-generale.mmd
    )
) else (
    echo ✗ architecture-generale.mmd not found
)

REM Convert use case diagram (optimized for vertical layout)
if exist "..\web\diagrams\cas-utilisation.mmd" (
    mmdc -i "..\web\diagrams\cas-utilisation.mmd" -o "images\cas-utilisation.png" -w 1000 -H 1400 --backgroundColor white --theme default
    if %errorlevel% equ 0 (
        echo ✓ Converted cas-utilisation.mmd
    ) else (
        echo ✗ Failed to convert cas-utilisation.mmd
    )
) else (
    echo ✗ cas-utilisation.mmd not found
)

REM Convert sequence diagram (optimized for vertical layout)
if exist "..\web\diagrams\sequence-collecte-metriques.mmd" (
    mmdc -i "..\web\diagrams\sequence-collecte-metriques.mmd" -o "images\sequence-collecte-metriques.png" -w 1000 -H 1200 --backgroundColor white --theme default
    if %errorlevel% equ 0 (
        echo ✓ Converted sequence-collecte-metriques.mmd
    ) else (
        echo ✗ Failed to convert sequence-collecte-metriques.mmd
    )
) else (
    echo ✗ sequence-collecte-metriques.mmd not found
)

REM Convert data model diagram (optimized for square format)
if exist "..\web\diagrams\modele-donnees.mmd" (
    mmdc -i "..\web\diagrams\modele-donnees.mmd" -o "images\modele-donnees.png" -w 1200 -H 1400 --backgroundColor white --theme default
    if %errorlevel% equ 0 (
        echo ✓ Converted modele-donnees.mmd
    ) else (
        echo ✗ Failed to convert modele-donnees.mmd
    )
) else (
    echo ✗ modele-donnees.mmd not found
)

echo.
echo === Conversion Complete ===
echo Images saved in the 'images' directory
echo You can now compile your LaTeX document with the generated PNG images
echo.
pause
