# Script de Validation - Externalisation CSS
# Vérifie que tous les styles ont été externalisés du fichier HTML

param(
    [string]$HtmlFile = "rapport_latex.html"
)

Write-Host "=== Validation de l'Externalisation CSS ===" -ForegroundColor Cyan
Write-Host ""

# Vérification de l'existence du fichier
if (-not (Test-Path $HtmlFile)) {
    Write-Host "❌ Fichier HTML non trouvé: $HtmlFile" -ForegroundColor Red
    exit 1
}

$errors = @()
$warnings = @()
$success = @()

# 1. Vérifier l'absence de balises <style>
Write-Host "🔍 Vérification des balises <style>..." -ForegroundColor Yellow
$styleTagsCount = (Select-String -Path $HtmlFile -Pattern '<style>' -AllMatches).Count
if ($styleTagsCount -eq 0) {
    $success += "✅ Aucune balise <style> trouvée"
} else {
    $errors += "❌ $styleTagsCount balise(s) <style> trouvée(s)"
}

# 2. Vérifier l'absence d'attributs style=""
Write-Host "🔍 Vérification des attributs style inline..." -ForegroundColor Yellow
$inlineStylesCount = (Select-String -Path $HtmlFile -Pattern 'style=' -AllMatches).Count
if ($inlineStylesCount -eq 0) {
    $success += "✅ Aucun style inline trouvé"
} else {
    $errors += "❌ $inlineStylesCount attribut(s) style inline trouvé(s)"
}

# 3. Vérifier la présence du lien vers le CSS externe
Write-Host "🔍 Vérification du lien CSS externe..." -ForegroundColor Yellow
$cssLinkCount = (Select-String -Path $HtmlFile -Pattern 'href="styles/rapport-styles.css"' -AllMatches).Count
if ($cssLinkCount -gt 0) {
    $success += "✅ Lien vers CSS externe trouvé"
} else {
    $errors += "❌ Lien vers CSS externe manquant"
}

# 4. Vérifier l'existence du fichier CSS externe
Write-Host "🔍 Vérification du fichier CSS externe..." -ForegroundColor Yellow
if (Test-Path "styles/rapport-styles.css") {
    $cssSize = (Get-Item "styles/rapport-styles.css").Length
    $success += "✅ Fichier CSS externe existe ($cssSize bytes)"
} else {
    $errors += "❌ Fichier CSS externe manquant: styles/rapport-styles.css"
}

# 5. Vérifier la présence du script JavaScript externe
Write-Host "🔍 Vérification du script JavaScript externe..." -ForegroundColor Yellow
$jsLinkCount = (Select-String -Path $HtmlFile -Pattern 'src="scripts/diagram-loader.js"' -AllMatches).Count
if ($jsLinkCount -gt 0) {
    $success += "✅ Lien vers script JavaScript trouvé"
} else {
    $warnings += "⚠️ Lien vers script JavaScript manquant"
}

# 6. Vérifier l'existence du fichier JavaScript externe
Write-Host "🔍 Vérification du fichier JavaScript externe..." -ForegroundColor Yellow
if (Test-Path "scripts/diagram-loader.js") {
    $jsSize = (Get-Item "scripts/diagram-loader.js").Length
    $success += "✅ Fichier JavaScript externe existe ($jsSize bytes)"
} else {
    $warnings += "⚠️ Fichier JavaScript externe manquant: scripts/diagram-loader.js"
}

# 7. Analyser le contenu HTML pour détecter des styles cachés
Write-Host "🔍 Analyse approfondie du contenu HTML..." -ForegroundColor Yellow
$htmlContent = Get-Content $HtmlFile -Raw

# Recherche de patterns de style cachés
$hiddenStylePatterns = @(
    'background:',
    'color:',
    'margin:',
    'padding:',
    'font-size:',
    'text-align:',
    'display:',
    'position:'
)

$hiddenStylesFound = 0
foreach ($pattern in $hiddenStylePatterns) {
    $matches = [regex]::Matches($htmlContent, $pattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    $hiddenStylesFound += $matches.Count
}

if ($hiddenStylesFound -eq 0) {
    $success += "✅ Aucun style CSS caché détecté"
} else {
    $warnings += "⚠️ $hiddenStylesFound propriété(s) CSS potentielle(s) détectée(s) dans le HTML"
}

# 8. Vérifier la taille du fichier HTML (doit être plus petit sans CSS)
Write-Host "🔍 Analyse de la taille du fichier..." -ForegroundColor Yellow
$htmlSize = (Get-Item $HtmlFile).Length
if ($htmlSize -lt 200000) { # Moins de 200KB
    $success += "✅ Taille du fichier HTML optimisée ($htmlSize bytes)"
} else {
    $warnings += "⚠️ Fichier HTML volumineux ($htmlSize bytes) - vérifier l'optimisation"
}

# Affichage des résultats
Write-Host ""
Write-Host "📊 RÉSULTATS DE LA VALIDATION" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

if ($success.Count -gt 0) {
    Write-Host ""
    Write-Host "✅ SUCCÈS ($($success.Count)):" -ForegroundColor Green
    foreach ($item in $success) {
        Write-Host "  $item" -ForegroundColor Green
    }
}

if ($warnings.Count -gt 0) {
    Write-Host ""
    Write-Host "⚠️ AVERTISSEMENTS ($($warnings.Count)):" -ForegroundColor Yellow
    foreach ($item in $warnings) {
        Write-Host "  $item" -ForegroundColor Yellow
    }
}

if ($errors.Count -gt 0) {
    Write-Host ""
    Write-Host "❌ ERREURS ($($errors.Count)):" -ForegroundColor Red
    foreach ($item in $errors) {
        Write-Host "  $item" -ForegroundColor Red
    }
}

# Conclusion
Write-Host ""
if ($errors.Count -eq 0) {
    Write-Host "🎉 VALIDATION RÉUSSIE - CSS correctement externalisé !" -ForegroundColor Green
    Write-Host "   Le fichier HTML ne contient plus de styles intégrés." -ForegroundColor Green
    exit 0
} else {
    Write-Host "💥 VALIDATION ÉCHOUÉE - Des problèmes ont été détectés." -ForegroundColor Red
    Write-Host "   Veuillez corriger les erreurs avant de continuer." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== Fin de la Validation ===" -ForegroundColor Cyan
