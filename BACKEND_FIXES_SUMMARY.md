# CloudCapacity Backend Issues - Diagnosis and Fixes

## 🔍 Issues Identified

Based on the logs and configuration analysis, the main issues are:

### 1. **URL Routing Problem**
- **Frontend expects**: `https://becloudcapacity.soprahronline.sopra/capacity/*`
- **Backend runs on**: `0.0.0.0:9099` (inside container)
- **Missing**: Reverse proxy or load balancer configuration

### 2. **SSL Configuration Issues**
- Backend tries to use SSL in production but may have certificate problems
- SSL files exist but might not be properly configured for the domain

### 3. **Environment Configuration**
- `APP_ENV=production` enables SSL but external routing might not work
- Missing health check endpoints for debugging

## 🛠️ Fixes Applied

### 1. **Enhanced Backend Logging and Error Handling**

**File**: `be_cloudcapacity/asgi.py`
- ✅ Added detailed startup logging
- ✅ Added SSL file existence checks
- ✅ Improved error handling for SSL configuration
- ✅ Added environment variable logging

**File**: `be_cloudcapacity/api/app.py`
- ✅ Added root health check endpoint (`/`)
- ✅ Added dedicated health endpoint (`/health`)
- ✅ Enhanced database initialization error handling
- ✅ Added environment information in responses

### 2. **Improved Docker Configuration**

**File**: `be_cloudcapacity/Dockerfile`
- ✅ Added health check with proper endpoint
- ✅ Added non-root user for security
- ✅ Set proper environment variables
- ✅ Improved logging configuration

### 3. **Updated Configuration**

**File**: `be_cloudcapacity/api/config.py`
- ✅ Added debug configuration options
- ✅ Updated version to 0.3
- ✅ Added log level configuration

**File**: `fe_cloudcapacity/src/environments/environment.prod.ts`
- ✅ Fixed API URL configuration (removed duplicate `/capacity`)
- ✅ Added debug mode flag

### 4. **Debug Tools Created**

**Files Created**:
- ✅ `be_cloudcapacity/debug_backend.py` - Comprehensive backend diagnostics
- ✅ `be_cloudcapacity/start_debug.py` - Debug startup script
- ✅ `test_backend_connection.py` - External connectivity testing

## 🚀 Next Steps for Deployment

### 1. **Test Backend Locally**
```bash
cd be_cloudcapacity
python debug_backend.py
```

### 2. **Start Backend in Debug Mode**
```bash
cd be_cloudcapacity
python start_debug.py
```

### 3. **Test External Connectivity**
```bash
python test_backend_connection.py
```

### 4. **Check OpenShift/OKD Configuration**

The main issue is likely in the OpenShift routing configuration. Check:

1. **Service Configuration**:
   ```yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: becloudcapacity-service
   spec:
     selector:
       app: becloudcapacity
     ports:
     - port: 80
       targetPort: 9099
       protocol: TCP
   ```

2. **Route Configuration**:
   ```yaml
   apiVersion: route.openshift.io/v1
   kind: Route
   metadata:
     name: becloudcapacity-route
   spec:
     host: becloudcapacity.soprahronline.sopra
     to:
       kind: Service
       name: becloudcapacity-service
     port:
       targetPort: 9099
     tls:
       termination: edge
   ```

### 5. **Verify Container Health**

In OpenShift, check:
```bash
# Check pod status
oc get pods -l app=becloudcapacity

# Check pod logs
oc logs -f deployment/becloudcapacity

# Check service endpoints
oc get endpoints becloudcapacity-service

# Test internal connectivity
oc exec -it <pod-name> -- curl http://localhost:9099/health
```

## 🔧 Troubleshooting Commands

### Backend Container Debugging
```bash
# Check if backend is responding internally
curl -k https://localhost:9099/health
curl http://localhost:9099/health

# Check SSL certificates
openssl x509 -in ca/soprahronline.sopra.cer -text -noout

# Check database connectivity
python -c "from api.database import engine; print(engine.execute('SELECT 1 FROM DUAL').scalar())"
```

### Network Debugging
```bash
# Check port binding
netstat -tlnp | grep 9099

# Check DNS resolution
nslookup becloudcapacity.soprahronline.sopra

# Test external connectivity
curl -k https://becloudcapacity.soprahronline.sopra/health
```

## 📋 Expected Behavior After Fixes

1. **Backend Health Endpoints**:
   - `GET /` - Returns API information and status
   - `GET /health` - Returns health check status
   - Both endpoints return JSON with version and environment info

2. **Enhanced Logging**:
   - Detailed startup information
   - SSL configuration status
   - Database connection status
   - Environment variable values

3. **Better Error Handling**:
   - Graceful SSL fallback
   - Database connection error reporting
   - Detailed error messages in logs

4. **Container Health Checks**:
   - Automatic health monitoring
   - Proper restart behavior on failures

## 🎯 Root Cause Analysis

The most likely root cause is **missing or misconfigured reverse proxy/load balancer** in the OpenShift environment. The backend is running correctly (as shown in logs), but external traffic is not being routed to the container.

**Recommendation**: Check OpenShift Route and Service configurations to ensure traffic to `https://becloudcapacity.soprahronline.sopra` is properly forwarded to the backend container on port 9099.
