# Script de Gestion du Rapport PFE CloudCapacity
# Auteur: MAHMOUD Mohamed Amine
# Date: Décembre 2024

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("open", "validate", "backup", "help")]
    [string]$Action = "help"
)

# Configuration
$ReportPath = $PSScriptRoot
$MainReport = Join-Path $ReportPath "rapport_latex.html"
$IndexPage = Join-Path $ReportPath "index.html"
$BackupDir = Join-Path $ReportPath "backups"

function Show-Help {
    Write-Host "=== Script de Gestion du Rapport PFE CloudCapacity ===" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\manage-rapport.ps1 -Action <action>" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Actions disponibles:" -ForegroundColor Green
    Write-Host "  open      - Ouvre le rapport dans le navigateur" -ForegroundColor White
    Write-Host "  validate  - Vérifie l'intégrité du rapport" -ForegroundColor White
    Write-Host "  backup    - Crée une sauvegarde du rapport" -ForegroundColor White
    Write-Host "  help      - Affiche cette aide" -ForegroundColor White
    Write-Host ""
    Write-Host "Exemples:" -ForegroundColor Green
    Write-Host "  .\manage-rapport.ps1 -Action open" -ForegroundColor Gray
    Write-Host "  .\manage-rapport.ps1 -Action validate" -ForegroundColor Gray
    Write-Host "  .\manage-rapport.ps1 -Action backup" -ForegroundColor Gray
}

function Open-Report {
    Write-Host "🌐 Ouverture du rapport PFE CloudCapacity..." -ForegroundColor Green
    
    if (Test-Path $IndexPage) {
        Start-Process $IndexPage
        Write-Host "✅ Page d'index ouverte dans le navigateur" -ForegroundColor Green
    } elseif (Test-Path $MainReport) {
        Start-Process $MainReport
        Write-Host "✅ Rapport principal ouvert dans le navigateur" -ForegroundColor Green
    } else {
        Write-Host "❌ Aucun fichier de rapport trouvé" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Test-ReportIntegrity {
    Write-Host "🔍 Validation de l'intégrité du rapport..." -ForegroundColor Green
    
    $errors = @()
    $warnings = @()
    
    # Vérification des fichiers principaux
    $requiredFiles = @(
        "rapport_latex.html",
        "index.html",
        "README.md",
        "styles/rapport-styles.css",
        "scripts/diagram-loader.js"
    )
    
    foreach ($file in $requiredFiles) {
        $filePath = Join-Path $ReportPath $file
        if (-not (Test-Path $filePath)) {
            $errors += "Fichier manquant: $file"
        } else {
            Write-Host "✅ $file" -ForegroundColor Green
        }
    }
    
    # Vérification des diagrammes
    $diagramFiles = @(
        "diagrams/architecture-generale.mmd",
        "diagrams/cas-utilisation.mmd",
        "diagrams/sequence-collecte-metriques.mmd",
        "diagrams/modele-donnees.mmd"
    )
    
    foreach ($diagram in $diagramFiles) {
        $diagramPath = Join-Path $ReportPath $diagram
        if (-not (Test-Path $diagramPath)) {
            $errors += "Diagramme manquant: $diagram"
        } else {
            Write-Host "✅ $diagram" -ForegroundColor Green
        }
    }
    
    # Vérification du contenu HTML
    if (Test-Path $MainReport) {
        $content = Get-Content $MainReport -Raw
        
        # Vérification des liens vers les fichiers externes
        if ($content -notmatch 'href="styles/rapport-styles.css"') {
            $warnings += "Lien CSS manquant dans le rapport principal"
        }
        
        if ($content -notmatch 'src="scripts/diagram-loader.js"') {
            $warnings += "Lien JavaScript manquant dans le rapport principal"
        }
        
        # Vérification des IDs de diagrammes
        $diagramIds = @("architecture-generale", "cas-utilisation", "sequence-collecte", "modele-donnees")
        foreach ($id in $diagramIds) {
            if ($content -notmatch "id=`"$id`"") {
                $warnings += "ID de diagramme manquant: $id"
            }
        }
    }
    
    # Affichage des résultats
    Write-Host ""
    if ($errors.Count -eq 0) {
        Write-Host "✅ Validation réussie - Aucune erreur détectée" -ForegroundColor Green
    } else {
        Write-Host "❌ Erreurs détectées:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  - $error" -ForegroundColor Red
        }
    }
    
    if ($warnings.Count -gt 0) {
        Write-Host "⚠️ Avertissements:" -ForegroundColor Yellow
        foreach ($warning in $warnings) {
            Write-Host "  - $warning" -ForegroundColor Yellow
        }
    }
    
    # Statistiques
    Write-Host ""
    Write-Host "📊 Statistiques du rapport:" -ForegroundColor Cyan
    if (Test-Path $MainReport) {
        $content = Get-Content $MainReport -Raw
        $wordCount = ($content -split '\s+').Count
        $lineCount = (Get-Content $MainReport).Count
        Write-Host "  - Lignes: $lineCount" -ForegroundColor White
        Write-Host "  - Mots (approximatif): $wordCount" -ForegroundColor White
    }
    
    return ($errors.Count -eq 0)
}

function Backup-Report {
    Write-Host "💾 Création d'une sauvegarde du rapport..." -ForegroundColor Green
    
    # Création du dossier de sauvegarde
    if (-not (Test-Path $BackupDir)) {
        New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    }
    
    # Nom de la sauvegarde avec timestamp
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupName = "rapport_pfe_backup_$timestamp"
    $backupPath = Join-Path $BackupDir $backupName
    
    try {
        # Copie de tous les fichiers
        Copy-Item -Path $ReportPath -Destination $backupPath -Recurse -Exclude "backups" -Force
        
        # Création d'un fichier ZIP
        $zipPath = "$backupPath.zip"
        Compress-Archive -Path $backupPath -DestinationPath $zipPath -Force
        
        # Suppression du dossier temporaire
        Remove-Item -Path $backupPath -Recurse -Force
        
        Write-Host "✅ Sauvegarde créée: $zipPath" -ForegroundColor Green
        
        # Nettoyage des anciennes sauvegardes (garde les 5 plus récentes)
        $oldBackups = Get-ChildItem -Path $BackupDir -Filter "*.zip" | Sort-Object CreationTime -Descending | Select-Object -Skip 5
        if ($oldBackups) {
            foreach ($backup in $oldBackups) {
                Remove-Item -Path $backup.FullName -Force
                Write-Host "🗑️ Ancienne sauvegarde supprimée: $($backup.Name)" -ForegroundColor Gray
            }
        }
        
        return $true
    } catch {
        Write-Host "❌ Erreur lors de la sauvegarde: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Exécution de l'action demandée
switch ($Action) {
    "open" {
        Open-Report
    }
    "validate" {
        Test-ReportIntegrity
    }
    "backup" {
        Backup-Report
    }
    "help" {
        Show-Help
    }
    default {
        Show-Help
    }
}

Write-Host ""
Write-Host "=== Rapport PFE CloudCapacity - MAHMOUD Mohamed Amine ===" -ForegroundColor Cyan
