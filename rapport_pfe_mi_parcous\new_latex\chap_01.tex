%========= Chapitre 1: Cadre général du projet =========%
%                                                       %
% Chapter 1: Project framework and context             %
%=======================================================%

\chapter{Cadre général du projet}

\section{Présentation de l'organisme d'accueil}

\subsection{Sopra HR Software}

\companyName\ est une filiale de Sopra Steria Group spécialisée dans les solutions de gestion des ressources humaines. L'entreprise accompagne plus de 900 clients dans 54 pays et propose des solutions complètes pour la digitalisation RH, allant de la gestion administrative à l'expérience collaborateur, en passant par la gestion des talents.

Disposant d'une infrastructure informatique conséquente pour héberger ses solutions, \companyName\ maintient un parc de serveurs on-premise répartis sur plusieurs sites. La performance et la disponibilité de ces serveurs sont cruciales pour garantir la qualité de service offerte aux clients.

\subsection{Pôle DevOps \& Infrastructure}

La \companyDivision\ de \companyName\ est responsable de la gestion, du suivi de capacité et de l'optimisation de l'ensemble de l'infrastructure technique. Cette équipe multidisciplinaire assure la continuité de service, la sécurité des données et l'évolution technologique des plateformes hébergeant les solutions RH.

\section{Contexte et problématique}

\subsection{Situation actuelle}

La gestion actuelle de la capacité des serveurs chez \companyName\ présente plusieurs limitations qui impactent l'efficacité opérationnelle et la prise de décision stratégique :

\begin{itemize}
    \item Collecte manuelle des métriques de performance, impliquant un temps considérable pour les équipes techniques
    \item Absence de centralisation des données de performance, rendant difficile l'analyse globale de l'infrastructure
    \item Génération manuelle des rapports de capacité, sujette aux erreurs et chronophage
    \item Manque d'outils prédictifs permettant d'anticiper les besoins en ressources
    \item Difficultés à corréler les incidents avec les métriques de performance pour une analyse approfondie
\end{itemize}

\subsection{Problèmes identifiés}

Ces limitations entraînent des risques opérationnels significatifs :

\begin{itemize}
    \item Lenteurs potentielles dans les applications dues à une capacité insuffisante
    \item Interruptions de service non anticipées impactant la satisfaction client
    \item Allocation sous-optimale des ressources, générant des coûts d'infrastructure supplémentaires
    \item Temps de réaction élevé face aux problèmes de capacité
    \item Difficultés de planification des évolutions d'infrastructure
\end{itemize}

\section{Objectifs du projet}

Le projet \projectTitle\ vise à répondre aux besoins identifiés en développant une solution intégrée qui permettra de :

\begin{itemize}
    \item \textbf{Automatiser le reporting de capacité} : Développer une plateforme web Angular 19 permettant de suivre en temps réel les métriques des serveurs (CPU, mémoire, swap) et de générer des rapports de capacité personnalisés
    \item \textbf{Centraliser la gestion de capacité} : Offrir une vue unifiée de l'ensemble des métriques de performance à travers des tableaux de bord dynamiques avec interface Modern Dark Dashboard
    \item \textbf{Intégrer l'écosystème existant} : Exploiter l'infrastructure Zabbix existante via une API FastAPI connectée à la base de données Oracle
    \item \textbf{Améliorer la réactivité} : Mettre en place un système d'alertes basé sur des seuils configurables pour informer rapidement les équipes techniques
    \item \textbf{Faciliter la prise de décision} : Fournir des analyses comparatives entre serveurs et périodes, avec préparation pour l'intégration future d'algorithmes CatBoost
    \item \textbf{Moderniser l'infrastructure} : Déployer la solution sur OpenShift avec pipeline CI/CD GitLab pour assurer la scalabilité et la maintenabilité
\end{itemize}

\section{Méthodologie de travail}

\subsection{Approche Agile Hybrid}

La réalisation du projet \projectTitle\ adopte une méthodologie Agile Hybrid adaptée au développement solo pour assurer une livraison itérative et une adaptation continue aux besoins métier. Cette approche minimaliste se caractérise par :

\begin{itemize}
    \item \textbf{Objectifs hebdomadaires} : Planification de mini-sprints d'une semaine avec objectifs clairs et mesurables
    \item \textbf{Suivi via Microsoft Teams} : Utilisation du canal Teams pour documenter les progrès quotidiens et les blocages
    \item \textbf{Milestones orientés fonctionnalités} : Gestion par jalons majeurs correspondant aux releases et fonctionnalités clés
    \item \textbf{Réunions hebdomadaires V1} : Réflexion hebdomadaire (réunion hebodomadaire) pour évaluer les progrès et ajuster la stratégie
    \item \textbf{Livraisons incrémentales} : Chaque milestone produit une version fonctionnelle avec nouvelles capacités
\end{itemize}

\subsection{Planification par milestones}

Le projet est organisé en milestones majeurs avec des objectifs fonctionnels spécifiques :

\textbf{Milestones réalisés (Phase 1 - Mi-parcours) :}
\begin{itemize}
    \item \textbf{M1 - Architecture Foundation} : Setup initial (Angular 19, FastAPI, base de données Oracle) - ✅ Terminé
    \item \textbf{M2 - Core Dashboard} : Développement du dashboard et des composants de base - ✅ Terminé
    \item \textbf{M3 - Zabbix Integration} : Intégration Zabbix et collecte de métriques - ✅ Terminé
    \item \textbf{M4 - Capacity Analysis} : Système de comparaison et analyse de capacité - ✅ Terminé
    \item \textbf{M5 - MAIA CMDB Integration} : Intégration MAIA CMDB et gestion des configurations clients - ✅ Terminé
    \item \textbf{M6 - Reporting Module} : Module de reporting et export PowerPoint - ✅ Terminé
\end{itemize}

\textbf{Milestones planifiés (Phase 2 - Finalisation) :}
\begin{itemize}
    \item \textbf{M7 - DevOps Pipeline} : Containerisation Docker et pipeline GitLab CI/CD - 🔄 En cours
    \item \textbf{M8 - Production Deployment} : Déploiement OpenShift et configuration des environnements - 📋 Planifié
    \item \textbf{M9 - AI Integration} : Intégration intelligence artificielle avec CatBoost - 📋 Planifié
    \item \textbf{M10 - Project Finalization} : Tests de performance, optimisation et documentation finale - 📋 Planifié
\end{itemize}

\subsection{Processus hebdomadaire}

La méthodologie Agile Hybrid s'appuie sur un processus hebdomadaire structuré :

\subsubsection{Planification hebdomadaire}
\begin{itemize}
    \item \textbf{Définition des objectifs} : Chaque lundi, définition de 3-5 objectifs spécifiques pour la semaine
    \item \textbf{Priorisation} : Classement des tâches par impact métier et dépendances techniques
    \item \textbf{Estimation} : Évaluation réaliste de la charge de travail et des délais
    \item \textbf{Documentation} : Enregistrement des objectifs dans le canal Teams dédié
\end{itemize}

\subsubsection{Suivi quotidien via Microsoft Teams}
\begin{itemize}
    \item \textbf{Canal dédié} : Utilisation d'un canal Teams spécifique pour le projet \projectTitle
    \item \textbf{Updates quotidiens} : Publication des progrès, blocages et questions
    \item \textbf{Documentation continue} : Partage de captures d'écran, code snippets et résultats
    \item \textbf{Collaboration} : Échanges avec les superviseurs et parties prenantes
\end{itemize}

\subsubsection{Réunions hebdomadaires V1 (Réflexion)}
\begin{itemize}
    \item \textbf{Bilan de la semaine} : Évaluation des objectifs atteints vs planifiés
    \item \textbf{Identification des blocages} : Analyse des difficultés rencontrées et solutions
    \item \textbf{Ajustements} : Modification de l'approche ou des priorités si nécessaire
    \item \textbf{Planification suivante} : Préparation des objectifs de la semaine suivante
    \item \textbf{Amélioration continue} : Réflexion sur l'efficacité du processus et optimisations
\end{itemize}

\subsection{Approche technique}

La méthodologie technique combine :

\begin{itemize}
    \item \textbf{Analyse des besoins} : Étude approfondie des processus existants et identification des exigences fonctionnelles et techniques
    \item \textbf{Conception architecturale} : Définition d'une architecture moderne basée sur Angular 19 (frontend) et FastAPI (backend)
    \item \textbf{Développement itératif} : Implémentation progressive des fonctionnalités avec validation continue et tests automatisés
    \item \textbf{Intégration native} : Exploitation de l'infrastructure existante (Zabbix, Oracle, GitLab) pour minimiser les impacts
    \item \textbf{Déploiement moderne} : Containerisation Docker et déploiement sur OpenShift avec pipeline CI/CD
    \item \textbf{Préparation IA} : Architecture préparée pour l'intégration future d'algorithmes CatBoost pour l'analyse prédictive
\end{itemize}

\clearpage
