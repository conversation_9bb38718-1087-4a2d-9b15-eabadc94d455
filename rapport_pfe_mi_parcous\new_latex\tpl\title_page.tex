%========= Title Page Template for CloudCapacity Report =========%
%                                                                 %
% Compact, professional title page based on academic standards   %
% Uses variables from global_config.tex for easy customization   %
%=================================================================%

\begin{titlepage}
    \thispagestyle{empty}

    % En-tête avec logos et informations république
    \begin{minipage}[t]{0.25\textwidth}
        \includegraphics[width=\textwidth]{\universityLogo}
    \end{minipage}
    \hfill
    \begin{minipage}[t]{0.45\textwidth}
        \centering
        {\small\textbf{\countryName}\\
        \ministryName\\[0.3cm]
        \textbf{\universityName}\\
        \universityDescription}
    \end{minipage}
    \hfill
    \begin{minipage}[t]{0.25\textwidth}
        \raggedleft
        % Company information
        {\small\textbf{\companyName}\\
        \companyDivision}
    \end{minipage}

    \vspace{1.5cm}

    % Titre principal - Enhanced typography for better readability
    \begin{center}
        {\fontsize{18}{22}\selectfont\textbf{RAPPORT DE PROJET DE FIN D'ÉTUDES}}\\[0.5cm]
        {\large Présenté en vue de l'obtention du diplôme d'\diplomaTitle}\\[0.5cm]
        {\normalsize Spécialité : \textbf{\studentSpecialty}}
    \end{center}

    \vspace{1cm}

    % Étudiant
    \begin{center}
        {\large Réalisé par :}\\[0.3cm]
        {\Large\textbf{\studentName}}
    \end{center}

    \vspace{1cm}

    % Titre du projet dans un cadre coloré
    \begin{center}
        \fcolorbox{primaryBlue}{lightBlue}{
            \begin{minipage}{0.8\textwidth}
                \centering
                {\LARGE\textbf{\textcolor{primaryBlue}{\projectTitle}}}\\[0.3cm]
                {\large \projectSubtitle}
            \end{minipage}
        }
    \end{center}

    \vspace{1cm}

    % Encadrants dans un tableau
    \begin{center}
        \begin{tabular}{|p{7cm}|p{7cm}|}
            \hline
            \textbf{Encadrant Académique} & \textbf{Encadrants Professionnels} \\
            \hline
            \academicSupervisor & \professionalSupervisor \\
            \academicSupervisorTitle & \professionalSupervisorTitle \\
            & \companyName \\
            & \\
            & \professionalSupervisorSecond \\
            & \professionalSupervisorSecondTitle \\
            & \companyName \\
            \hline
        \end{tabular}
    \end{center}

    \vspace{1cm}

    % Entreprise d'accueil
    \begin{center}
        {\large\textbf{Entreprise d'accueil :}}\\[0.3cm]
        {\Large\textbf{\companyName}}\\
        {\normalsize \companyDivision}
    \end{center}

    \vspace{1cm}

    % Type de rapport et année
    \begin{center}
        {\large\textbf{\reportType}}\\[0.3cm]
        {\normalsize Année Universitaire \academicYear}\\
        {\normalsize \submissionDate}
    \end{center}

\end{titlepage}
