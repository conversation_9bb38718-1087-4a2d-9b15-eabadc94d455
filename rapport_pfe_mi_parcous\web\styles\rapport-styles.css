/* Styles pour le Rapport PFE CloudCapacity */

body {
    font-family: 'Latin Modern Roman', 'Times New Roman', serif;
    line-height: 1.6;
    color: #333;
    max-width: 210mm;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
}

.page {
    padding: 20px;
    margin-bottom: 20px;
}

h1,
h2,
h3,
h4,
h5 {
    font-family: 'Latin Modern Sans', Arial, sans-serif;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

h1 {
    font-size: 2rem;
}

h2 {
    font-size: 1.5rem;
}

h3 {
    font-size: 1.25rem;
}

h4 {
    font-size: 1.1rem;
}

p,
ul,
ol {
    margin-bottom: 1rem;
}

.toc {
    margin: 2rem 0;
    padding: 1rem;
    background-color: #f9f9f9;
    border-left: 4px solid #0066cc;
}

.toc-item {
    margin-left: 1rem;
}

.toc-subsection {
    margin-left: 2rem;
}

.toc-subsubsection {
    margin-left: 3rem;
}

.latex-section {
    margin-top: 2rem;
}

.titlepage {
    height: 95vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.titlepage h1 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
}

.titlepage .subtitle {
    font-size: 1.8rem;
    margin-bottom: 4rem;
}

.titlepage .author {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.titlepage .date {
    font-size: 1.1rem;
    margin-top: 4rem;
}

/* Nouvelles classes pour la page de titre */
.titlepage-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
}

.logo-container {
    text-align: left;
}

.logo {
    height: 80px;
}

.republic-info {
    text-align: right;
    font-size: 0.9rem;
}

.author-section {
    margin: 3rem 0;
}

.diploma-section {
    margin: 2rem 0;
}

.company-section {
    margin-top: 3rem;
    display: flex;
    justify-content: center;
    align-items: center;
}

.company-info {
    text-align: center;
}

.company-title {
    font-weight: bold;
    margin-bottom: 1rem;
}

/* Styles améliorés pour la page de titre académique */
.project-title {
    margin: 3rem 0;
    text-align: center;
}

.project-title h2 {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.project-title h3 {
    font-size: 1.5rem;
    font-weight: normal;
    color: #34495e;
    font-style: italic;
    line-height: 1.4;
}

.author-label {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.author-name {
    font-size: 1.4rem;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 0.5rem;
}

.student-id {
    font-size: 1rem;
    color: #7f8c8d;
    font-style: italic;
}

.supervision-section {
    margin: 3rem 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.supervision-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.supervisor {
    margin: 1rem 0;
    text-align: center;
    padding: 1rem;
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #f8f9fa;
    min-width: 300px;
}

.supervisor strong {
    color: #2c3e50;
    font-size: 1.1rem;
}

.supervisor em {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.diploma-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.diploma-name {
    font-size: 1.6rem;
    font-weight: bold;
    color: #27ae60;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.speciality {
    font-size: 1.2rem;
    color: #34495e;
    font-weight: 500;
}

.academic-year {
    margin-top: 3rem;
    text-align: center;
}

.year {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.report-type {
    font-size: 1.3rem;
    font-weight: bold;
    color: #e74c3c;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Styles pour les dédicaces améliorées */
.dedication-content {
    text-align: center;
    margin: 2rem 0;
}

.dedication-text {
    margin: 2rem 0;
    font-size: 1.1rem;
    line-height: 1.6;
    font-style: italic;
    color: #2c3e50;
}

.dedication-text:first-line {
    font-weight: bold;
    font-style: normal;
}

/* Styles pour les remerciements améliorés */
.acknowledgments-content {
    text-align: justify;
    line-height: 1.8;
    margin: 1rem 0;
}

.acknowledgments-content p {
    margin-bottom: 1.5rem;
    text-indent: 2rem;
}

.acknowledgments-content strong {
    color: #2c3e50;
    font-weight: 600;
}

/* Styles pour les résumés structurés */
.abstract-content {
    text-align: justify;
    line-height: 1.7;
    margin: 1rem 0;
}

.abstract-content p {
    margin-bottom: 1.2rem;
    text-indent: 0;
}

.abstract-content strong {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.05rem;
}

/* Styles pour les mots-clés */
.abstract-content p:last-child {
    background-color: #f8f9fa;
    padding: 1rem;
    border-left: 4px solid #3498db;
    border-radius: 4px;
    font-style: italic;
}

/* Amélioration des titres de sections */
.dedication h2,
.acknowledgements h2,
.abstract h2 {
    text-align: center;
    color: #2c3e50;
    font-size: 2rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 1rem;
}

/* Responsive design pour les nouvelles sections */
@media (max-width: 768px) {
    .project-title h2 {
        font-size: 2rem;
    }

    .project-title h3 {
        font-size: 1.2rem;
    }

    .supervisor {
        min-width: 250px;
        margin: 0.5rem 0;
    }

    .abstract-content p {
        text-indent: 1rem;
    }

    .acknowledgments-content p {
        text-indent: 1rem;
    }
}

/* Styles pour l'introduction structurée */
.introduction-section {
    margin: 2rem 0;
}

.introduction-section h3 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-left: 4px solid #3498db;
    padding-left: 1rem;
    background-color: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 4px;
}

.introduction-section p {
    text-align: justify;
    line-height: 1.7;
    margin-bottom: 1rem;
    text-indent: 2rem;
}

.objectives-list {
    margin: 1rem 0;
    padding-left: 2rem;
}

.objectives-list li {
    margin-bottom: 0.8rem;
    line-height: 1.6;
    color: #2c3e50;
    position: relative;
}

.objectives-list li::before {
    content: "▶";
    color: #3498db;
    font-weight: bold;
    position: absolute;
    left: -1.5rem;
}

/* Amélioration des titres de chapitres */
.latex-section h2 {
    color: #2c3e50;
    font-size: 2.2rem;
    font-weight: bold;
    margin-bottom: 2rem;
    text-align: center;
    border-bottom: 3px solid #3498db;
    padding-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.latex-section h3 {
    color: #34495e;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem 0;
    border-left: 3px solid #e74c3c;
    padding-left: 1rem;
}

.latex-section h4 {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 500;
    margin: 1rem 0 0.5rem 0;
    font-style: italic;
}

/* Styles pour les paragraphes académiques */
.latex-section p {
    text-align: justify;
    line-height: 1.7;
    margin-bottom: 1rem;
    text-indent: 2rem;
    color: #2c3e50;
}

/* Styles pour les listes dans les sections */
.latex-section ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.latex-section li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Styles pour les tableaux académiques */
.table-data {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    font-size: 0.95rem;
}

.table-data th {
    background-color: #34495e;
    color: white;
    padding: 0.8rem;
    text-align: left;
    font-weight: 600;
}

.table-data td {
    padding: 0.8rem;
    border-bottom: 1px solid #bdc3c7;
}

.table-data tr:nth-child(even) {
    background-color: #f8f9fa;
}

.table-data tr:hover {
    background-color: #e8f4fd;
}

.dedication,
.acknowledgements {
    font-style: italic;
    text-align: center;
    margin: 4rem 0;
}

.abstract {
    margin: 2rem 0;
    padding: 1rem;
    font-style: italic;
    background-color: #f9f9f9;
}

.table-data {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
}

.table-data th,
.table-data td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.table-data th {
    background-color: #f2f2f2;
}

.mermaid {
    margin: 20px 0;
}

.diagram-container {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
}

.figure {
    margin: 20px 0;
    text-align: center;
}

.figure-caption {
    font-style: italic;
    text-align: center;
    margin-top: 10px;
}

.code {
    font-family: 'Courier New', Courier, monospace;
    background-color: #f6f8fa;
    padding: 10px;
    border-radius: 5px;
    border-left: 3px solid #0066cc;
    overflow-x: auto;
    margin: 10px 0;
}

/* Styles spécifiques pour les captures d'écran */
.screenshot {
    max-width: 100%;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 10px 0;
}

/* Styles pour les tableaux de résultats */
.results-table {
    border-collapse: collapse;
    width: 100%;
    margin: 1rem 0;
    font-size: 0.9rem;
}

.results-table th,
.results-table td {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: center;
}

.results-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

/* Styles pour les métriques de performance */
.performance-metric {
    background-color: #e8f4fd;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.performance-metric .metric-title {
    font-weight: bold;
    color: #0066cc;
}

/* Styles pour les sections de code */
.code-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    margin: 15px 0;
}

.code-section .code-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #495057;
}

/* Styles pour les alertes et notifications */
.alert {
    padding: 12px;
    border-radius: 5px;
    margin: 10px 0;
}

.alert-info {
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

/* Styles pour les listes de fonctionnalités */
.feature-list {
    list-style: none;
    padding-left: 0;
}

.feature-list li {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.feature-list li:before {
    content: "✓ ";
    color: #28a745;
    font-weight: bold;
    margin-right: 5px;
}

/* Styles pour les sections de statut */
.status-implemented {
    color: #28a745;
    font-weight: bold;
}

.status-in-progress {
    color: #ffc107;
    font-weight: bold;
}

.status-planned {
    color: #6c757d;
    font-weight: bold;
}